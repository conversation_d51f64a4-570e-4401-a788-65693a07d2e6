# Cargo.toml - Chatterbox TTS Rust/WASM Implementation
# MCStack v9r0 Compliant Configuration

[package]
name = "chatterbox-tts"
version = "0.2.0"
edition = "2021"
authors = ["Resemble AI Engineering <<EMAIL>>"]
license = "MIT"
description = "Production-grade TTS system with formal verification and WASM support"
homepage = "https://github.com/resemble-ai/chatterbox-rust"
repository = "https://github.com/resemble-ai/chatterbox-rust"
documentation = "https://docs.rs/chatterbox-tts"
readme = "README.md"
keywords = ["tts", "speech", "ai", "wasm", "verification"]
categories = ["multimedia::audio", "science", "web-programming::wasm"]

[lib]
crate-type = ["cdylib", "rlib"]

# MCStack v9r0 Security & Safety Features
[dependencies]
# Core neural network inference
candle-core = { version = "0.7.0", features = ["cuda", "metal"] }
candle-nn = "0.7.0"
candle-transformers = "0.7.0"

# Audio processing with SIMD optimization
rustfft = "6.2"
apodize = "1.0"
realfft = "3.3"

# Async runtime for real-time processing
tokio = { version = "1.40", features = ["macros", "rt", "rt-multi-thread", "time"] }
tokio-stream = "0.1"
futures = "0.3"

# Serialization and data handling
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
bincode = "1.3"

# Cryptography for watermarking (Perth implementation)
aes-gcm = "0.10"
rand = "0.8"
sha3 = "0.10"
ed25519-dalek = "2.1"
zeroize = { version = "1.8", features = ["derive"] }

# Memory safety and bounds checking
bounded-vec = "0.7"
smallvec = "1.13"

# Error handling
thiserror = "1.0"
anyhow = "1.0"

# Logging and telemetry (MCStack compliance)
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
metrics = "0.21"
opentelemetry = { version = "0.20", features = ["rt-tokio"] }

# WASM-specific dependencies
[target.'cfg(target_arch = "wasm32")'.dependencies]
wasm-bindgen = "0.2"
wasm-bindgen-futures = "0.4"
js-sys = "0.3"
web-sys = { version = "0.3", features = [
    "console",
    "AudioContext",
    "AudioBuffer",
    "AudioDestinationNode",
    "Performance",
    "Window",
]}
getrandom = { version = "0.2", features = ["js"] }

# Development and testing dependencies
[dev-dependencies]
tokio-test = "0.4"
criterion = { version = "0.5", features = ["html_reports"] }
proptest = "1.4"
quickcheck = "1.0"
quickcheck_macros = "1.0"

# Formal verification (optional)
[dependencies.prusti-contracts]
version = "0.2.0"
optional = true

[dependencies.creusot-contracts]
version = "0.2.0"
optional = true

# Feature flags for different build configurations
[features]
default = ["audio-processing", "watermarking"]

# Core features
audio-processing = []
neural-models = ["candle-core", "candle-nn", "candle-transformers"]
watermarking = ["aes-gcm", "ed25519-dalek", "zeroize"]

# Formal verification
verification = ["prusti-contracts", "creusot-contracts"]
verification-prusti = ["prusti-contracts"]
verification-creusot = ["creusot-contracts"]

# Platform-specific optimizations
simd = ["rustfft/avx"]
gpu-cuda = ["candle-core/cuda"]
gpu-metal = ["candle-core/metal"]

# WASM build
wasm = ["wasm-bindgen", "js-sys", "web-sys", "getrandom/js"]
wasm-simd = ["wasm", "simd"]

# Security and compliance
security-hardened = ["zeroize", "aes-gcm"]
mcstack-compliance = ["tracing", "metrics", "opentelemetry"]

# Development and debugging
dev-tools = ["criterion"]
profiling = ["tokio/tracing"]

# Benchmarking profiles
[[bench]]
name = "synthesis_benchmark"
harness = false
required-features = ["dev-tools"]

[[bench]]
name = "memory_benchmark"
harness = false
required-features = ["dev-tools"]

# Example binaries
[[example]]
name = "basic_synthesis"
required-features = ["audio-processing"]

[[example]]
name = "voice_conversion"
required-features = ["audio-processing", "neural-models"]

[[example]]
name = "wasm_demo"
required-features = ["wasm"]
crate-type = ["cdylib"]

# Build optimization profiles
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

# WASM-optimized release build
[profile.release-wasm]
inherits = "release"
opt-level = "s"  # Optimize for size
lto = "fat"

# Development profile with verification
[profile.dev-verified]
inherits = "dev"
debug = true
overflow-checks = true
debug-assertions = true

# MCStack v9r0 Compliance Metadata
[package.metadata.mcstack]
version = "v9r0_enhanced"
governance_level = "GAL-3"
safety_critical = true
formal_verification = true
security_level = "high"
compliance_frameworks = ["NIST-AI-RMF", "EU-AI-Act", "ISO-27001"]

[package.metadata.mcstack.security]
supply_chain_verification = true
sbom_generation = true
cryptographic_signing = true
watermarking = "perth"

[package.metadata.mcstack.verification]
formal_methods = ["prusti", "creusot"]
property_testing = true
memory_safety = true
real_time_guarantees = true

[package.metadata.mcstack.governance]
audit_trail = true
compliance_monitoring = true
automated_testing = true
documentation_required = true

# Documentation configuration
[package.metadata.docs.rs]
all-features = true
rustdoc-args = ["--cfg", "docsrs"]

# Publishing configuration
[package.metadata.release]
sign-commit = true
sign-tag = true
pre-release-replacements = [
    { file = "CHANGELOG.md", search = "Unreleased", replace = "{{version}}" },
    { file = "README.md", search = "chatterbox-tts = \".*\"", replace = "chatterbox-tts = \"{{version}}\"" },
]

# Workspace configuration for mono-repo setup
[workspace]
members = [
    "crates/chatterbox-core",
    "crates/chatterbox-models",
    "crates/chatterbox-audio",
    "crates/chatterbox-verification", 
    "crates/chatterbox-wasm",
    "examples/web-demo",
    "examples/cli-tool",
]

[workspace.dependencies]
# Shared dependencies across workspace
chatterbox-core = { path = "crates/chatterbox-core" }
chatterbox-models = { path = "crates/chatterbox-models" }
chatterbox-audio = { path = "crates/chatterbox-audio" }
chatterbox-verification = { path = "crates/chatterbox-verification" }

# Linting and code quality
[workspace.lints.rust]
unsafe_code = "forbid"
missing_docs = "warn"
rust_2018_idioms = "warn"
trivial_casts = "warn"
trivial_numeric_casts = "warn"
unused_import_braces = "warn"
unused_qualifications = "warn"

[workspace.lints.clippy]
all = "warn"
pedantic = "warn"
nursery = "warn"
cargo = "warn"
# Allow some clippy lints that are too strict for ML code
float_cmp = "allow"
cast_precision_loss = "allow"
cast_sign_loss = "allow"
cast_possible_truncation = "allow"