{% extends "base.html" %}

{% block title %}Dashboard - Chatterbox Enterprise{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">
        <i class="fas fa-tachometer-alt me-2 text-primary"></i>
        System Dashboard
    </h1>
    <div class="d-flex gap-2">
        <button class="btn btn-outline-primary btn-sm" onclick="refreshMetrics()">
            <i class="fas fa-sync-alt me-1"></i> Refresh
        </button>
        <button class="btn btn-outline-secondary btn-sm" onclick="exportMetrics()">
            <i class="fas fa-download me-1"></i> Export
        </button>
    </div>
</div>

<!-- System Metrics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card metric-card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="metric-value" id="cpu-usage">{{ metrics.cpu_usage|round(1) }}%</div>
                        <div class="metric-label">CPU Usage</div>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-microchip fa-2x"></i>
                    </div>
                </div>
                <div class="progress progress-custom mt-2">
                    <div class="progress-bar bg-white" style="width: {{ metrics.cpu_usage }}%"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card metric-card" style="background: linear-gradient(135deg, #059669, #10b981);">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="metric-value" id="memory-usage">{{ metrics.memory_usage|round(1) }}%</div>
                        <div class="metric-label">Memory Usage</div>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-memory fa-2x"></i>
                    </div>
                </div>
                <div class="progress progress-custom mt-2">
                    <div class="progress-bar bg-white" style="width: {{ metrics.memory_usage }}%"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card metric-card" style="background: linear-gradient(135deg, #7c3aed, #8b5cf6);">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="metric-value" id="gpu-usage">{{ metrics.gpu_usage|round(1) }}%</div>
                        <div class="metric-label">GPU Usage</div>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-tv fa-2x"></i>
                    </div>
                </div>
                <div class="progress progress-custom mt-2">
                    <div class="progress-bar bg-white" style="width: {{ metrics.gpu_usage }}%"></div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-3">
        <div class="card metric-card" style="background: linear-gradient(135deg, #dc2626, #ef4444);">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="metric-value" id="requests-per-second">{{ metrics.requests_per_second|round(1) }}</div>
                        <div class="metric-label">Requests/sec</div>
                    </div>
                    <div class="text-white-50">
                        <i class="fas fa-bolt fa-2x"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-white-75">
                        <i class="fas fa-users me-1"></i>
                        {{ metrics.active_connections }} active connections
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics -->
<div class="row mb-4">
    <div class="col-lg-8 mb-3">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Performance Metrics
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <input type="radio" class="btn-check" name="timeRange" id="range1h" checked>
                    <label class="btn btn-outline-primary" for="range1h">1H</label>
                    
                    <input type="radio" class="btn-check" name="timeRange" id="range24h">
                    <label class="btn btn-outline-primary" for="range24h">24H</label>
                    
                    <input type="radio" class="btn-check" name="timeRange" id="range7d">
                    <label class="btn btn-outline-primary" for="range7d">7D</label>
                </div>
            </div>
            <div class="card-body">
                <canvas id="performanceChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-brain me-2"></i>
                    Loaded Models
                </h5>
            </div>
            <div class="card-body">
                {% for model in models %}
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <div class="fw-bold">{{ model.name }}</div>
                        <small class="text-muted">{{ model.model_type }} • {{ model.language }}</small>
                    </div>
                    <div class="text-end">
                        <span class="status-indicator status-online"></span>
                        <small class="text-muted">{{ model.memory_usage_mb }}MB</small>
                    </div>
                </div>
                {% endfor %}
                
                <div class="mt-3 pt-3 border-top">
                    <div class="d-flex justify-content-between">
                        <span>Total Models:</span>
                        <strong>{{ models|length }}</strong>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Memory Usage:</span>
                        <strong>{{ models|map(attribute='memory_usage_mb')|sum }}MB</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity and System Status -->
<div class="row">
    <div class="col-lg-8 mb-3">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    Recent Requests
                </h5>
                <button class="btn btn-outline-primary btn-sm" onclick="clearRequestLog()">
                    <i class="fas fa-trash me-1"></i> Clear
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Duration</th>
                                <th>User</th>
                            </tr>
                        </thead>
                        <tbody id="request-log">
                            {% for request in recent_requests %}
                            <tr>
                                <td>{{ request.timestamp }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ request.request_type }}</span>
                                </td>
                                <td>
                                    {% if request.status == "success" %}
                                    <span class="badge bg-success">Success</span>
                                    {% else %}
                                    <span class="badge bg-danger">{{ request.status }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ request.duration_ms }}ms</td>
                                <td>{{ request.user_id|default("Anonymous") }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4 mb-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server me-2"></i>
                    System Status
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>API Server</span>
                        <span class="status-indicator status-online"></span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Database</span>
                        <span class="status-indicator status-online"></span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Redis Cache</span>
                        <span class="status-indicator status-online"></span>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>GPU Acceleration</span>
                        <span class="status-indicator status-online"></span>
                    </div>
                </div>

                <hr>

                <div class="text-center">
                    <div class="h4 text-success mb-1">{{ metrics.uptime_hours|round(1) }}h</div>
                    <small class="text-muted">System Uptime</small>
                </div>

                <div class="mt-3">
                    <div class="d-flex justify-content-between">
                        <span>Error Rate:</span>
                        <span class="text-{% if metrics.error_rate < 0.01 %}success{% elif metrics.error_rate < 0.05 %}warning{% else %}danger{% endif %}">
                            {{ (metrics.error_rate * 100)|round(2) }}%
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Performance chart
    const ctx = document.getElementById('performanceChart').getContext('2d');
    const performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['10:00', '10:05', '10:10', '10:15', '10:20', '10:25', '10:30'],
            datasets: [{
                label: 'CPU Usage (%)',
                data: [45, 52, 48, 61, 55, 47, 52],
                borderColor: '#2563eb',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                tension: 0.4
            }, {
                label: 'Memory Usage (%)',
                data: [67, 69, 71, 68, 70, 72, 69],
                borderColor: '#059669',
                backgroundColor: 'rgba(5, 150, 105, 0.1)',
                tension: 0.4
            }, {
                label: 'Requests/sec',
                data: [15, 18, 22, 19, 25, 21, 17],
                borderColor: '#dc2626',
                backgroundColor: 'rgba(220, 38, 38, 0.1)',
                tension: 0.4,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    max: 100
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                }
            }
        }
    });

    // Update metrics from WebSocket
    function updateMetrics(metrics) {
        document.getElementById('cpu-usage').textContent = metrics.cpu_usage.toFixed(1) + '%';
        document.getElementById('memory-usage').textContent = metrics.memory_usage.toFixed(1) + '%';
        document.getElementById('gpu-usage').textContent = metrics.gpu_usage.toFixed(1) + '%';
        document.getElementById('requests-per-second').textContent = metrics.requests_per_second.toFixed(1);
    }

    // Refresh metrics manually
    function refreshMetrics() {
        fetch('/api/metrics')
            .then(response => response.json())
            .then(data => {
                updateMetrics(data);
                showToast('Metrics refreshed', 'success');
            })
            .catch(error => {
                showToast('Failed to refresh metrics', 'danger');
            });
    }

    // Export metrics
    function exportMetrics() {
        showToast('Exporting metrics...', 'info');
        // Implementation for exporting metrics
    }

    // Clear request log
    function clearRequestLog() {
        document.getElementById('request-log').innerHTML = '';
        showToast('Request log cleared', 'success');
    }

    // Auto-refresh every 30 seconds
    setInterval(refreshMetrics, 30000);
</script>
{% endblock %}
