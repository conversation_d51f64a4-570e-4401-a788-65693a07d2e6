use chatterbox_enterprise::{
    config::Config,
    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},
    audio::{AudioData, SampleRate},
    models::ModelManager,
    security::WatermarkEngine,
    telemetry::TelemetryCollector,
    Result,
};
use std::sync::Arc;
use tokio;

#[tokio::test]
async fn test_engine_initialization() -> Result<()> {
    // Create a test configuration
    let config = Config::default();
    
    // Initialize components
    let model_manager = Arc::new(ModelManager::new(config.clone()).await?);
    let watermark_engine = Arc::new(WatermarkEngine::new(&config)?);
    let telemetry = Arc::new(TelemetryCollector::new(&config)?);
    
    // Create the engine
    let engine = ChatterboxEngine::new(
        config,
        model_manager,
        watermark_engine,
        telemetry,
    ).await?;
    
    // Verify engine is healthy
    let health = engine.health_check().await?;
    assert!(health.is_healthy);
    
    Ok(())
}

#[tokio::test]
async fn test_tts_synthesis() -> Result<()> {
    let config = Config::default();
    let model_manager = Arc::new(ModelManager::new(config.clone()).await?);
    let watermark_engine = Arc::new(WatermarkEngine::new(&config)?);
    let telemetry = Arc::new(TelemetryCollector::new(&config)?);
    
    let engine = ChatterboxEngine::new(
        config,
        model_manager,
        watermark_engine,
        telemetry,
    ).await?;
    
    // Test synthesis
    let options = SynthesisOptions {
        voice_model: "test-voice".to_string(),
        speaking_rate: 1.0,
        pitch: 0.0,
        volume: 1.0,
        emotion: Some("neutral".to_string()),
        apply_watermark: true,
    };
    
    let result = engine.synthesize_speech("Hello, world!", &options).await?;
    
    // Verify the result
    assert!(!result.audio.samples().is_empty());
    assert_eq!(result.audio.sample_rate(), SampleRate::Hz22050);
    assert!(result.metadata.is_some());
    
    Ok(())
}

#[tokio::test]
async fn test_stt_recognition() -> Result<()> {
    let config = Config::default();
    let model_manager = Arc::new(ModelManager::new(config.clone()).await?);
    let watermark_engine = Arc::new(WatermarkEngine::new(&config)?);
    let telemetry = Arc::new(TelemetryCollector::new(&config)?);
    
    let engine = ChatterboxEngine::new(
        config,
        model_manager,
        watermark_engine,
        telemetry,
    ).await?;
    
    // Create test audio data (silence for testing)
    let samples = vec![0.0f32; 22050]; // 1 second of silence
    let audio = AudioData::new(samples, SampleRate::Hz22050);
    
    let options = RecognitionOptions {
        language: Some("en-US".to_string()),
        punctuation: true,
        confidence_threshold: 0.5,
        enable_word_timestamps: false,
    };
    
    let result = engine.recognize_speech(&audio, &options).await?;
    
    // For silence, we expect empty or minimal text
    assert!(result.text.len() <= 10); // Should be empty or very short
    
    Ok(())
}

#[tokio::test]
async fn test_watermark_verification() -> Result<()> {
    let config = Config::default();
    let watermark_engine = WatermarkEngine::new(&config)?;
    
    // Create test audio
    let samples = vec![0.1f32; 22050]; // 1 second of test audio
    let mut audio = AudioData::new(samples, SampleRate::Hz22050);
    
    // Apply watermark
    watermark_engine.apply_watermark(&mut audio, "test-content")?;
    
    // Verify watermark
    let verification = watermark_engine.verify_watermark(&audio)?;
    assert!(verification.is_authentic);
    
    Ok(())
}

#[tokio::test]
async fn test_model_loading() -> Result<()> {
    let config = Config::default();
    let model_manager = ModelManager::new(config).await?;
    
    // Test model loading (this will use mock models in test environment)
    let models = model_manager.list_available_models().await?;
    assert!(!models.is_empty());
    
    // Test model status
    let status = model_manager.get_model_status().await?;
    assert!(status.total_models > 0);
    
    Ok(())
}

#[tokio::test]
async fn test_telemetry_collection() -> Result<()> {
    let config = Config::default();
    let telemetry = TelemetryCollector::new(&config)?;
    
    // Record some test metrics
    telemetry.record_request("test");
    telemetry.record_synthesis(
        std::time::Duration::from_millis(100),
        1.0,
        "test-voice"
    );
    
    // Test health metrics
    let health = telemetry.get_health_metrics().await?;
    assert!(health.uptime_seconds > 0.0);
    
    Ok(())
}

#[tokio::test]
async fn test_error_handling() -> Result<()> {
    let config = Config::default();
    let model_manager = Arc::new(ModelManager::new(config.clone()).await?);
    let watermark_engine = Arc::new(WatermarkEngine::new(&config)?);
    let telemetry = Arc::new(TelemetryCollector::new(&config)?);
    
    let engine = ChatterboxEngine::new(
        config,
        model_manager,
        watermark_engine,
        telemetry,
    ).await?;
    
    // Test with invalid input
    let options = SynthesisOptions {
        voice_model: "non-existent-voice".to_string(),
        speaking_rate: 1.0,
        pitch: 0.0,
        volume: 1.0,
        emotion: None,
        apply_watermark: false,
    };
    
    // This should return an error for non-existent voice
    let result = engine.synthesize_speech("Test", &options).await;
    assert!(result.is_err());
    
    Ok(())
}

#[tokio::test]
async fn test_concurrent_requests() -> Result<()> {
    let config = Config::default();
    let model_manager = Arc::new(ModelManager::new(config.clone()).await?);
    let watermark_engine = Arc::new(WatermarkEngine::new(&config)?);
    let telemetry = Arc::new(TelemetryCollector::new(&config)?);
    
    let engine = Arc::new(ChatterboxEngine::new(
        config,
        model_manager,
        watermark_engine,
        telemetry,
    ).await?);
    
    // Test concurrent synthesis requests
    let mut handles = vec![];
    
    for i in 0..5 {
        let engine_clone = engine.clone();
        let handle = tokio::spawn(async move {
            let options = SynthesisOptions {
                voice_model: "test-voice".to_string(),
                speaking_rate: 1.0,
                pitch: 0.0,
                volume: 1.0,
                emotion: None,
                apply_watermark: false,
            };
            
            engine_clone.synthesize_speech(
                &format!("Test message {}", i),
                &options
            ).await
        });
        handles.push(handle);
    }
    
    // Wait for all requests to complete
    for handle in handles {
        let result = handle.await.unwrap();
        // In test environment, this might fail due to mock models
        // but we're testing that the system doesn't crash
        let _ = result;
    }
    
    Ok(())
}

#[cfg(feature = "benchmark")]
mod benchmarks {
    use super::*;
    use std::time::Instant;
    
    #[tokio::test]
    async fn benchmark_synthesis_performance() -> Result<()> {
        let config = Config::default();
        let model_manager = Arc::new(ModelManager::new(config.clone()).await?);
        let watermark_engine = Arc::new(WatermarkEngine::new(&config)?);
        let telemetry = Arc::new(TelemetryCollector::new(&config)?);
        
        let engine = ChatterboxEngine::new(
            config,
            model_manager,
            watermark_engine,
            telemetry,
        ).await?;
        
        let options = SynthesisOptions {
            voice_model: "test-voice".to_string(),
            speaking_rate: 1.0,
            pitch: 0.0,
            volume: 1.0,
            emotion: None,
            apply_watermark: false,
        };
        
        let text = "This is a benchmark test for speech synthesis performance.";
        let iterations = 10;
        
        let start = Instant::now();
        for _ in 0..iterations {
            let _ = engine.synthesize_speech(text, &options).await;
        }
        let duration = start.elapsed();
        
        println!("Average synthesis time: {:?}", duration / iterations);
        
        Ok(())
    }
}
