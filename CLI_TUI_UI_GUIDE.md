# 🖥️ Chatterbox Enterprise - CLI, TUI & Web UI Integration Guide

## 📋 Overview

Chatterbox Enterprise provides **three comprehensive user interfaces** for different use cases and preferences:

1. **🔧 CLI (Command Line Interface)** - Professional command-line tool for automation and scripting
2. **📺 TUI (Terminal User Interface)** - Interactive terminal dashboard for real-time monitoring
3. **🌐 Web UI** - Modern web interface for comprehensive management and visualization

---

## 🔧 CLI (Command Line Interface)

### Installation & Setup

```bash
# Build all binaries
cargo build --release

# CLI binary location
./target/release/chatterbox-cli

# Add to PATH (optional)
export PATH=$PATH:$(pwd)/target/release
```

### Basic Usage

```bash
# Show help
chatterbox-cli --help

# Text-to-Speech synthesis
chatterbox-cli tts --text "Hello, world!" --voice "en-us-female-1" --output "hello.wav"

# Speech-to-Text recognition
chatterbox-cli stt --input "audio.wav" --language "en-US" --output "transcript.txt"

# List available models
chatterbox-cli models --filter "all" --detailed

# System status
chatterbox-cli status --detailed

# Interactive mode
chatterbox-cli tts --interactive
```

### Advanced Features

#### 🎤 Text-to-Speech Options
```bash
# Full TTS command with all options
chatterbox-cli tts \
  --text "Welcome to Chatterbox Enterprise" \
  --voice "en-us-female-1" \
  --output "welcome.wav" \
  --rate 1.2 \
  --pitch 0.1 \
  --volume 0.9 \
  --emotion "happy" \
  --watermark

# Interactive TTS mode
chatterbox-cli tts --interactive
```

#### 🎧 Speech-to-Text Options
```bash
# Full STT command with all options
chatterbox-cli stt \
  --input "meeting.wav" \
  --language "en-US" \
  --output "transcript.txt" \
  --punctuation \
  --confidence 0.8 \
  --timestamps

# Auto-detect language
chatterbox-cli stt --input "audio.wav" --punctuation
```

#### 🤖 Model Management
```bash
# List all models
chatterbox-cli models --filter "all" --detailed

# List only TTS models
chatterbox-cli models --filter "tts"

# List only STT models
chatterbox-cli models --filter "stt"
```

#### 📊 System Monitoring
```bash
# Basic status
chatterbox-cli status

# Detailed metrics
chatterbox-cli status --detailed

# Continuous monitoring (updates every 5 seconds)
chatterbox-cli status --watch --interval 5
```

#### 🔄 Batch Processing
```bash
# Create batch input file (batch_input.txt)
echo "tts,Hello world,en-us-female-1,output1.wav" > batch_input.txt
echo "tts,Goodbye world,en-us-male-1,output2.wav" >> batch_input.txt

# Run batch processing
chatterbox-cli batch --input batch_input.txt --output batch_results --workers 4
```

#### 🚀 Performance Benchmarking
```bash
# Benchmark TTS performance
chatterbox-cli benchmark --test-type "tts" --iterations 10 --concurrent 2

# Benchmark STT performance
chatterbox-cli benchmark --test-type "stt" --iterations 5 --concurrent 1

# Benchmark both TTS and STT
chatterbox-cli benchmark --test-type "both" --iterations 10 --concurrent 3
```

#### 🐚 Interactive Shell
```bash
# Start interactive shell
chatterbox-cli shell

# In shell mode:
> tts "Hello from shell" --voice "en-us-female-1"
> stt "audio.wav" --language "en-US"
> status
> models
> exit
```

### Output Formats

```bash
# JSON output
chatterbox-cli status --format json

# Table output (default)
chatterbox-cli status --format table

# Plain text output
chatterbox-cli status --format plain
```

### Configuration

```bash
# Use custom config file
chatterbox-cli --config /path/to/config.toml status

# Verbose logging
chatterbox-cli --verbose tts --text "Debug mode"
```

---

## 📺 TUI (Terminal User Interface)

### Installation & Launch

```bash
# Build TUI binary
cargo build --release

# Launch TUI
./target/release/chatterbox-tui

# Or with custom config
./target/release/chatterbox-tui --config /path/to/config.toml
```

### Interface Overview

```
┌─────────────────────────────────────────────────────────────────┐
│ Chatterbox Enterprise TUI                                       │
├─────────────────────────────────────────────────────────────────┤
│ [Dashboard] [TTS] [STT] [Models] [Settings] [Logs]             │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─ System Metrics ─┐  ┌─ Performance ─┐  ┌─ Models ─┐        │
│  │ CPU:  45.2%      │  │ [Chart Area]  │  │ TTS: 2   │        │
│  │ RAM:  67.8%      │  │               │  │ STT: 1   │        │
│  │ GPU:  23.1%      │  │               │  │ Total:3  │        │
│  └──────────────────┘  └───────────────┘  └──────────┘        │
│                                                                 │
│  ┌─ Recent Activity ──────────────────────────────────────────┐ │
│  │ 12:34:56 TTS    Success  234ms  user123                   │ │
│  │ 12:34:45 STT    Success  567ms  user456                   │ │
│  │ 12:34:32 TTS    Success  198ms  user789                   │ │
│  └────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│ Tab: Switch tabs | q: Quit | ESC: Back to dashboard            │
└─────────────────────────────────────────────────────────────────┘
```

### Navigation & Controls

| Key | Action |
|-----|--------|
| `Tab` | Switch between tabs |
| `q` | Quit application |
| `Esc` | Return to dashboard |
| `Enter` | Execute action (in TTS/STT modes) |
| `Backspace` | Delete character (in input fields) |
| `↑/↓` | Navigate lists |
| `Space` | Toggle selections |

### Dashboard Tab
- **Real-time Metrics**: CPU, Memory, GPU usage
- **Performance Charts**: Request rates, response times
- **Model Status**: Loaded models and memory usage
- **Activity Log**: Recent requests and their status

### TTS Tab
- **Voice Selection**: Choose from available voice models
- **Text Input**: Type text to synthesize
- **Options**: Adjust rate, pitch, volume, emotion
- **Real-time Synthesis**: Press Enter to generate speech

### STT Tab
- **File Input**: Specify audio file path
- **Language Selection**: Choose recognition language
- **Options**: Configure punctuation, confidence, timestamps
- **Real-time Recognition**: Press Enter to transcribe

### Models Tab
- **Model List**: All loaded TTS and STT models
- **Memory Usage**: Per-model memory consumption
- **Status Indicators**: Loading, loaded, error states
- **Model Management**: Load/unload models

### Settings Tab
- **Configuration**: Adjust system settings
- **Performance Tuning**: CPU/GPU allocation
- **Security Settings**: Authentication, encryption
- **Logging Levels**: Debug, info, warn, error

### Logs Tab
- **Real-time Logs**: System and application logs
- **Filtering**: By level, component, timestamp
- **Search**: Find specific log entries
- **Export**: Save logs to file

---

## 🌐 Web UI

### Installation & Launch

```bash
# Build Web UI binary
cargo build --release

# Launch Web UI server
./target/release/chatterbox-webui

# Or with custom config
./target/release/chatterbox-webui --config /path/to/config.toml
```

### Access URLs

- **Dashboard**: http://localhost:3030
- **TTS Interface**: http://localhost:3030/tts
- **STT Interface**: http://localhost:3030/stt
- **Models Management**: http://localhost:3030/models
- **API Documentation**: http://localhost:3030/api/docs

### Features Overview

#### 🏠 Dashboard
- **Real-time Metrics**: Interactive charts and gauges
- **System Status**: Service health indicators
- **Performance Analytics**: Historical data and trends
- **Request Monitoring**: Live request tracking
- **Resource Usage**: CPU, Memory, GPU utilization

#### 🎤 TTS Interface
- **Text Editor**: Rich text input with formatting
- **Voice Gallery**: Preview and select voices
- **Advanced Options**: Rate, pitch, volume, emotion controls
- **Audio Player**: Built-in playback and download
- **Batch Processing**: Multiple text synthesis
- **Voice Cloning**: Custom voice creation

#### 🎧 STT Interface
- **File Upload**: Drag-and-drop audio files
- **Live Recording**: Browser-based audio capture
- **Language Detection**: Automatic language identification
- **Transcript Editor**: Edit and export results
- **Confidence Visualization**: Word-level confidence scores
- **Timestamp Display**: Precise timing information

#### 🤖 Models Management
- **Model Library**: Browse available models
- **Performance Metrics**: Speed and accuracy stats
- **Memory Monitoring**: Resource usage tracking
- **Model Deployment**: Load/unload models
- **Version Control**: Model versioning and updates
- **Custom Models**: Upload and train custom models

#### 📊 Analytics & Reporting
- **Usage Statistics**: Request volumes and patterns
- **Performance Reports**: Response times and throughput
- **Error Analysis**: Failure rates and causes
- **User Analytics**: Usage by user/application
- **Cost Tracking**: Resource consumption costs
- **Export Options**: PDF, CSV, JSON reports

### API Integration

#### REST API Endpoints
```javascript
// TTS Synthesis
POST /api/tts/synthesize
{
  "text": "Hello world",
  "voice": "en-us-female-1",
  "options": {
    "rate": 1.0,
    "pitch": 0.0,
    "volume": 1.0,
    "emotion": "neutral"
  }
}

// STT Recognition
POST /api/stt/recognize
Content-Type: multipart/form-data
{
  "audio": [audio file],
  "language": "en-US",
  "options": {
    "punctuation": true,
    "confidence_threshold": 0.5
  }
}

// System Metrics
GET /api/metrics

// Model Management
GET /api/models
POST /api/models/load
DELETE /api/models/{model_id}
```

#### WebSocket Real-time Updates
```javascript
// Connect to WebSocket
const ws = new WebSocket('ws://localhost:3030/ws');

// Handle real-time updates
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  
  if (data.type === 'metrics') {
    updateDashboard(data.payload);
  } else if (data.type === 'request') {
    updateRequestLog(data.payload);
  }
};

// Send commands
ws.send(JSON.stringify({
  type: 'tts',
  payload: {
    text: "Hello world",
    voice: "en-us-female-1"
  }
}));
```

### Customization & Theming

#### Custom CSS
```css
/* Custom theme variables */
:root {
  --primary-color: #your-brand-color;
  --secondary-color: #your-accent-color;
  --background-color: #your-bg-color;
}

/* Custom dashboard layout */
.dashboard-grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}
```

#### JavaScript Extensions
```javascript
// Custom dashboard widgets
function addCustomWidget(containerId, widgetConfig) {
  // Implementation for custom widgets
}

// Custom event handlers
document.addEventListener('chatterbox:request', function(event) {
  // Handle custom events
});
```

---

## 🔗 Integration Examples

### Automation Scripts

#### Bash Script Integration
```bash
#!/bin/bash
# Automated TTS generation script

TEXTS=(
  "Welcome to our service"
  "Thank you for calling"
  "Please hold while we connect you"
)

for text in "${TEXTS[@]}"; do
  chatterbox-cli tts \
    --text "$text" \
    --voice "en-us-female-1" \
    --output "prompts/$(echo "$text" | tr ' ' '_').wav" \
    --format json
done
```

#### Python Integration
```python
import subprocess
import json

def synthesize_speech(text, voice="en-us-female-1"):
    """Synthesize speech using Chatterbox CLI"""
    result = subprocess.run([
        'chatterbox-cli', 'tts',
        '--text', text,
        '--voice', voice,
        '--format', 'json'
    ], capture_output=True, text=True)
    
    return json.loads(result.stdout)

def recognize_speech(audio_file):
    """Recognize speech using Chatterbox CLI"""
    result = subprocess.run([
        'chatterbox-cli', 'stt',
        '--input', audio_file,
        '--format', 'json'
    ], capture_output=True, text=True)
    
    return json.loads(result.stdout)

# Usage
tts_result = synthesize_speech("Hello from Python!")
stt_result = recognize_speech("audio.wav")
```

### Docker Integration

#### CLI in Docker
```dockerfile
FROM rust:1.70 as builder
COPY . /app
WORKDIR /app
RUN cargo build --release --bin chatterbox-cli

FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y ca-certificates
COPY --from=builder /app/target/release/chatterbox-cli /usr/local/bin/
ENTRYPOINT ["chatterbox-cli"]
```

```bash
# Build and run CLI in Docker
docker build -t chatterbox-cli .
docker run -v $(pwd)/audio:/audio chatterbox-cli tts --text "Hello Docker!"
```

### CI/CD Integration

#### GitHub Actions
```yaml
name: TTS Generation
on: [push]

jobs:
  generate-audio:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Chatterbox CLI
        run: |
          cargo build --release --bin chatterbox-cli
          
      - name: Generate TTS Audio
        run: |
          ./target/release/chatterbox-cli tts \
            --text "Build completed successfully" \
            --voice "en-us-female-1" \
            --output "build-notification.wav"
            
      - name: Upload Audio Artifact
        uses: actions/upload-artifact@v3
        with:
          name: tts-audio
          path: "*.wav"
```

---

## 🚀 Performance & Optimization

### CLI Performance Tips
- Use `--format json` for programmatic processing
- Enable `--verbose` only for debugging
- Use batch processing for multiple operations
- Configure appropriate worker counts for your hardware

### TUI Performance Tips
- Adjust refresh intervals based on your needs
- Use filtering to reduce log noise
- Monitor memory usage in the Models tab
- Close unused tabs to reduce resource usage

### Web UI Performance Tips
- Enable browser caching for static assets
- Use WebSocket connections for real-time updates
- Implement pagination for large datasets
- Optimize chart rendering for better performance

---

## 🔧 Troubleshooting

### Common CLI Issues
```bash
# Permission denied
chmod +x ./target/release/chatterbox-cli

# Config file not found
chatterbox-cli --config config/config.toml status

# Audio file format issues
chatterbox-cli stt --input audio.wav --verbose
```

### TUI Issues
```bash
# Terminal compatibility
export TERM=xterm-256color
./target/release/chatterbox-tui

# Resize issues
# Press Ctrl+L to refresh display
```

### Web UI Issues
```bash
# Port already in use
./target/release/chatterbox-webui --port 3031

# CORS issues
# Configure allowed origins in config.toml
```

---

## 📚 Additional Resources

- **API Documentation**: Available at `/api/docs` when Web UI is running
- **Configuration Reference**: See `config/config.example.toml`
- **Performance Tuning**: Check `DEPLOYMENT_GUIDE.md`
- **Security Configuration**: Review security settings in config
- **Custom Extensions**: See developer documentation for plugins

---

**🎉 You now have comprehensive CLI, TUI, and Web UI interfaces for Chatterbox Enterprise!**

Each interface is designed for different use cases:
- **CLI**: Automation, scripting, CI/CD integration
- **TUI**: Real-time monitoring, system administration
- **Web UI**: User-friendly management, analytics, collaboration
