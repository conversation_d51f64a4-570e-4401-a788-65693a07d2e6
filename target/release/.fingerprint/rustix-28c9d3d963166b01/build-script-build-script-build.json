{"rustc": 657458298742096754, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 9652763411108993936, "profile": 17151587042336376072, "path": 15336760248519228289, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rustix-28c9d3d963166b01/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "metadata": 7953970670347159126, "config": 2202906307356721367, "compile_kind": 0}