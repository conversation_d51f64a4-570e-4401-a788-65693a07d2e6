{"rustc": 657458298742096754, "features": "[\"basic-toml\", \"config\", \"humansize\", \"num-traits\", \"serde\", \"urlencode\"]", "declared_features": "[\"basic-toml\", \"config\", \"humansize\", \"markdown\", \"num-traits\", \"serde\", \"serde-json\", \"serde-yaml\", \"urlencode\", \"with-actix-web\", \"with-axum\", \"with-gotham\", \"with-hyper\", \"with-mendes\", \"with-rocket\", \"with-tide\", \"with-warp\"]", "target": 6798099621933875243, "profile": 15629586509063106353, "path": 17222798394057777631, "deps": [[871868610555714878, "mime_guess", false, 4334169163812100439], [4631004556132098597, "basic_toml", false, 12939487313669081720], [7470442545028885647, "mime", false, 7496228059881324849], [10633404241517405153, "serde", false, 1754368462240600734], [11852780150458150307, "syn", false, 4588168524210602166], [14184616077534028683, "parser", false, 10869681361649190194], [17525013869477438691, "quote", false, 7073750988044045661], [18036439996138669183, "proc_macro2", false, 10380317007028105658]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/askama_derive-508b7b96a2c968a3/dep-lib-askama_derive", "checksum": false}}], "rustflags": [], "metadata": 8402977040812984929, "config": 2202906307356721367, "compile_kind": 0}