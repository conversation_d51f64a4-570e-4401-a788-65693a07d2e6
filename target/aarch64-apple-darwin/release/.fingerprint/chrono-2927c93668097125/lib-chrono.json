{"rustc": 657458298742096754, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 3643947551994703751, "profile": 6331269874747811904, "path": 6981329754550603484, "deps": [[10448766010662481490, "num_traits", false, 17811295733862874451], [10633404241517405153, "serde", false, 5109338396333200840], [17958873330977204455, "iana_time_zone", false, 7082072354557271980]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/release/.fingerprint/chrono-2927c93668097125/dep-lib-chrono", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 9803565982372010724, "config": 2202906307356721367, "compile_kind": 14493662952515048101}