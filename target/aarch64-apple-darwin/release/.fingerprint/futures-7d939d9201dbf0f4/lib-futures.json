{"rustc": 657458298742096754, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 367108867326171846, "profile": 7061138148322209165, "path": 18020465671828189374, "deps": [[461436706529125561, "futures_io", false, 4858556579657042662], [1910231660504989506, "futures_task", false, 12157391310207057281], [5846781562065118163, "futures_channel", false, 16648078104537183102], [8083238378394459630, "futures_executor", false, 8195706819535757437], [9396302785578940539, "futures_core", false, 12496760049344593807], [11289432439818403777, "futures_sink", false, 10225327527863391800], [16476303074998891276, "futures_util", false, 15526984290002130556]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/release/.fingerprint/futures-7d939d9201dbf0f4/dep-lib-futures", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 7593721274762670645, "config": 2202906307356721367, "compile_kind": 14493662952515048101}