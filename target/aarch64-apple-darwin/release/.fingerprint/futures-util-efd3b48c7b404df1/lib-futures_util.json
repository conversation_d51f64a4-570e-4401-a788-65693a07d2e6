{"rustc": 657458298742096754, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 14098227409853078778, "profile": 7061138148322209165, "path": 9774881064298247872, "deps": [[461436706529125561, "futures_io", false, 4858556579657042662], [554324495028472449, "memchr", false, 15865173367266631754], [1910231660504989506, "futures_task", false, 12157391310207057281], [4761952582670444189, "pin_utils", false, 17614422325329185473], [5846781562065118163, "futures_channel", false, 16648078104537183102], [9396302785578940539, "futures_core", false, 12496760049344593807], [10080452282735337284, "futures_macro", false, 1812459562809580186], [11289432439818403777, "futures_sink", false, 10225327527863391800], [11809678037142197677, "pin_project_lite", false, 8287266273614999353], [17040352472033410869, "slab", false, 11017691913658280682]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/release/.fingerprint/futures-util-efd3b48c7b404df1/dep-lib-futures_util", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 5677230335954518303, "config": 2202906307356721367, "compile_kind": 14493662952515048101}