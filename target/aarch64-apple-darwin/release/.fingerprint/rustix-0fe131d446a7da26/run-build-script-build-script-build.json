{"rustc": 657458298742096754, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[1167534579069247419, "build_script_build", false, 10529825361278642302]], "local": [{"RerunIfChanged": {"output": "aarch64-apple-darwin/release/build/rustix-0fe131d446a7da26/output", "paths": ["build.rs"]}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_RUSTIX_USE_EXPERIMENTAL_ASM", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_RUSTIX_USE_LIBC", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_USE_LIBC", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_FEATURE_RUSTC_DEP_OF_STD", "val": null}}, {"RerunIfEnvChanged": {"var": "CARGO_CFG_MIRI", "val": null}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 0, "config": 0, "compile_kind": 0}