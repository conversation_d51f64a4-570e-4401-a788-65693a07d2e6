{"rustc": 657458298742096754, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 16815627201824848041, "profile": 6331269874747811904, "path": 18386749064320751168, "deps": [[2289252893304123003, "allocator_api2", false, 13242948022382050496], [7657838323326184233, "equivalent", false, 14771411212119301635], [9298736802812612957, "<PERSON><PERSON><PERSON>", false, 17375802334943393691]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/release/.fingerprint/hashbrown-d5a8a359dd6a0000/dep-lib-hashbrown", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 6228333144549390726, "config": 2202906307356721367, "compile_kind": 14493662952515048101}