{"rustc": 657458298742096754, "features": "[\"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust\"]", "declared_features": "[\"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust\"]", "target": 3720860574746933051, "profile": 6331269874747811904, "path": 1180730263996205797, "deps": [[902078471074753561, "async_trait", false, 16343415519043757327], [2054793698746015061, "ron", false, 13600735350370179329], [2375673591521754460, "ini", false, 2131522057229446697], [6554997584025100622, "toml", false, 7169861338997602313], [6954241390595330609, "nom", false, 18261301554179902428], [10633404241517405153, "serde", false, 5109338396333200840], [11852147291591572288, "lazy_static", false, 15899760336711596451], [12509852874546367857, "serde_json", false, 9534181349831439660], [14371587128542172710, "yaml_rust", false, 1042168495100768663], [15515435424891816657, "path<PERSON><PERSON>", false, 18176664336190132336], [16387082853761081162, "json5_rs", false, 1080997090004140407]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/release/.fingerprint/config-2fb924f08c6294a1/dep-lib-config", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 18005506352503131519, "config": 2202906307356721367, "compile_kind": 14493662952515048101}