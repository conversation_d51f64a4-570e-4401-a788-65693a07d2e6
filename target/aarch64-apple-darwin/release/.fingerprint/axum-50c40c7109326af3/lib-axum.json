{"rustc": 657458298742096754, "features": "[\"default\", \"form\", \"http1\", \"json\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"ws\"]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"headers\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 2272233468398073248, "profile": 6331269874747811904, "path": 8149203148243359886, "deps": [[504931904268503175, "http", false, 453327719002389897], [554324495028472449, "memchr", false, 15865173367266631754], [902078471074753561, "async_trait", false, 16343415519043757327], [1011640204279865735, "base64", false, 16652908330428295963], [2070739116102306658, "tokio", false, 16533359091594297886], [2543237566893615891, "bytes", false, 5872506577332915746], [3930354675071354477, "percent_encoding", false, 12403391393443868937], [5204382251033773414, "tower_service", false, 5160020533984039917], [5402984160842549810, "build_script_build", false, 1954459379313401387], [5641730196288390710, "tokio_tungstenite", false, 3975236796203065012], [7470442545028885647, "mime", false, 2671615963128579676], [8252504589640438155, "sha1", false, 15635274832118398581], [9433891360627596617, "serde_path_to_error", false, 2484867275122132451], [10633404241517405153, "serde", false, 5109338396333200840], [10821342338875855840, "tower_layer", false, 9786085792340189775], [11070927463981346568, "axum_core", false, 5983566988382833147], [11809678037142197677, "pin_project_lite", false, 8287266273614999353], [11995922566983883800, "tower", false, 12402317095008800827], [12509852874546367857, "serde_json", false, 9534181349831439660], [13606258873719457095, "http_body", false, 9905496203946700351], [14051957667571541382, "bitflags", false, 14024104231063972195], [14446744633799657975, "matchit", false, 10201708387443598953], [14663280588845858595, "itoa", false, 10283257784020864142], [14796620158950075325, "hyper", false, 11214432821465083655], [15255313314640684218, "sync_wrapper", false, 17613352084474181037], [15501288286569156197, "serde_urlencoded", false, 7883930540328587228], [16005103308121562836, "multer", false, 5525261273580805508], [16476303074998891276, "futures_util", false, 15526984290002130556]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/release/.fingerprint/axum-50c40c7109326af3/dep-lib-axum", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 17576717817111726285, "config": 2202906307356721367, "compile_kind": 14493662952515048101}