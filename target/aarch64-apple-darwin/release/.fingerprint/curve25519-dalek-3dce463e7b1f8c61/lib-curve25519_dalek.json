{"rustc": 657458298742096754, "features": "[\"alloc\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"avx2_backend\", \"default\", \"fiat-crypto\", \"fiat_u32_backend\", \"fiat_u64_backend\", \"nightly\", \"packed_simd\", \"serde\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 701098235721206363, "profile": 6331269874747811904, "path": 15176986393061851946, "deps": [[1486664334664968274, "subtle", false, 14644958431869557923], [8926101378076943148, "byteorder", false, 16321163038332444976], [11761531122794857361, "rand_core", false, 16486879022514708131], [12829070662860118430, "digest", false, 332985945580074758], [16255406213544131105, "zeroize", false, 10732289857048161645]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/release/.fingerprint/curve25519-dalek-3dce463e7b1f8c61/dep-lib-curve25519_dalek", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 4666180542459884904, "config": 2202906307356721367, "compile_kind": 14493662952515048101}