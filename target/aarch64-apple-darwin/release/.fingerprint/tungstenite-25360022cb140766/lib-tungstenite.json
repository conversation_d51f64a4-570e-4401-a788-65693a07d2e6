{"rustc": 657458298742096754, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\", \"url\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 13609099840216434247, "profile": 6331269874747811904, "path": 11987633499056755531, "deps": [[504931904268503175, "http", false, 453327719002389897], [523978686976814572, "data_encoding", false, 8556901207521821343], [1632138251998637657, "httparse", false, 3431850140166807043], [2543237566893615891, "bytes", false, 5872506577332915746], [5910892534286594076, "rand", false, 8941704202185138840], [6388073677788589347, "utf8", false, 16526168874423121038], [8252504589640438155, "sha1", false, 15635274832118398581], [8926101378076943148, "byteorder", false, 16321163038332444976], [11266840602298992523, "thiserror", false, 8429890771248855103], [15399619262696441677, "log", false, 8850934629118406408], [18130989770956114225, "url", false, 1609828698343636508]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/release/.fingerprint/tungstenite-25360022cb140766/dep-lib-tungstenite", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 10010679548380497168, "config": 2202906307356721367, "compile_kind": 14493662952515048101}