{"rustc": 657458298742096754, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 9156206050283006199, "profile": 7158748133538487479, "path": 17197901064513482735, "deps": [[3735647485472055247, "thread_local", false, 5832787447563169513], [5310583321894612108, "tracing_serde", false, 11463669558321108876], [8244776183334334055, "once_cell", false, 5490888198617425822], [8973061845687057626, "smallvec", false, 9955151458764303234], [10633404241517405153, "serde", false, 5109338396333200840], [11641382387439738731, "regex", false, 3450845920817645676], [11998755268370809021, "nu_ansi_term", false, 26136088452925444], [12509852874546367857, "serde_json", false, 9534181349831439660], [12679427474704493495, "matchers", false, 10742587468481745952], [13909326142996790163, "tracing_log", false, 7694240715140706181], [15515836537549001135, "tracing_core", false, 11651026844214887189], [16132118061651035107, "tracing", false, 8114917290640724248], [16405267689229882368, "sharded_slab", false, 11582495424421004394]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/release/.fingerprint/tracing-subscriber-f4ec481ecdd0fa88/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 12822423491602284083, "config": 2202906307356721367, "compile_kind": 14493662952515048101}