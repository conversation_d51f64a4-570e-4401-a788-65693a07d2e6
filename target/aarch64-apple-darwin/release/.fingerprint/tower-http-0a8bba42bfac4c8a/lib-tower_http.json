{"rustc": 657458298742096754, "features": "[\"cors\", \"default\", \"trace\", \"tracing\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 5946479460708793394, "profile": 6331269874747811904, "path": 770683400442405230, "deps": [[504931904268503175, "http", false, 453327719002389897], [1232724694990601665, "http_range_header", false, 4632719192135605934], [2543237566893615891, "bytes", false, 5872506577332915746], [4722402946104583944, "bitflags", false, 16898765021198539610], [5204382251033773414, "tower_service", false, 5160020533984039917], [9396302785578940539, "futures_core", false, 12496760049344593807], [10821342338875855840, "tower_layer", false, 9786085792340189775], [11809678037142197677, "pin_project_lite", false, 8287266273614999353], [13606258873719457095, "http_body", false, 9905496203946700351], [16132118061651035107, "tracing", false, 8114917290640724248], [16476303074998891276, "futures_util", false, 15526984290002130556]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/release/.fingerprint/tower-http-0a8bba42bfac4c8a/dep-lib-tower_http", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 6562849041221450444, "config": 2202906307356721367, "compile_kind": 14493662952515048101}