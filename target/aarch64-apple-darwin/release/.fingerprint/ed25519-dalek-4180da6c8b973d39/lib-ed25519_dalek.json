{"rustc": 657458298742096754, "features": "[\"default\", \"rand\", \"serde_crate\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"asm\", \"batch\", \"batch_deterministic\", \"default\", \"legacy_compatibility\", \"merlin\", \"nightly\", \"rand\", \"rand_core\", \"serde\", \"serde_bytes\", \"serde_crate\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 11260574056418161977, "profile": 6331269874747811904, "path": 10423549338768888261, "deps": [[288937492735761168, "sha2", false, 3559534585318626733], [2174722005733178090, "ed25519", false, 3591563833452072474], [5682154963376874164, "curve25519_dalek", false, 18096382900361182777], [10633404241517405153, "serde_crate", false, 5109338396333200840], [14778545527639897399, "rand", false, 7693357696556584615], [16255406213544131105, "zeroize", false, 10732289857048161645]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/release/.fingerprint/ed25519-dalek-4180da6c8b973d39/dep-lib-ed25519_dalek", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 14415899641967170644, "config": 2202906307356721367, "compile_kind": 14493662952515048101}