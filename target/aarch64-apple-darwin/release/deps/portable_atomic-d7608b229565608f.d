/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/target/aarch64-apple-darwin/release/deps/libportable_atomic-d7608b229565608f.rmeta: /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/cfgs.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/utils.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/gen/utils.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/core_atomic.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/atomic128/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/atomic128/aarch64.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/atomic128/macros.rs

/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/target/aarch64-apple-darwin/release/deps/libportable_atomic-d7608b229565608f.rlib: /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/cfgs.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/utils.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/gen/utils.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/core_atomic.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/atomic128/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/atomic128/aarch64.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/atomic128/macros.rs

/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/target/aarch64-apple-darwin/release/deps/portable_atomic-d7608b229565608f.d: /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/cfgs.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/utils.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/gen/utils.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/core_atomic.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/atomic128/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/atomic128/aarch64.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/atomic128/macros.rs

/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/lib.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/cfgs.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/utils.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/gen/utils.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/mod.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/core_atomic.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/atomic128/mod.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/atomic128/aarch64.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/portable-atomic-1.11.0/src/imp/atomic128/macros.rs:
