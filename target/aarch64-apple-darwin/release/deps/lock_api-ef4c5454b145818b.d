/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/target/aarch64-apple-darwin/release/deps/liblock_api-ef4c5454b145818b.rmeta: /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/lock_api-0.4.13/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/lock_api-0.4.13/src/mutex.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/lock_api-0.4.13/src/remutex.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/lock_api-0.4.13/src/rwlock.rs

/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/target/aarch64-apple-darwin/release/deps/liblock_api-ef4c5454b145818b.rlib: /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/lock_api-0.4.13/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/lock_api-0.4.13/src/mutex.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/lock_api-0.4.13/src/remutex.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/lock_api-0.4.13/src/rwlock.rs

/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/target/aarch64-apple-darwin/release/deps/lock_api-ef4c5454b145818b.d: /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/lock_api-0.4.13/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/lock_api-0.4.13/src/mutex.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/lock_api-0.4.13/src/remutex.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/lock_api-0.4.13/src/rwlock.rs

/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/lock_api-0.4.13/src/lib.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/lock_api-0.4.13/src/mutex.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/lock_api-0.4.13/src/remutex.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/lock_api-0.4.13/src/rwlock.rs:
