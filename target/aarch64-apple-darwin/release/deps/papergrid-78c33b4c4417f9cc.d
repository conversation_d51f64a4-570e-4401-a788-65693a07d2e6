/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/target/aarch64-apple-darwin/release/deps/libpapergrid-78c33b4c4417f9cc.rmeta: /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/ansi/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/ansi/ansi_buf.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/ansi/ansi_str.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/colors.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/alignment.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/border.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/borders.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/entity.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/formatting.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/horizontal_line.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/indent.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/position.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/sides.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/vertical_line.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/compact/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/spanned/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/spanned/borders_config.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/spanned/entity_map.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/spanned/offset.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/dimension/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/dimension/compact.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/dimension/spanned.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/dimension/spanned_vec_records.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/grid/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/grid/compact.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/grid/iterable.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/grid/peekable.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/exact_records.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/into_records.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/iter_records.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/peekable_records.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/vec_records/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/vec_records/cell.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/vec_records/cell_info.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/util/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/util/string.rs

/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/target/aarch64-apple-darwin/release/deps/libpapergrid-78c33b4c4417f9cc.rlib: /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/ansi/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/ansi/ansi_buf.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/ansi/ansi_str.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/colors.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/alignment.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/border.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/borders.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/entity.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/formatting.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/horizontal_line.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/indent.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/position.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/sides.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/vertical_line.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/compact/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/spanned/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/spanned/borders_config.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/spanned/entity_map.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/spanned/offset.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/dimension/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/dimension/compact.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/dimension/spanned.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/dimension/spanned_vec_records.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/grid/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/grid/compact.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/grid/iterable.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/grid/peekable.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/exact_records.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/into_records.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/iter_records.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/peekable_records.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/vec_records/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/vec_records/cell.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/vec_records/cell_info.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/util/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/util/string.rs

/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/target/aarch64-apple-darwin/release/deps/papergrid-78c33b4c4417f9cc.d: /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/lib.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/ansi/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/ansi/ansi_buf.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/ansi/ansi_str.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/colors.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/alignment.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/border.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/borders.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/entity.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/formatting.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/horizontal_line.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/indent.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/position.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/sides.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/vertical_line.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/compact/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/spanned/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/spanned/borders_config.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/spanned/entity_map.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/spanned/offset.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/dimension/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/dimension/compact.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/dimension/spanned.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/dimension/spanned_vec_records.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/grid/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/grid/compact.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/grid/iterable.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/grid/peekable.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/exact_records.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/into_records.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/iter_records.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/peekable_records.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/vec_records/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/vec_records/cell.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/vec_records/cell_info.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/util/mod.rs /Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/util/string.rs

/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/lib.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/ansi/mod.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/ansi/ansi_buf.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/ansi/ansi_str.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/colors.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/mod.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/alignment.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/border.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/borders.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/entity.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/formatting.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/horizontal_line.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/indent.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/position.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/sides.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/vertical_line.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/compact/mod.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/spanned/mod.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/spanned/borders_config.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/spanned/entity_map.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/config/spanned/offset.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/dimension/mod.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/dimension/compact.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/dimension/spanned.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/dimension/spanned_vec_records.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/grid/mod.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/grid/compact.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/grid/iterable.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/grid/peekable.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/mod.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/exact_records.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/into_records.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/iter_records.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/peekable_records.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/vec_records/mod.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/vec_records/cell.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/records/vec_records/cell_info.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/util/mod.rs:
/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/papergrid-0.11.0/src/util/string.rs:
