{"rustc": 657458298742096754, "features": "[\"alloc\", \"android-tzdata\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"android-tzdata\", \"arbitrary\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 3643947551994703751, "profile": 3797293754785534760, "path": 6981329754550603484, "deps": [[10448766010662481490, "num_traits", false, 1540199129515824993], [10633404241517405153, "serde", false, 17377261056403478855], [17958873330977204455, "iana_time_zone", false, 17944211189918661852]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/chrono-1f469458021bcb5c/dep-lib-chrono", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 9803565982372010724, "config": 2202906307356721367, "compile_kind": 14493662952515048101}