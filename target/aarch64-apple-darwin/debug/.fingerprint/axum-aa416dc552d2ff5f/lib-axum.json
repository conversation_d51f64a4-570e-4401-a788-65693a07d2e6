{"rustc": 657458298742096754, "features": "[\"default\", \"form\", \"http1\", \"json\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"ws\"]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"headers\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 2272233468398073248, "profile": 4374887572363265115, "path": 8149203148243359886, "deps": [[504931904268503175, "http", false, 15700821049539912839], [554324495028472449, "memchr", false, 13814225048810371401], [902078471074753561, "async_trait", false, 4723537573814239992], [1011640204279865735, "base64", false, 3711457778723059832], [2070739116102306658, "tokio", false, 8569350391102415802], [2543237566893615891, "bytes", false, 11285958084381035690], [3930354675071354477, "percent_encoding", false, 1292154236931134700], [5204382251033773414, "tower_service", false, 3780885632544697223], [5402984160842549810, "build_script_build", false, 6343845230439266056], [5641730196288390710, "tokio_tungstenite", false, 16027749376649742810], [7470442545028885647, "mime", false, 7582029441842730510], [8252504589640438155, "sha1", false, 1271555237853258607], [9433891360627596617, "serde_path_to_error", false, 16116815505031177669], [10633404241517405153, "serde", false, 7420612406277754670], [10821342338875855840, "tower_layer", false, 15813761809476068863], [11070927463981346568, "axum_core", false, 9447435100881434946], [11809678037142197677, "pin_project_lite", false, 16754346873387115223], [11995922566983883800, "tower", false, 15988464434562671145], [12509852874546367857, "serde_json", false, 797747138307893778], [13606258873719457095, "http_body", false, 1775941832943516538], [14051957667571541382, "bitflags", false, 9096864295849434225], [14446744633799657975, "matchit", false, 149576249764938794], [14663280588845858595, "itoa", false, 13684857788306320302], [14796620158950075325, "hyper", false, 6006358606205941452], [15255313314640684218, "sync_wrapper", false, 3416968923421115899], [15501288286569156197, "serde_urlencoded", false, 10801726278724553802], [16005103308121562836, "multer", false, 10768289074862774693], [16476303074998891276, "futures_util", false, 14799717098828253815]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/axum-aa416dc552d2ff5f/dep-lib-axum", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 17576717817111726285, "config": 2202906307356721367, "compile_kind": 14493662952515048101}