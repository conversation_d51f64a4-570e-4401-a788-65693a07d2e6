{"rustc": 657458298742096754, "features": "[\"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust\"]", "declared_features": "[\"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust\"]", "target": 3720860574746933051, "profile": 4374887572363265115, "path": 1180730263996205797, "deps": [[902078471074753561, "async_trait", false, 4723537573814239992], [2054793698746015061, "ron", false, 3280130527604955372], [2375673591521754460, "ini", false, 16379778212486287751], [6554997584025100622, "toml", false, 10319558446129476871], [6954241390595330609, "nom", false, 10749478766199033965], [10633404241517405153, "serde", false, 7420612406277754670], [11852147291591572288, "lazy_static", false, 9742125527777654707], [12509852874546367857, "serde_json", false, 797747138307893778], [14371587128542172710, "yaml_rust", false, 8817685313665474975], [15515435424891816657, "path<PERSON><PERSON>", false, 9792181240463016485], [16387082853761081162, "json5_rs", false, 12848495380291329314]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/config-b4db7e32fb7531c2/dep-lib-config", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 18005506352503131519, "config": 2202906307356721367, "compile_kind": 14493662952515048101}