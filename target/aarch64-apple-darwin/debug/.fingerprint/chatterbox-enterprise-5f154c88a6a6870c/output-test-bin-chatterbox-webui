{"$message_type":"diagnostic","message":"problems parsing template source at row 158, column 55 near:\n\"='memory_usage_mb')|sum }}MB</strong>\\n  \"...","code":null,"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":398,"byte_end":406,"line_start":16,"line_end":16,"column_start":10,"column_end":18,"is_primary":true,"text":[{"text":"#[derive(Template)]","highlight_start":10,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/bin/webui.rs","byte_start":398,"byte_end":406,"line_start":16,"line_end":16,"column_start":10,"column_end":18,"is_primary":false,"text":[{"text":"#[derive(Template)]","highlight_start":10,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Template)]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/askama_derive-0.12.5/src/lib.rs","byte_start":450,"byte_end":507,"line_start":22,"line_end":22,"column_start":1,"column_end":58,"is_primary":false,"text":[{"text":"pub fn derive_template(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: problems parsing template source at row 158, column 55 near:\u001b[0m\n\u001b[0m\u001b[1m       \"='memory_usage_mb')|sum }}MB</strong>\\n  \"...\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:16:10\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Template)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the derive macro `Template` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"template \"tts.html\" not found in directories [\"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/templates\"]","code":null,"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":599,"byte_end":607,"line_start":25,"line_end":25,"column_start":10,"column_end":18,"is_primary":true,"text":[{"text":"#[derive(Template)]","highlight_start":10,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/bin/webui.rs","byte_start":599,"byte_end":607,"line_start":25,"line_end":25,"column_start":10,"column_end":18,"is_primary":false,"text":[{"text":"#[derive(Template)]","highlight_start":10,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Template)]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/askama_derive-0.12.5/src/lib.rs","byte_start":450,"byte_end":507,"line_start":22,"line_end":22,"column_start":1,"column_end":58,"is_primary":false,"text":[{"text":"pub fn derive_template(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: template \"tts.html\" not found in directories [\"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/templates\"]\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:25:10\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m25\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Template)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the derive macro `Template` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"template \"stt.html\" not found in directories [\"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/templates\"]","code":null,"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":749,"byte_end":757,"line_start":33,"line_end":33,"column_start":10,"column_end":18,"is_primary":true,"text":[{"text":"#[derive(Template)]","highlight_start":10,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/bin/webui.rs","byte_start":749,"byte_end":757,"line_start":33,"line_end":33,"column_start":10,"column_end":18,"is_primary":false,"text":[{"text":"#[derive(Template)]","highlight_start":10,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Template)]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/askama_derive-0.12.5/src/lib.rs","byte_start":450,"byte_end":507,"line_start":22,"line_end":22,"column_start":1,"column_end":58,"is_primary":false,"text":[{"text":"pub fn derive_template(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: template \"stt.html\" not found in directories [\"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/templates\"]\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:33:10\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m33\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Template)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the derive macro `Template` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"template \"models.html\" not found in directories [\"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/templates\"]","code":null,"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":896,"byte_end":904,"line_start":41,"line_end":41,"column_start":10,"column_end":18,"is_primary":true,"text":[{"text":"#[derive(Template)]","highlight_start":10,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":{"span":{"file_name":"src/bin/webui.rs","byte_start":896,"byte_end":904,"line_start":41,"line_end":41,"column_start":10,"column_end":18,"is_primary":false,"text":[{"text":"#[derive(Template)]","highlight_start":10,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},"macro_decl_name":"#[derive(Template)]","def_site_span":{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/askama_derive-0.12.5/src/lib.rs","byte_start":450,"byte_end":507,"line_start":22,"line_end":22,"column_start":1,"column_end":58,"is_primary":false,"text":[{"text":"pub fn derive_template(input: TokenStream) -> TokenStream {","highlight_start":1,"highlight_end":58}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}}}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: template \"models.html\" not found in directories [\"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/templates\"]\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:41:10\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m41\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m#[derive(Template)]\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: this error originates in the derive macro `Template` (in Nightly builds, run with -Z macro-backtrace for more info)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `RecognitionOptions` and `SynthesisOptions`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/webui.rs","byte_start":80,"byte_end":96,"line_start":3,"line_end":3,"column_start":32,"column_end":48,"is_primary":true,"text":[{"text":"    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},","highlight_start":32,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":98,"byte_end":116,"line_start":3,"line_end":3,"column_start":50,"column_end":68,"is_primary":true,"text":[{"text":"    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},","highlight_start":50,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":78,"byte_end":116,"line_start":3,"line_end":3,"column_start":30,"column_end":68,"is_primary":true,"text":[{"text":"    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},","highlight_start":30,"highlight_end":68}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/bin/webui.rs","byte_start":61,"byte_end":62,"line_start":3,"line_end":3,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/bin/webui.rs","byte_start":116,"byte_end":117,"line_start":3,"line_end":3,"column_start":68,"column_end":69,"is_primary":true,"text":[{"text":"    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},","highlight_start":68,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `RecognitionOptions` and `SynthesisOptions`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:3:32\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `std::collections::HashMap`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/webui.rs","byte_start":289,"byte_end":314,"line_start":11,"line_end":11,"column_start":5,"column_end":30,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":5,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":285,"byte_end":316,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use std::collections::HashMap;","highlight_start":1,"highlight_end":31},{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `std::collections::HashMap`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:11:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::collections::HashMap;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type alias takes 1 generic argument but 2 generic arguments were supplied","code":{"code":"E0107","explanation":"An incorrect number of generic arguments was provided.\n\nErroneous code example:\n\n```compile_fail,E0107\nstruct Foo<T> { x: T }\n\nstruct Bar { x: Foo }             // error: wrong number of type arguments:\n                                  //        expected 1, found 0\nstruct Baz<S, T> { x: Foo<S, T> } // error: wrong number of type arguments:\n                                  //        expected 1, found 2\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool>(x);                 // error: wrong number of type arguments:\n                                    //        expected 2, found 1\n    foo::<bool, i32, i32>(x, 2, 4); // error: wrong number of type arguments:\n                                    //        expected 2, found 3\n    f::<'static>();                 // error: wrong number of lifetime arguments\n                                    //        expected 0, found 1\n}\n```\n\nWhen using/declaring an item with generic arguments, you must provide the exact\nsame number:\n\n```\nstruct Foo<T> { x: T }\n\nstruct Bar<T> { x: Foo<T> }               // ok!\nstruct Baz<S, T> { x: Foo<S>, y: Foo<T> } // ok!\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool, u32>(x, 12);              // ok!\n    f();                                  // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":8915,"byte_end":8921,"line_start":325,"line_end":325,"column_start":53,"column_end":59,"is_primary":true,"text":[{"text":"async fn dashboard_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {","highlight_start":53,"highlight_end":59}],"label":"expected 1 generic argument","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"type alias defined here, with 1 generic parameter: `T`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":457,"byte_end":458,"line_start":14,"line_end":14,"column_start":17,"column_end":18,"is_primary":false,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":17,"highlight_end":18}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":450,"byte_end":456,"line_start":14,"line_end":14,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the unnecessary generic argument","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":8932,"byte_end":8949,"line_start":325,"line_end":325,"column_start":70,"column_end":87,"is_primary":true,"text":[{"text":"async fn dashboard_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {","highlight_start":70,"highlight_end":87}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0107]\u001b[0m\u001b[0m\u001b[1m: type alias takes 1 generic argument but 2 generic arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:325:53\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m325\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn dashboard_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove the unnecessary generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected 1 generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: type alias defined here, with 1 generic parameter: `T`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs:14:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub type Result<T> = std::result::Result<T, ChatterboxError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type alias takes 1 generic argument but 2 generic arguments were supplied","code":{"code":"E0107","explanation":"An incorrect number of generic arguments was provided.\n\nErroneous code example:\n\n```compile_fail,E0107\nstruct Foo<T> { x: T }\n\nstruct Bar { x: Foo }             // error: wrong number of type arguments:\n                                  //        expected 1, found 0\nstruct Baz<S, T> { x: Foo<S, T> } // error: wrong number of type arguments:\n                                  //        expected 1, found 2\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool>(x);                 // error: wrong number of type arguments:\n                                    //        expected 2, found 1\n    foo::<bool, i32, i32>(x, 2, 4); // error: wrong number of type arguments:\n                                    //        expected 2, found 3\n    f::<'static>();                 // error: wrong number of lifetime arguments\n                                    //        expected 0, found 1\n}\n```\n\nWhen using/declaring an item with generic arguments, you must provide the exact\nsame number:\n\n```\nstruct Foo<T> { x: T }\n\nstruct Bar<T> { x: Foo<T> }               // ok!\nstruct Baz<S, T> { x: Foo<S>, y: Foo<T> } // ok!\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool, u32>(x, 12);              // ok!\n    f();                                  // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":9385,"byte_end":9391,"line_start":340,"line_end":340,"column_start":52,"column_end":58,"is_primary":true,"text":[{"text":"async fn tts_page_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {","highlight_start":52,"highlight_end":58}],"label":"expected 1 generic argument","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"type alias defined here, with 1 generic parameter: `T`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":457,"byte_end":458,"line_start":14,"line_end":14,"column_start":17,"column_end":18,"is_primary":false,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":17,"highlight_end":18}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":450,"byte_end":456,"line_start":14,"line_end":14,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the unnecessary generic argument","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":9402,"byte_end":9419,"line_start":340,"line_end":340,"column_start":69,"column_end":86,"is_primary":true,"text":[{"text":"async fn tts_page_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {","highlight_start":69,"highlight_end":86}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0107]\u001b[0m\u001b[0m\u001b[1m: type alias takes 1 generic argument but 2 generic arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:340:52\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m340\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn tts_page_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove the unnecessary generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected 1 generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: type alias defined here, with 1 generic parameter: `T`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs:14:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub type Result<T> = std::result::Result<T, ChatterboxError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type alias takes 1 generic argument but 2 generic arguments were supplied","code":{"code":"E0107","explanation":"An incorrect number of generic arguments was provided.\n\nErroneous code example:\n\n```compile_fail,E0107\nstruct Foo<T> { x: T }\n\nstruct Bar { x: Foo }             // error: wrong number of type arguments:\n                                  //        expected 1, found 0\nstruct Baz<S, T> { x: Foo<S, T> } // error: wrong number of type arguments:\n                                  //        expected 1, found 2\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool>(x);                 // error: wrong number of type arguments:\n                                    //        expected 2, found 1\n    foo::<bool, i32, i32>(x, 2, 4); // error: wrong number of type arguments:\n                                    //        expected 2, found 3\n    f::<'static>();                 // error: wrong number of lifetime arguments\n                                    //        expected 0, found 1\n}\n```\n\nWhen using/declaring an item with generic arguments, you must provide the exact\nsame number:\n\n```\nstruct Foo<T> { x: T }\n\nstruct Bar<T> { x: Foo<T> }               // ok!\nstruct Baz<S, T> { x: Foo<S>, y: Foo<T> } // ok!\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool, u32>(x, 12);              // ok!\n    f();                                  // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":9796,"byte_end":9802,"line_start":353,"line_end":353,"column_start":52,"column_end":58,"is_primary":true,"text":[{"text":"async fn stt_page_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {","highlight_start":52,"highlight_end":58}],"label":"expected 1 generic argument","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"type alias defined here, with 1 generic parameter: `T`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":457,"byte_end":458,"line_start":14,"line_end":14,"column_start":17,"column_end":18,"is_primary":false,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":17,"highlight_end":18}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":450,"byte_end":456,"line_start":14,"line_end":14,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the unnecessary generic argument","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":9813,"byte_end":9830,"line_start":353,"line_end":353,"column_start":69,"column_end":86,"is_primary":true,"text":[{"text":"async fn stt_page_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {","highlight_start":69,"highlight_end":86}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0107]\u001b[0m\u001b[0m\u001b[1m: type alias takes 1 generic argument but 2 generic arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:353:52\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m353\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn stt_page_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove the unnecessary generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected 1 generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: type alias defined here, with 1 generic parameter: `T`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs:14:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub type Result<T> = std::result::Result<T, ChatterboxError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type alias takes 1 generic argument but 2 generic arguments were supplied","code":{"code":"E0107","explanation":"An incorrect number of generic arguments was provided.\n\nErroneous code example:\n\n```compile_fail,E0107\nstruct Foo<T> { x: T }\n\nstruct Bar { x: Foo }             // error: wrong number of type arguments:\n                                  //        expected 1, found 0\nstruct Baz<S, T> { x: Foo<S, T> } // error: wrong number of type arguments:\n                                  //        expected 1, found 2\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool>(x);                 // error: wrong number of type arguments:\n                                    //        expected 2, found 1\n    foo::<bool, i32, i32>(x, 2, 4); // error: wrong number of type arguments:\n                                    //        expected 2, found 3\n    f::<'static>();                 // error: wrong number of lifetime arguments\n                                    //        expected 0, found 1\n}\n```\n\nWhen using/declaring an item with generic arguments, you must provide the exact\nsame number:\n\n```\nstruct Foo<T> { x: T }\n\nstruct Bar<T> { x: Foo<T> }               // ok!\nstruct Baz<S, T> { x: Foo<S>, y: Foo<T> } // ok!\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool, u32>(x, 12);              // ok!\n    f();                                  // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":10239,"byte_end":10245,"line_start":366,"line_end":366,"column_start":55,"column_end":61,"is_primary":true,"text":[{"text":"async fn models_page_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {","highlight_start":55,"highlight_end":61}],"label":"expected 1 generic argument","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"type alias defined here, with 1 generic parameter: `T`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":457,"byte_end":458,"line_start":14,"line_end":14,"column_start":17,"column_end":18,"is_primary":false,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":17,"highlight_end":18}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":450,"byte_end":456,"line_start":14,"line_end":14,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the unnecessary generic argument","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":10256,"byte_end":10273,"line_start":366,"line_end":366,"column_start":72,"column_end":89,"is_primary":true,"text":[{"text":"async fn models_page_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {","highlight_start":72,"highlight_end":89}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0107]\u001b[0m\u001b[0m\u001b[1m: type alias takes 1 generic argument but 2 generic arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:366:55\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m366\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn models_page_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove the unnecessary generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected 1 generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: type alias defined here, with 1 generic parameter: `T`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs:14:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub type Result<T> = std::result::Result<T, ChatterboxError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type alias takes 1 generic argument but 2 generic arguments were supplied","code":{"code":"E0107","explanation":"An incorrect number of generic arguments was provided.\n\nErroneous code example:\n\n```compile_fail,E0107\nstruct Foo<T> { x: T }\n\nstruct Bar { x: Foo }             // error: wrong number of type arguments:\n                                  //        expected 1, found 0\nstruct Baz<S, T> { x: Foo<S, T> } // error: wrong number of type arguments:\n                                  //        expected 1, found 2\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool>(x);                 // error: wrong number of type arguments:\n                                    //        expected 2, found 1\n    foo::<bool, i32, i32>(x, 2, 4); // error: wrong number of type arguments:\n                                    //        expected 2, found 3\n    f::<'static>();                 // error: wrong number of lifetime arguments\n                                    //        expected 0, found 1\n}\n```\n\nWhen using/declaring an item with generic arguments, you must provide the exact\nsame number:\n\n```\nstruct Foo<T> { x: T }\n\nstruct Bar<T> { x: Foo<T> }               // ok!\nstruct Baz<S, T> { x: Foo<S>, y: Foo<T> } // ok!\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool, u32>(x, 12);              // ok!\n    f();                                  // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":10789,"byte_end":10795,"line_start":383,"line_end":383,"column_start":6,"column_end":12,"is_primary":true,"text":[{"text":") -> Result<impl Reply, warp::Rejection> {","highlight_start":6,"highlight_end":12}],"label":"expected 1 generic argument","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"type alias defined here, with 1 generic parameter: `T`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":457,"byte_end":458,"line_start":14,"line_end":14,"column_start":17,"column_end":18,"is_primary":false,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":17,"highlight_end":18}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":450,"byte_end":456,"line_start":14,"line_end":14,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the unnecessary generic argument","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":10806,"byte_end":10823,"line_start":383,"line_end":383,"column_start":23,"column_end":40,"is_primary":true,"text":[{"text":") -> Result<impl Reply, warp::Rejection> {","highlight_start":23,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0107]\u001b[0m\u001b[0m\u001b[1m: type alias takes 1 generic argument but 2 generic arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:383:6\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m383\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<impl Reply, warp::Rejection> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove the unnecessary generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected 1 generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: type alias defined here, with 1 generic parameter: `T`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs:14:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub type Result<T> = std::result::Result<T, ChatterboxError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type alias takes 1 generic argument but 2 generic arguments were supplied","code":{"code":"E0107","explanation":"An incorrect number of generic arguments was provided.\n\nErroneous code example:\n\n```compile_fail,E0107\nstruct Foo<T> { x: T }\n\nstruct Bar { x: Foo }             // error: wrong number of type arguments:\n                                  //        expected 1, found 0\nstruct Baz<S, T> { x: Foo<S, T> } // error: wrong number of type arguments:\n                                  //        expected 1, found 2\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool>(x);                 // error: wrong number of type arguments:\n                                    //        expected 2, found 1\n    foo::<bool, i32, i32>(x, 2, 4); // error: wrong number of type arguments:\n                                    //        expected 2, found 3\n    f::<'static>();                 // error: wrong number of lifetime arguments\n                                    //        expected 0, found 1\n}\n```\n\nWhen using/declaring an item with generic arguments, you must provide the exact\nsame number:\n\n```\nstruct Foo<T> { x: T }\n\nstruct Bar<T> { x: Foo<T> }               // ok!\nstruct Baz<S, T> { x: Foo<S>, y: Foo<T> } // ok!\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool, u32>(x, 12);              // ok!\n    f();                                  // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":11304,"byte_end":11310,"line_start":402,"line_end":402,"column_start":6,"column_end":12,"is_primary":true,"text":[{"text":") -> Result<impl Reply, warp::Rejection> {","highlight_start":6,"highlight_end":12}],"label":"expected 1 generic argument","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"type alias defined here, with 1 generic parameter: `T`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":457,"byte_end":458,"line_start":14,"line_end":14,"column_start":17,"column_end":18,"is_primary":false,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":17,"highlight_end":18}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":450,"byte_end":456,"line_start":14,"line_end":14,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the unnecessary generic argument","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":11321,"byte_end":11338,"line_start":402,"line_end":402,"column_start":23,"column_end":40,"is_primary":true,"text":[{"text":") -> Result<impl Reply, warp::Rejection> {","highlight_start":23,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0107]\u001b[0m\u001b[0m\u001b[1m: type alias takes 1 generic argument but 2 generic arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:402:6\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m402\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<impl Reply, warp::Rejection> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove the unnecessary generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected 1 generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: type alias defined here, with 1 generic parameter: `T`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs:14:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub type Result<T> = std::result::Result<T, ChatterboxError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type alias takes 1 generic argument but 2 generic arguments were supplied","code":{"code":"E0107","explanation":"An incorrect number of generic arguments was provided.\n\nErroneous code example:\n\n```compile_fail,E0107\nstruct Foo<T> { x: T }\n\nstruct Bar { x: Foo }             // error: wrong number of type arguments:\n                                  //        expected 1, found 0\nstruct Baz<S, T> { x: Foo<S, T> } // error: wrong number of type arguments:\n                                  //        expected 1, found 2\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool>(x);                 // error: wrong number of type arguments:\n                                    //        expected 2, found 1\n    foo::<bool, i32, i32>(x, 2, 4); // error: wrong number of type arguments:\n                                    //        expected 2, found 3\n    f::<'static>();                 // error: wrong number of lifetime arguments\n                                    //        expected 0, found 1\n}\n```\n\nWhen using/declaring an item with generic arguments, you must provide the exact\nsame number:\n\n```\nstruct Foo<T> { x: T }\n\nstruct Bar<T> { x: Foo<T> }               // ok!\nstruct Baz<S, T> { x: Foo<S>, y: Foo<T> } // ok!\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool, u32>(x, 12);              // ok!\n    f();                                  // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":11780,"byte_end":11786,"line_start":418,"line_end":418,"column_start":55,"column_end":61,"is_primary":true,"text":[{"text":"async fn api_metrics_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {","highlight_start":55,"highlight_end":61}],"label":"expected 1 generic argument","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"type alias defined here, with 1 generic parameter: `T`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":457,"byte_end":458,"line_start":14,"line_end":14,"column_start":17,"column_end":18,"is_primary":false,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":17,"highlight_end":18}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":450,"byte_end":456,"line_start":14,"line_end":14,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the unnecessary generic argument","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":11797,"byte_end":11814,"line_start":418,"line_end":418,"column_start":72,"column_end":89,"is_primary":true,"text":[{"text":"async fn api_metrics_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {","highlight_start":72,"highlight_end":89}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0107]\u001b[0m\u001b[0m\u001b[1m: type alias takes 1 generic argument but 2 generic arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:418:55\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m418\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn api_metrics_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove the unnecessary generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                       \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected 1 generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: type alias defined here, with 1 generic parameter: `T`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs:14:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub type Result<T> = std::result::Result<T, ChatterboxError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type alias takes 1 generic argument but 2 generic arguments were supplied","code":{"code":"E0107","explanation":"An incorrect number of generic arguments was provided.\n\nErroneous code example:\n\n```compile_fail,E0107\nstruct Foo<T> { x: T }\n\nstruct Bar { x: Foo }             // error: wrong number of type arguments:\n                                  //        expected 1, found 0\nstruct Baz<S, T> { x: Foo<S, T> } // error: wrong number of type arguments:\n                                  //        expected 1, found 2\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool>(x);                 // error: wrong number of type arguments:\n                                    //        expected 2, found 1\n    foo::<bool, i32, i32>(x, 2, 4); // error: wrong number of type arguments:\n                                    //        expected 2, found 3\n    f::<'static>();                 // error: wrong number of lifetime arguments\n                                    //        expected 0, found 1\n}\n```\n\nWhen using/declaring an item with generic arguments, you must provide the exact\nsame number:\n\n```\nstruct Foo<T> { x: T }\n\nstruct Bar<T> { x: Foo<T> }               // ok!\nstruct Baz<S, T> { x: Foo<S>, y: Foo<T> } // ok!\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool, u32>(x, 12);              // ok!\n    f();                                  // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":11964,"byte_end":11970,"line_start":423,"line_end":423,"column_start":54,"column_end":60,"is_primary":true,"text":[{"text":"async fn api_models_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {","highlight_start":54,"highlight_end":60}],"label":"expected 1 generic argument","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"type alias defined here, with 1 generic parameter: `T`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":457,"byte_end":458,"line_start":14,"line_end":14,"column_start":17,"column_end":18,"is_primary":false,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":17,"highlight_end":18}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":450,"byte_end":456,"line_start":14,"line_end":14,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the unnecessary generic argument","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":11981,"byte_end":11998,"line_start":423,"line_end":423,"column_start":71,"column_end":88,"is_primary":true,"text":[{"text":"async fn api_models_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {","highlight_start":71,"highlight_end":88}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0107]\u001b[0m\u001b[0m\u001b[1m: type alias takes 1 generic argument but 2 generic arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:423:54\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m423\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn api_models_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove the unnecessary generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected 1 generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: type alias defined here, with 1 generic parameter: `T`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs:14:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub type Result<T> = std::result::Result<T, ChatterboxError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"type alias takes 1 generic argument but 2 generic arguments were supplied","code":{"code":"E0107","explanation":"An incorrect number of generic arguments was provided.\n\nErroneous code example:\n\n```compile_fail,E0107\nstruct Foo<T> { x: T }\n\nstruct Bar { x: Foo }             // error: wrong number of type arguments:\n                                  //        expected 1, found 0\nstruct Baz<S, T> { x: Foo<S, T> } // error: wrong number of type arguments:\n                                  //        expected 1, found 2\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool>(x);                 // error: wrong number of type arguments:\n                                    //        expected 2, found 1\n    foo::<bool, i32, i32>(x, 2, 4); // error: wrong number of type arguments:\n                                    //        expected 2, found 3\n    f::<'static>();                 // error: wrong number of lifetime arguments\n                                    //        expected 0, found 1\n}\n```\n\nWhen using/declaring an item with generic arguments, you must provide the exact\nsame number:\n\n```\nstruct Foo<T> { x: T }\n\nstruct Bar<T> { x: Foo<T> }               // ok!\nstruct Baz<S, T> { x: Foo<S>, y: Foo<T> } // ok!\n\nfn foo<T, U>(x: T, y: U) {}\nfn f() {}\n\nfn main() {\n    let x: bool = true;\n    foo::<bool, u32>(x, 12);              // ok!\n    f();                                  // ok!\n}\n```\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":12175,"byte_end":12181,"line_start":431,"line_end":431,"column_start":6,"column_end":12,"is_primary":true,"text":[{"text":") -> Result<impl Reply, warp::Rejection> {","highlight_start":6,"highlight_end":12}],"label":"expected 1 generic argument","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"type alias defined here, with 1 generic parameter: `T`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":457,"byte_end":458,"line_start":14,"line_end":14,"column_start":17,"column_end":18,"is_primary":false,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":17,"highlight_end":18}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs","byte_start":450,"byte_end":456,"line_start":14,"line_end":14,"column_start":10,"column_end":16,"is_primary":true,"text":[{"text":"pub type Result<T> = std::result::Result<T, ChatterboxError>;","highlight_start":10,"highlight_end":16}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the unnecessary generic argument","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":12192,"byte_end":12209,"line_start":431,"line_end":431,"column_start":23,"column_end":40,"is_primary":true,"text":[{"text":") -> Result<impl Reply, warp::Rejection> {","highlight_start":23,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0107]\u001b[0m\u001b[0m\u001b[1m: type alias takes 1 generic argument but 2 generic arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:431:6\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m431\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m) -> Result<impl Reply, warp::Rejection> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove the unnecessary generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m      \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected 1 generic argument\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: type alias defined here, with 1 generic parameter: `T`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/error.rs:14:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub type Result<T> = std::result::Result<T, ChatterboxError>;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this function takes 1 argument but 3 arguments were supplied","code":{"code":"E0061","explanation":"An invalid number of arguments was passed when calling a function.\n\nErroneous code example:\n\n```compile_fail,E0061\nfn f(u: i32) {}\n\nf(); // error!\n```\n\nThe number of arguments passed to a function must match the number of arguments\nspecified in the function signature.\n\nFor example, a function like:\n\n```\nfn f(a: u16, b: &str) {}\n```\n\nMust always be called with exactly two arguments, e.g., `f(2, \"test\")`.\n\nNote that Rust does not have a notion of optional function arguments or\nvariadic functions (except for its C-FFI).\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":3084,"byte_end":3108,"line_start":135,"line_end":135,"column_start":17,"column_end":41,"is_primary":false,"text":[{"text":"                watermark_engine.clone(),","highlight_start":17,"highlight_end":41}],"label":"unexpected argument #2 of type `Arc<WatermarkEngine>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":3126,"byte_end":3143,"line_start":136,"line_end":136,"column_start":17,"column_end":34,"is_primary":false,"text":[{"text":"                telemetry.clone(),","highlight_start":17,"highlight_end":34}],"label":"unexpected argument #3 of type `Arc<TelemetryCollector>`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":3014,"byte_end":3035,"line_start":133,"line_end":133,"column_start":13,"column_end":34,"is_primary":true,"text":[{"text":"            ChatterboxEngine::new(","highlight_start":13,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"expected `Config`, found `Arc<ModelManager>`","code":null,"level":"note","spans":[{"file_name":"src/bin/webui.rs","byte_start":3053,"byte_end":3066,"line_start":134,"line_end":134,"column_start":17,"column_end":30,"is_primary":true,"text":[{"text":"                model_manager,","highlight_start":17,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"expected struct `Config`\n   found struct `Arc<ModelManager>`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"associated function defined here","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/engine.rs","byte_start":4331,"byte_end":4334,"line_start":142,"line_end":142,"column_start":18,"column_end":21,"is_primary":true,"text":[{"text":"    pub async fn new(config: Config) -> Result<Self> {","highlight_start":18,"highlight_end":21}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null},{"message":"remove the extra arguments","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":3066,"byte_end":3108,"line_start":134,"line_end":135,"column_start":30,"column_end":41,"is_primary":true,"text":[{"text":"                model_manager,","highlight_start":30,"highlight_end":31},{"text":"                watermark_engine.clone(),","highlight_start":1,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null},{"file_name":"src/bin/webui.rs","byte_start":3108,"byte_end":3143,"line_start":135,"line_end":136,"column_start":41,"column_end":34,"is_primary":true,"text":[{"text":"                watermark_engine.clone(),","highlight_start":41,"highlight_end":42},{"text":"                telemetry.clone(),","highlight_start":1,"highlight_end":34}],"label":null,"suggested_replacement":"","suggestion_applicability":"HasPlaceholders","expansion":null},{"file_name":"src/bin/webui.rs","byte_start":3053,"byte_end":3066,"line_start":134,"line_end":134,"column_start":17,"column_end":30,"is_primary":true,"text":[{"text":"                model_manager,","highlight_start":17,"highlight_end":30}],"label":null,"suggested_replacement":"/* Config */","suggestion_applicability":"HasPlaceholders","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0061]\u001b[0m\u001b[0m\u001b[1m: this function takes 1 argument but 3 arguments were supplied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:133:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m133\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            ChatterboxEngine::new(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                model_manager,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                watermark_engine.clone(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #2 of type `Arc<WatermarkEngine>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m136\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                telemetry.clone(),\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12munexpected argument #3 of type `Arc<TelemetryCollector>`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: expected `Config`, found `Arc<ModelManager>`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:134:17\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                model_manager,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: expected struct `\u001b[0m\u001b[0m\u001b[1m\u001b[35mConfig\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m               found struct `\u001b[0m\u001b[0m\u001b[1m\u001b[35mArc<ModelManager>\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: associated function defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/src/engine.rs:142:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m142\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub async fn new(config: Config) -> Result<Self> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: remove the extra arguments\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[38;5;9mmodel_manager,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m134\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[38;5;10m/* Config */\u001b[0m\u001b[0m,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `render` found for struct `DashboardTemplate` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":9311,"byte_end":9317,"line_start":337,"line_end":337,"column_start":35,"column_end":41,"is_primary":true,"text":[{"text":"    Ok(warp::reply::html(template.render().unwrap()))","highlight_start":35,"highlight_end":41}],"label":"method not found in `DashboardTemplate`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":446,"byte_end":470,"line_start":18,"line_end":18,"column_start":1,"column_end":25,"is_primary":false,"text":[{"text":"struct DashboardTemplate {","highlight_start":1,"highlight_end":25}],"label":"method `render` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is implemented and in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the following trait defines an item `render`, perhaps you need to implement it:\ncandidate #1: `Template`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `render` found for struct `DashboardTemplate` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:337:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct DashboardTemplate {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethod `render` not found for this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m337\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Ok(warp::reply::html(template.render().unwrap()))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `DashboardTemplate`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is implemented and in scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait defines an item `render`, perhaps you need to implement it:\u001b[0m\n\u001b[0m            candidate #1: `Template`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `render` found for struct `TtsTemplate` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":9722,"byte_end":9728,"line_start":350,"line_end":350,"column_start":35,"column_end":41,"is_primary":true,"text":[{"text":"    Ok(warp::reply::html(template.render().unwrap()))","highlight_start":35,"highlight_end":41}],"label":"method not found in `TtsTemplate`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":641,"byte_end":659,"line_start":27,"line_end":27,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"struct TtsTemplate {","highlight_start":1,"highlight_end":19}],"label":"method `render` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is implemented and in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the following trait defines an item `render`, perhaps you need to implement it:\ncandidate #1: `Template`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `render` found for struct `TtsTemplate` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:350:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m27\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct TtsTemplate {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethod `render` not found for this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m350\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Ok(warp::reply::html(template.render().unwrap()))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `TtsTemplate`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is implemented and in scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait defines an item `render`, perhaps you need to implement it:\u001b[0m\n\u001b[0m            candidate #1: `Template`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `render` found for struct `SttTemplate` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":10162,"byte_end":10168,"line_start":363,"line_end":363,"column_start":35,"column_end":41,"is_primary":true,"text":[{"text":"    Ok(warp::reply::html(template.render().unwrap()))","highlight_start":35,"highlight_end":41}],"label":"method not found in `SttTemplate`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":791,"byte_end":809,"line_start":35,"line_end":35,"column_start":1,"column_end":19,"is_primary":false,"text":[{"text":"struct SttTemplate {","highlight_start":1,"highlight_end":19}],"label":"method `render` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is implemented and in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the following trait defines an item `render`, perhaps you need to implement it:\ncandidate #1: `Template`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `render` found for struct `SttTemplate` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:363:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m35\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct SttTemplate {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethod `render` not found for this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m363\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Ok(warp::reply::html(template.render().unwrap()))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `SttTemplate`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is implemented and in scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait defines an item `render`, perhaps you need to implement it:\u001b[0m\n\u001b[0m            candidate #1: `Template`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"no method named `render` found for struct `ModelsTemplate` in the current scope","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":10684,"byte_end":10690,"line_start":377,"line_end":377,"column_start":35,"column_end":41,"is_primary":true,"text":[{"text":"    Ok(warp::reply::html(template.render().unwrap()))","highlight_start":35,"highlight_end":41}],"label":"method not found in `ModelsTemplate`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":941,"byte_end":962,"line_start":43,"line_end":43,"column_start":1,"column_end":22,"is_primary":false,"text":[{"text":"struct ModelsTemplate {","highlight_start":1,"highlight_end":22}],"label":"method `render` not found for this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"items from traits can only be used if the trait is implemented and in scope","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"the following trait defines an item `render`, perhaps you need to implement it:\ncandidate #1: `Template`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: no method named `render` found for struct `ModelsTemplate` in the current scope\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:377:35\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct ModelsTemplate {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mmethod `render` not found for this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m377\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Ok(warp::reply::html(template.render().unwrap()))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod not found in `ModelsTemplate`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: items from traits can only be used if the trait is implemented and in scope\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait defines an item `render`, perhaps you need to implement it:\u001b[0m\n\u001b[0m            candidate #1: `Template`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `request`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/webui.rs","byte_start":10737,"byte_end":10744,"line_start":381,"line_end":381,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    request: TtsRequest,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":10737,"byte_end":10744,"line_start":381,"line_end":381,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    request: TtsRequest,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":"_request","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `request`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:381:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m381\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    request: TtsRequest,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_request`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/webui.rs","byte_start":10762,"byte_end":10767,"line_start":382,"line_end":382,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: Arc<AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":10762,"byte_end":10767,"line_start":382,"line_end":382,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: Arc<AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:382:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m382\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state: Arc<AppState>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `form`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/webui.rs","byte_start":11240,"byte_end":11244,"line_start":400,"line_end":400,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    form: warp::multipart::FormData,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":11240,"byte_end":11244,"line_start":400,"line_end":400,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    form: warp::multipart::FormData,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":"_form","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `form`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:400:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m400\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    form: warp::multipart::FormData,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_form`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/webui.rs","byte_start":11277,"byte_end":11282,"line_start":401,"line_end":401,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: Arc<AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":11277,"byte_end":11282,"line_start":401,"line_end":401,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    state: Arc<AppState>,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:401:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m401\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state: Arc<AppState>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/webui.rs","byte_start":11939,"byte_end":11944,"line_start":423,"line_end":423,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"async fn api_models_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":11939,"byte_end":11944,"line_start":423,"line_end":423,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"async fn api_models_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {","highlight_start":29,"highlight_end":34}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:423:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m423\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn api_models_handler(state: Arc<AppState>) -> Result<impl Reply, warp::Rejection> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `ws`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/webui.rs","byte_start":12319,"byte_end":12321,"line_start":435,"line_end":435,"column_start":31,"column_end":33,"is_primary":true,"text":[{"text":"async fn websocket_connection(ws: warp::ws::WebSocket, state: Arc<AppState>) {","highlight_start":31,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":12319,"byte_end":12321,"line_start":435,"line_end":435,"column_start":31,"column_end":33,"is_primary":true,"text":[{"text":"async fn websocket_connection(ws: warp::ws::WebSocket, state: Arc<AppState>) {","highlight_start":31,"highlight_end":33}],"label":null,"suggested_replacement":"_ws","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `ws`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:435:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m435\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn websocket_connection(ws: warp::ws::WebSocket, state: Arc<AppState>) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_ws`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `state`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/webui.rs","byte_start":12344,"byte_end":12349,"line_start":435,"line_end":435,"column_start":56,"column_end":61,"is_primary":true,"text":[{"text":"async fn websocket_connection(ws: warp::ws::WebSocket, state: Arc<AppState>) {","highlight_start":56,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/bin/webui.rs","byte_start":12344,"byte_end":12349,"line_start":435,"line_end":435,"column_start":56,"column_end":61,"is_primary":true,"text":[{"text":"async fn websocket_connection(ws: warp::ws::WebSocket, state: Arc<AppState>) {","highlight_start":56,"highlight_end":61}],"label":null,"suggested_replacement":"_state","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `state`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:435:56\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m435\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0masync fn websocket_connection(ws: warp::ws::WebSocket, state: Arc<AppState>) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_state`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":6300,"byte_end":6317,"line_start":236,"line_end":236,"column_start":19,"column_end":36,"is_primary":true,"text":[{"text":"        .and_then(dashboard_handler);","highlight_start":19,"highlight_end":36}],"label":"the trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":6291,"byte_end":6299,"line_start":236,"line_end":236,"column_start":10,"column_end":18,"is_primary":false,"text":[{"text":"        .and_then(dashboard_handler);","highlight_start":10,"highlight_end":18}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following other types implement trait `reject::sealed::CombineRejection<E>`:\n  `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\n  `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\n  `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\n  `Rejection` implements `reject::sealed::CombineRejection<Rejection>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `warp::Filter::and_then`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":7921,"byte_end":7929,"line_start":255,"line_end":255,"column_start":8,"column_end":16,"is_primary":false,"text":[{"text":"    fn and_then<F>(self, fun: F) -> AndThen<Self, F>","highlight_start":8,"highlight_end":16}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":8116,"byte_end":8145,"line_start":260,"line_end":260,"column_start":42,"column_end":71,"is_primary":true,"text":[{"text":"        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,","highlight_start":42,"highlight_end":71}],"label":"required by this bound in `Filter::and_then`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:236:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m236\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .and_then(dashboard_handler);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `reject::sealed::CombineRejection<E>`:\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `warp::Filter::and_then`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs:260:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m255\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn and_then<F>(self, fun: F) -> AndThen<Self, F>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m260\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Filter::and_then`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":6468,"byte_end":6484,"line_start":242,"line_end":242,"column_start":19,"column_end":35,"is_primary":true,"text":[{"text":"        .and_then(tts_page_handler);","highlight_start":19,"highlight_end":35}],"label":"the trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":6459,"byte_end":6467,"line_start":242,"line_end":242,"column_start":10,"column_end":18,"is_primary":false,"text":[{"text":"        .and_then(tts_page_handler);","highlight_start":10,"highlight_end":18}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following other types implement trait `reject::sealed::CombineRejection<E>`:\n  `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\n  `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\n  `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\n  `Rejection` implements `reject::sealed::CombineRejection<Rejection>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `warp::Filter::and_then`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":7921,"byte_end":7929,"line_start":255,"line_end":255,"column_start":8,"column_end":16,"is_primary":false,"text":[{"text":"    fn and_then<F>(self, fun: F) -> AndThen<Self, F>","highlight_start":8,"highlight_end":16}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":8116,"byte_end":8145,"line_start":260,"line_end":260,"column_start":42,"column_end":71,"is_primary":true,"text":[{"text":"        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,","highlight_start":42,"highlight_end":71}],"label":"required by this bound in `Filter::and_then`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:242:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m242\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .and_then(tts_page_handler);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `reject::sealed::CombineRejection<E>`:\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `warp::Filter::and_then`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs:260:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m255\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn and_then<F>(self, fun: F) -> AndThen<Self, F>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m260\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Filter::and_then`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":6635,"byte_end":6651,"line_start":248,"line_end":248,"column_start":19,"column_end":35,"is_primary":true,"text":[{"text":"        .and_then(stt_page_handler);","highlight_start":19,"highlight_end":35}],"label":"the trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":6626,"byte_end":6634,"line_start":248,"line_end":248,"column_start":10,"column_end":18,"is_primary":false,"text":[{"text":"        .and_then(stt_page_handler);","highlight_start":10,"highlight_end":18}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following other types implement trait `reject::sealed::CombineRejection<E>`:\n  `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\n  `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\n  `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\n  `Rejection` implements `reject::sealed::CombineRejection<Rejection>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `warp::Filter::and_then`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":7921,"byte_end":7929,"line_start":255,"line_end":255,"column_start":8,"column_end":16,"is_primary":false,"text":[{"text":"    fn and_then<F>(self, fun: F) -> AndThen<Self, F>","highlight_start":8,"highlight_end":16}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":8116,"byte_end":8145,"line_start":260,"line_end":260,"column_start":42,"column_end":71,"is_primary":true,"text":[{"text":"        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,","highlight_start":42,"highlight_end":71}],"label":"required by this bound in `Filter::and_then`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:248:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m248\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .and_then(stt_page_handler);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `reject::sealed::CombineRejection<E>`:\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `warp::Filter::and_then`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs:260:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m255\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn and_then<F>(self, fun: F) -> AndThen<Self, F>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m260\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Filter::and_then`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":6811,"byte_end":6830,"line_start":254,"line_end":254,"column_start":19,"column_end":38,"is_primary":true,"text":[{"text":"        .and_then(models_page_handler);","highlight_start":19,"highlight_end":38}],"label":"the trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":6802,"byte_end":6810,"line_start":254,"line_end":254,"column_start":10,"column_end":18,"is_primary":false,"text":[{"text":"        .and_then(models_page_handler);","highlight_start":10,"highlight_end":18}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following other types implement trait `reject::sealed::CombineRejection<E>`:\n  `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\n  `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\n  `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\n  `Rejection` implements `reject::sealed::CombineRejection<Rejection>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `warp::Filter::and_then`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":7921,"byte_end":7929,"line_start":255,"line_end":255,"column_start":8,"column_end":16,"is_primary":false,"text":[{"text":"    fn and_then<F>(self, fun: F) -> AndThen<Self, F>","highlight_start":8,"highlight_end":16}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":8116,"byte_end":8145,"line_start":260,"line_end":260,"column_start":42,"column_end":71,"is_primary":true,"text":[{"text":"        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,","highlight_start":42,"highlight_end":71}],"label":"required by this bound in `Filter::and_then`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:254:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m254\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .and_then(models_page_handler);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `reject::sealed::CombineRejection<E>`:\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `warp::Filter::and_then`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs:260:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m255\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn and_then<F>(self, fun: F) -> AndThen<Self, F>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m260\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Filter::and_then`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":7081,"byte_end":7096,"line_start":263,"line_end":263,"column_start":19,"column_end":34,"is_primary":true,"text":[{"text":"        .and_then(api_tts_handler);","highlight_start":19,"highlight_end":34}],"label":"the trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":7072,"byte_end":7080,"line_start":263,"line_end":263,"column_start":10,"column_end":18,"is_primary":false,"text":[{"text":"        .and_then(api_tts_handler);","highlight_start":10,"highlight_end":18}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following other types implement trait `reject::sealed::CombineRejection<E>`:\n  `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\n  `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\n  `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\n  `Rejection` implements `reject::sealed::CombineRejection<Rejection>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `warp::Filter::and_then`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":7921,"byte_end":7929,"line_start":255,"line_end":255,"column_start":8,"column_end":16,"is_primary":false,"text":[{"text":"    fn and_then<F>(self, fun: F) -> AndThen<Self, F>","highlight_start":8,"highlight_end":16}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":8116,"byte_end":8145,"line_start":260,"line_end":260,"column_start":42,"column_end":71,"is_primary":true,"text":[{"text":"        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,","highlight_start":42,"highlight_end":71}],"label":"required by this bound in `Filter::and_then`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:263:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m263\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .and_then(api_tts_handler);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `reject::sealed::CombineRejection<E>`:\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `warp::Filter::and_then`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs:260:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m255\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn and_then<F>(self, fun: F) -> AndThen<Self, F>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m260\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Filter::and_then`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":7333,"byte_end":7348,"line_start":271,"line_end":271,"column_start":19,"column_end":34,"is_primary":true,"text":[{"text":"        .and_then(api_stt_handler);","highlight_start":19,"highlight_end":34}],"label":"the trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":7324,"byte_end":7332,"line_start":271,"line_end":271,"column_start":10,"column_end":18,"is_primary":false,"text":[{"text":"        .and_then(api_stt_handler);","highlight_start":10,"highlight_end":18}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following other types implement trait `reject::sealed::CombineRejection<E>`:\n  `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\n  `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\n  `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\n  `Rejection` implements `reject::sealed::CombineRejection<Rejection>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `warp::Filter::and_then`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":7921,"byte_end":7929,"line_start":255,"line_end":255,"column_start":8,"column_end":16,"is_primary":false,"text":[{"text":"    fn and_then<F>(self, fun: F) -> AndThen<Self, F>","highlight_start":8,"highlight_end":16}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":8116,"byte_end":8145,"line_start":260,"line_end":260,"column_start":42,"column_end":71,"is_primary":true,"text":[{"text":"        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,","highlight_start":42,"highlight_end":71}],"label":"required by this bound in `Filter::and_then`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:271:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m271\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .and_then(api_stt_handler);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `reject::sealed::CombineRejection<E>`:\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `warp::Filter::and_then`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs:260:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m255\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn and_then<F>(self, fun: F) -> AndThen<Self, F>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m260\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Filter::and_then`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":7516,"byte_end":7535,"line_start":277,"line_end":277,"column_start":19,"column_end":38,"is_primary":true,"text":[{"text":"        .and_then(api_metrics_handler);","highlight_start":19,"highlight_end":38}],"label":"the trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":7507,"byte_end":7515,"line_start":277,"line_end":277,"column_start":10,"column_end":18,"is_primary":false,"text":[{"text":"        .and_then(api_metrics_handler);","highlight_start":10,"highlight_end":18}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following other types implement trait `reject::sealed::CombineRejection<E>`:\n  `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\n  `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\n  `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\n  `Rejection` implements `reject::sealed::CombineRejection<Rejection>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `warp::Filter::and_then`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":7921,"byte_end":7929,"line_start":255,"line_end":255,"column_start":8,"column_end":16,"is_primary":false,"text":[{"text":"    fn and_then<F>(self, fun: F) -> AndThen<Self, F>","highlight_start":8,"highlight_end":16}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":8116,"byte_end":8145,"line_start":260,"line_end":260,"column_start":42,"column_end":71,"is_primary":true,"text":[{"text":"        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,","highlight_start":42,"highlight_end":71}],"label":"required by this bound in `Filter::and_then`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:277:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m277\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .and_then(api_metrics_handler);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `reject::sealed::CombineRejection<E>`:\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `warp::Filter::and_then`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs:260:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m255\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn and_then<F>(self, fun: F) -> AndThen<Self, F>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m260\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Filter::and_then`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":7701,"byte_end":7719,"line_start":283,"line_end":283,"column_start":19,"column_end":37,"is_primary":true,"text":[{"text":"        .and_then(api_models_handler);","highlight_start":19,"highlight_end":37}],"label":"the trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":7692,"byte_end":7700,"line_start":283,"line_end":283,"column_start":10,"column_end":18,"is_primary":false,"text":[{"text":"        .and_then(api_models_handler);","highlight_start":10,"highlight_end":18}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following other types implement trait `reject::sealed::CombineRejection<E>`:\n  `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\n  `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\n  `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\n  `Rejection` implements `reject::sealed::CombineRejection<Rejection>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `warp::Filter::and_then`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":7921,"byte_end":7929,"line_start":255,"line_end":255,"column_start":8,"column_end":16,"is_primary":false,"text":[{"text":"    fn and_then<F>(self, fun: F) -> AndThen<Self, F>","highlight_start":8,"highlight_end":16}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":8116,"byte_end":8145,"line_start":260,"line_end":260,"column_start":42,"column_end":71,"is_primary":true,"text":[{"text":"        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,","highlight_start":42,"highlight_end":71}],"label":"required by this bound in `Filter::and_then`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:283:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m283\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .and_then(api_models_handler);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `reject::sealed::CombineRejection<E>`:\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `warp::Filter::and_then`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs:260:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m255\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn and_then<F>(self, fun: F) -> AndThen<Self, F>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m260\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Filter::and_then`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied","code":{"code":"E0277","explanation":"You tried to use a type which doesn't implement some trait in a place which\nexpected that trait.\n\nErroneous code example:\n\n```compile_fail,E0277\n// here we declare the Foo trait with a bar method\ntrait Foo {\n    fn bar(&self);\n}\n\n// we now declare a function which takes an object implementing the Foo trait\nfn some_func<T: Foo>(foo: T) {\n    foo.bar();\n}\n\nfn main() {\n    // we now call the method with the i32 type, which doesn't implement\n    // the Foo trait\n    some_func(5i32); // error: the trait bound `i32 : Foo` is not satisfied\n}\n```\n\nIn order to fix this error, verify that the type you're using does implement\nthe trait. Example:\n\n```\ntrait Foo {\n    fn bar(&self);\n}\n\n// we implement the trait on the i32 type\nimpl Foo for i32 {\n    fn bar(&self) {}\n}\n\nfn some_func<T: Foo>(foo: T) {\n    foo.bar(); // we can now use this method since i32 implements the\n               // Foo trait\n}\n\nfn main() {\n    some_func(5i32); // ok!\n}\n```\n\nOr in a generic context, an erroneous code example would look like:\n\n```compile_fail,E0277\nfn some_func<T>(foo: T) {\n    println!(\"{:?}\", foo); // error: the trait `core::fmt::Debug` is not\n                           //        implemented for the type `T`\n}\n\nfn main() {\n    // We now call the method with the i32 type,\n    // which *does* implement the Debug trait.\n    some_func(5i32);\n}\n```\n\nNote that the error here is in the definition of the generic function. Although\nwe only call it with a parameter that does implement `Debug`, the compiler\nstill rejects the function. It must work with all possible input types. In\norder to make this example compile, we need to restrict the generic type we're\naccepting:\n\n```\nuse std::fmt;\n\n// Restrict the input type to types that implement Debug.\nfn some_func<T: fmt::Debug>(foo: T) {\n    println!(\"{:?}\", foo);\n}\n\nfn main() {\n    // Calling the method is still fine, as i32 implements Debug.\n    some_func(5i32);\n\n    // This would fail to compile now:\n    // struct WithoutDebug;\n    // some_func(WithoutDebug);\n}\n```\n\nRust only looks at the signature of the called function, as such it must\nalready specify all requirements that will be used for every type parameter.\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":7886,"byte_end":7903,"line_start":289,"line_end":289,"column_start":19,"column_end":36,"is_primary":true,"text":[{"text":"        .and_then(websocket_handler);","highlight_start":19,"highlight_end":36}],"label":"the trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":7877,"byte_end":7885,"line_start":289,"line_end":289,"column_start":10,"column_end":18,"is_primary":false,"text":[{"text":"        .and_then(websocket_handler);","highlight_start":10,"highlight_end":18}],"label":"required by a bound introduced by this call","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the following other types implement trait `reject::sealed::CombineRejection<E>`:\n  `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\n  `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\n  `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\n  `Rejection` implements `reject::sealed::CombineRejection<Rejection>`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"required by a bound in `warp::Filter::and_then`","code":null,"level":"note","spans":[{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":7921,"byte_end":7929,"line_start":255,"line_end":255,"column_start":8,"column_end":16,"is_primary":false,"text":[{"text":"    fn and_then<F>(self, fun: F) -> AndThen<Self, F>","highlight_start":8,"highlight_end":16}],"label":"required by a bound in this associated function","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs","byte_start":8116,"byte_end":8145,"line_start":260,"line_end":260,"column_start":42,"column_end":71,"is_primary":true,"text":[{"text":"        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,","highlight_start":42,"highlight_end":71}],"label":"required by this bound in `Filter::and_then`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0277]\u001b[0m\u001b[0m\u001b[1m: the trait bound `ChatterboxError: reject::sealed::CombineRejection<Rejection>` is not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:289:19\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m289\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .and_then(websocket_handler);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mthe trait `reject::sealed::CombineRejection<Rejection>` is not implemented for `ChatterboxError`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound introduced by this call\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: the following other types implement trait `reject::sealed::CombineRejection<E>`:\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Infallible` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Infallible>`\u001b[0m\n\u001b[0m              `Rejection` implements `reject::sealed::CombineRejection<Rejection>`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: required by a bound in `warp::Filter::and_then`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/mod.rs:260:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m255\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn and_then<F>(self, fun: F) -> AndThen<Self, F>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mrequired by a bound in this associated function\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m260\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        <F::Output as TryFuture>::Error: CombineRejection<Self::Error>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10mrequired by this bound in `Filter::and_then`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the method `or` exists for struct `AndThen<And<..., ...>, ...>`, but its trait bounds were not satisfied","code":{"code":"E0599","explanation":"This error occurs when a method is used on a type which doesn't implement it:\n\nErroneous code example:\n\n```compile_fail,E0599\nstruct Mouth;\n\nlet x = Mouth;\nx.chocolate(); // error: no method named `chocolate` found for type `Mouth`\n               //        in the current scope\n```\n\nIn this case, you need to implement the `chocolate` method to fix the error:\n\n```\nstruct Mouth;\n\nimpl Mouth {\n    fn chocolate(&self) { // We implement the `chocolate` method here.\n        println!(\"Hmmm! I love chocolate!\");\n    }\n}\n\nlet x = Mouth;\nx.chocolate(); // ok!\n```\n"},"level":"error","spans":[{"file_name":"src/bin/webui.rs","byte_start":7950,"byte_end":7969,"line_start":292,"line_end":293,"column_start":18,"column_end":10,"is_primary":false,"text":[{"text":"    let routes = dashboard","highlight_start":18,"highlight_end":27},{"text":"        .or(tts_page)","highlight_start":1,"highlight_end":10}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/webui.rs","byte_start":7969,"byte_end":7971,"line_start":293,"line_end":293,"column_start":10,"column_end":12,"is_primary":true,"text":[{"text":"        .or(tts_page)","highlight_start":10,"highlight_end":12}],"label":"method cannot be called on `AndThen<And<..., ...>, ...>` due to unsatisfied trait bounds","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/and_then.rs","byte_start":263,"byte_end":287,"line_start":12,"line_end":12,"column_start":1,"column_end":25,"is_primary":false,"text":[{"text":"pub struct AndThen<T, F> {","highlight_start":1,"highlight_end":25}],"label":"doesn't satisfy `_: FilterBase` or `_: Filter`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"the full type name has been written to '/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/target/aarch64-apple-darwin/debug/deps/chatterbox_webui-5f154c88a6a6870c.long-type-314265469156558438.txt'","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"consider using `--verbose` to print the full type name to the console","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"the following trait bounds were not satisfied:\n`warp::filter::and_then::AndThen<warp::filter::and::And<impl warp::Filter + warp::filter::FilterBase<Extract = (), Error = Rejection> + std::marker::Copy, impl warp::Filter + warp::filter::FilterBase<Extract = (Arc<AppState>,), Error = Infallible> + Clone>, fn(Arc<AppState>) -> impl Future<Output = Result<impl Reply, ChatterboxError>> {dashboard_handler}>: warp::filter::FilterBase`\nwhich is required by `warp::filter::and_then::AndThen<warp::filter::and::And<impl warp::Filter + warp::filter::FilterBase<Extract = (), Error = Rejection> + std::marker::Copy, impl warp::Filter + warp::filter::FilterBase<Extract = (Arc<AppState>,), Error = Infallible> + Clone>, fn(Arc<AppState>) -> impl Future<Output = Result<impl Reply, ChatterboxError>> {dashboard_handler}>: warp::Filter`\n`&warp::filter::and_then::AndThen<warp::filter::and::And<impl warp::Filter + warp::filter::FilterBase<Extract = (), Error = Rejection> + std::marker::Copy, impl warp::Filter + warp::filter::FilterBase<Extract = (Arc<AppState>,), Error = Infallible> + Clone>, fn(Arc<AppState>) -> impl Future<Output = Result<impl Reply, ChatterboxError>> {dashboard_handler}>: warp::filter::FilterBase`\nwhich is required by `&warp::filter::and_then::AndThen<warp::filter::and::And<impl warp::Filter + warp::filter::FilterBase<Extract = (), Error = Rejection> + std::marker::Copy, impl warp::Filter + warp::filter::FilterBase<Extract = (Arc<AppState>,), Error = Infallible> + Clone>, fn(Arc<AppState>) -> impl Future<Output = Result<impl Reply, ChatterboxError>> {dashboard_handler}>: warp::Filter`\n`&mut warp::filter::and_then::AndThen<warp::filter::and::And<impl warp::Filter + warp::filter::FilterBase<Extract = (), Error = Rejection> + std::marker::Copy, impl warp::Filter + warp::filter::FilterBase<Extract = (Arc<AppState>,), Error = Infallible> + Clone>, fn(Arc<AppState>) -> impl Future<Output = Result<impl Reply, ChatterboxError>> {dashboard_handler}>: warp::filter::FilterBase`\nwhich is required by `&mut warp::filter::and_then::AndThen<warp::filter::and::And<impl warp::Filter + warp::filter::FilterBase<Extract = (), Error = Rejection> + std::marker::Copy, impl warp::Filter + warp::filter::FilterBase<Extract = (Arc<AppState>,), Error = Infallible> + Clone>, fn(Arc<AppState>) -> impl Future<Output = Result<impl Reply, ChatterboxError>> {dashboard_handler}>: warp::Filter`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0599]\u001b[0m\u001b[0m\u001b[1m: the method `or` exists for struct `AndThen<And<..., ...>, ...>`, but its trait bounds were not satisfied\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/webui.rs:293:10\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m292\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    let routes = dashboard\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m __________________-\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m293\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .or(tts_page)\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mmethod cannot be called on `AndThen<And<..., ...>, ...>` due to unsatisfied trait bounds\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|_________|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m::: \u001b[0m\u001b[0m/Users/<USER>/.cargo/registry/src/index.crates.io-6f17d22bba15001f/warp-0.3.7/src/filter/and_then.rs:12:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0mpub struct AndThen<T, F> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mdoesn't satisfy `_: FilterBase` or `_: Filter`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the full type name has been written to '/Users/<USER>/Documents/augment-projects/sources/mc_tts_stt_next/target/aarch64-apple-darwin/debug/deps/chatterbox_webui-5f154c88a6a6870c.long-type-314265469156558438.txt'\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: consider using `--verbose` to print the full type name to the console\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: the following trait bounds were not satisfied:\u001b[0m\n\u001b[0m            `warp::filter::and_then::AndThen<warp::filter::and::And<impl warp::Filter + warp::filter::FilterBase<Extract = (), Error = Rejection> + std::marker::Copy, impl warp::Filter + warp::filter::FilterBase<Extract = (Arc<AppState>,), Error = Infallible> + Clone>, fn(Arc<AppState>) -> impl Future<Output = Result<impl Reply, ChatterboxError>> {dashboard_handler}>: warp::filter::FilterBase`\u001b[0m\n\u001b[0m            which is required by `warp::filter::and_then::AndThen<warp::filter::and::And<impl warp::Filter + warp::filter::FilterBase<Extract = (), Error = Rejection> + std::marker::Copy, impl warp::Filter + warp::filter::FilterBase<Extract = (Arc<AppState>,), Error = Infallible> + Clone>, fn(Arc<AppState>) -> impl Future<Output = Result<impl Reply, ChatterboxError>> {dashboard_handler}>: warp::Filter`\u001b[0m\n\u001b[0m            `&warp::filter::and_then::AndThen<warp::filter::and::And<impl warp::Filter + warp::filter::FilterBase<Extract = (), Error = Rejection> + std::marker::Copy, impl warp::Filter + warp::filter::FilterBase<Extract = (Arc<AppState>,), Error = Infallible> + Clone>, fn(Arc<AppState>) -> impl Future<Output = Result<impl Reply, ChatterboxError>> {dashboard_handler}>: warp::filter::FilterBase`\u001b[0m\n\u001b[0m            which is required by `&warp::filter::and_then::AndThen<warp::filter::and::And<impl warp::Filter + warp::filter::FilterBase<Extract = (), Error = Rejection> + std::marker::Copy, impl warp::Filter + warp::filter::FilterBase<Extract = (Arc<AppState>,), Error = Infallible> + Clone>, fn(Arc<AppState>) -> impl Future<Output = Result<impl Reply, ChatterboxError>> {dashboard_handler}>: warp::Filter`\u001b[0m\n\u001b[0m            `&mut warp::filter::and_then::AndThen<warp::filter::and::And<impl warp::Filter + warp::filter::FilterBase<Extract = (), Error = Rejection> + std::marker::Copy, impl warp::Filter + warp::filter::FilterBase<Extract = (Arc<AppState>,), Error = Infallible> + Clone>, fn(Arc<AppState>) -> impl Future<Output = Result<impl Reply, ChatterboxError>> {dashboard_handler}>: warp::filter::FilterBase`\u001b[0m\n\u001b[0m            which is required by `&mut warp::filter::and_then::AndThen<warp::filter::and::And<impl warp::Filter + warp::filter::FilterBase<Extract = (), Error = Rejection> + std::marker::Copy, impl warp::Filter + warp::filter::FilterBase<Extract = (Arc<AppState>,), Error = Infallible> + Clone>, fn(Arc<AppState>) -> impl Future<Output = Result<impl Reply, ChatterboxError>> {dashboard_handler}>: warp::Filter`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 28 previous errors; 9 warnings emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 28 previous errors; 9 warnings emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"Some errors have detailed explanations: E0061, E0107, E0277, E0599.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mSome errors have detailed explanations: E0061, E0107, E0277, E0599.\u001b[0m\n"}
{"$message_type":"diagnostic","message":"For more information about an error, try `rustc --explain E0061`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about an error, try `rustc --explain E0061`.\u001b[0m\n"}
