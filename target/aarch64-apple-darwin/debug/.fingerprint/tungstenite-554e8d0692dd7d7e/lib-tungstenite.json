{"rustc": 657458298742096754, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\", \"url\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 13609099840216434247, "profile": 3797293754785534760, "path": 11987633499056755531, "deps": [[504931904268503175, "http", false, 934683072299825957], [523978686976814572, "data_encoding", false, 13722620345731793933], [1632138251998637657, "httparse", false, 5384773269534446801], [2543237566893615891, "bytes", false, 5602759987719946778], [5910892534286594076, "rand", false, 16728303341635495276], [6388073677788589347, "utf8", false, 16729468555605294980], [8252504589640438155, "sha1", false, 705136138388767469], [8926101378076943148, "byteorder", false, 11443564875887011076], [11266840602298992523, "thiserror", false, 17844283191937953560], [15399619262696441677, "log", false, 12197920241697778201], [18130989770956114225, "url", false, 5399618054563088784]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/tungstenite-554e8d0692dd7d7e/dep-lib-tungstenite", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 10010679548380497168, "config": 2202906307356721367, "compile_kind": 14493662952515048101}