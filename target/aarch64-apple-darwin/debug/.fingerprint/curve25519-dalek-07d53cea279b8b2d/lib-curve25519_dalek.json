{"rustc": 657458298742096754, "features": "[\"alloc\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"avx2_backend\", \"default\", \"fiat-crypto\", \"fiat_u32_backend\", \"fiat_u64_backend\", \"nightly\", \"packed_simd\", \"serde\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 701098235721206363, "profile": 4374887572363265115, "path": 15176986393061851946, "deps": [[1486664334664968274, "subtle", false, 6214705429943545257], [8926101378076943148, "byteorder", false, 8497819907663012272], [11761531122794857361, "rand_core", false, 13375723406305734458], [12829070662860118430, "digest", false, 17495627696540224030], [16255406213544131105, "zeroize", false, 4448680748894229927]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/curve25519-dalek-07d53cea279b8b2d/dep-lib-curve25519_dalek", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 4666180542459884904, "config": 2202906307356721367, "compile_kind": 14493662952515048101}