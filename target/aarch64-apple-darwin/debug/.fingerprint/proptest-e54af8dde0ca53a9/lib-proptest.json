{"rustc": 657458298742096754, "features": "[\"bit-set\", \"default\", \"fork\", \"lazy_static\", \"regex-syntax\", \"rusty-fork\", \"std\", \"tempfile\", \"timeout\"]", "declared_features": "[\"alloc\", \"atomic64bit\", \"attr-macro\", \"bit-set\", \"default\", \"default-code-coverage\", \"fork\", \"handle-panics\", \"hardware-rng\", \"lazy_static\", \"no_std\", \"proptest-macro\", \"regex-syntax\", \"rusty-fork\", \"std\", \"tempfile\", \"timeout\", \"unstable\", \"x86\"]", "target": 4236691149125048855, "profile": 4374887572363265115, "path": 5567726862407015972, "deps": [[1797930717222993868, "rand_xorshift", false, 6962383015267139735], [4365100477378741705, "bit_set", false, 3711715369127118692], [4535781288325395887, "unarray", false, 5603242939819732334], [4722402946104583944, "bitflags", false, 2165309080323476574], [5910892534286594076, "rand", false, 9471981064599548653], [6166007883050419165, "bit_vec", false, 857056519291609627], [9111760993595911334, "regex_syntax", false, 204510617391570475], [10302351941801334405, "tempfile", false, 15306080714106510773], [10448766010662481490, "num_traits", false, 14280259535752585738], [11852147291591572288, "lazy_static", false, 9742125527777654707], [12017018019769837221, "rand_chacha", false, 17642291794005952372], [12694360097883174558, "rusty_fork", false, 5939434444646316537]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/proptest-e54af8dde0ca53a9/dep-lib-proptest", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 6696697305191021729, "config": 2202906307356721367, "compile_kind": 14493662952515048101}