{"rustc": 657458298742096754, "features": "[\"cors\", \"default\", \"trace\", \"tracing\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 5946479460708793394, "profile": 3797293754785534760, "path": 770683400442405230, "deps": [[504931904268503175, "http", false, 934683072299825957], [1232724694990601665, "http_range_header", false, 15695376779058383615], [2543237566893615891, "bytes", false, 5602759987719946778], [4722402946104583944, "bitflags", false, 16444296716632961664], [5204382251033773414, "tower_service", false, 15705307474958358534], [9396302785578940539, "futures_core", false, 8090229307551885060], [10821342338875855840, "tower_layer", false, 1060884011685702136], [11809678037142197677, "pin_project_lite", false, 11147781824329188078], [13606258873719457095, "http_body", false, 13877136918882320815], [16132118061651035107, "tracing", false, 1632409348248456988], [16476303074998891276, "futures_util", false, 15119982687840391673]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/tower-http-379ec3de68866814/dep-lib-tower_http", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 6562849041221450444, "config": 2202906307356721367, "compile_kind": 14493662952515048101}