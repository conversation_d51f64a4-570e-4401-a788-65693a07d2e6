{"rustc": 657458298742096754, "features": "[\"alloc\", \"getrandom\", \"getrandom_package\", \"libc\", \"std\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"getrandom_package\", \"libc\", \"log\", \"nightly\", \"packed_simd\", \"rand_pcg\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"stdweb\", \"wasm-bindgen\"]", "target": 721237385257707553, "profile": 4374887572363265115, "path": 6838059287388725002, "deps": [[6453573393678185459, "getrandom_package", false, 2540465261905909688], [7762067171913260472, "libc", false, 6996105985488277489], [11761531122794857361, "rand_core", false, 13375723406305734458], [15629295216311830669, "rand_chacha", false, 17716715068144333138]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/rand-cd2e14c455b9c36a/dep-lib-rand", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 7119169968661360791, "config": 2202906307356721367, "compile_kind": 14493662952515048101}