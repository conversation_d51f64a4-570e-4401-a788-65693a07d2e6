{"rustc": 657458298742096754, "features": "[\"default\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"arbitrary\", \"default\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "target": 4336297695352704405, "profile": 4374887572363265115, "path": 2203645061096561020, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/regex-syntax-95bdde193224f145/dep-lib-regex_syntax", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 17586400164587752172, "config": 2202906307356721367, "compile_kind": 14493662952515048101}