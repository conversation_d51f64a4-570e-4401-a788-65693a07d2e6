{"rustc": 657458298742096754, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 9156206050283006199, "profile": 8470298568473545773, "path": 17197901064513482735, "deps": [[3735647485472055247, "thread_local", false, 9746669218297624467], [5310583321894612108, "tracing_serde", false, 7618200857810778457], [8244776183334334055, "once_cell", false, 17236654666591032151], [8973061845687057626, "smallvec", false, 3550098765173168852], [10633404241517405153, "serde", false, 7420612406277754670], [11641382387439738731, "regex", false, 11609625493149959071], [11998755268370809021, "nu_ansi_term", false, 3761546794691100798], [12509852874546367857, "serde_json", false, 797747138307893778], [12679427474704493495, "matchers", false, 11688674210546621964], [13909326142996790163, "tracing_log", false, 9684568099039104744], [15515836537549001135, "tracing_core", false, 13697376408948604024], [16132118061651035107, "tracing", false, 8020496456261047502], [16405267689229882368, "sharded_slab", false, 5623732761503709085]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/tracing-subscriber-149ca93ee2cd6952/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 12822423491602284083, "config": 2202906307356721367, "compile_kind": 14493662952515048101}