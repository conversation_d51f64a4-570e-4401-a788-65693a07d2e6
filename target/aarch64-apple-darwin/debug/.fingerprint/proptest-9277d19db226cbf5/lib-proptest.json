{"rustc": 657458298742096754, "features": "[\"bit-set\", \"default\", \"fork\", \"lazy_static\", \"regex-syntax\", \"rusty-fork\", \"std\", \"tempfile\", \"timeout\"]", "declared_features": "[\"alloc\", \"atomic64bit\", \"attr-macro\", \"bit-set\", \"default\", \"default-code-coverage\", \"fork\", \"handle-panics\", \"hardware-rng\", \"lazy_static\", \"no_std\", \"proptest-macro\", \"regex-syntax\", \"rusty-fork\", \"std\", \"tempfile\", \"timeout\", \"unstable\", \"x86\"]", "target": 4236691149125048855, "profile": 3797293754785534760, "path": 5567726862407015972, "deps": [[1797930717222993868, "rand_xorshift", false, 6156918634084726353], [4365100477378741705, "bit_set", false, 9807172411474658114], [4535781288325395887, "unarray", false, 7722531288058235288], [4722402946104583944, "bitflags", false, 16444296716632961664], [5910892534286594076, "rand", false, 16728303341635495276], [6166007883050419165, "bit_vec", false, 10612930892206970025], [9111760993595911334, "regex_syntax", false, 1979924892068389192], [10302351941801334405, "tempfile", false, 14734457898899307101], [10448766010662481490, "num_traits", false, 1540199129515824993], [11852147291591572288, "lazy_static", false, 9562908401030502352], [12017018019769837221, "rand_chacha", false, 14209115560087020315], [12694360097883174558, "rusty_fork", false, 12780400846879393307]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/proptest-9277d19db226cbf5/dep-lib-proptest", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 6696697305191021729, "config": 2202906307356721367, "compile_kind": 14493662952515048101}