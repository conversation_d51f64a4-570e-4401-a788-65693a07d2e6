{"rustc": 657458298742096754, "features": "[\"default\", \"form\", \"http1\", \"json\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"ws\"]", "declared_features": "[\"__private_docs\", \"default\", \"form\", \"headers\", \"http1\", \"http2\", \"json\", \"macros\", \"matched-path\", \"multipart\", \"original-uri\", \"query\", \"tokio\", \"tower-log\", \"tracing\", \"ws\"]", "target": 2272233468398073248, "profile": 3797293754785534760, "path": 8149203148243359886, "deps": [[504931904268503175, "http", false, 934683072299825957], [554324495028472449, "memchr", false, 11051615246840488307], [902078471074753561, "async_trait", false, 4723537573814239992], [1011640204279865735, "base64", false, 7550504653927168840], [2070739116102306658, "tokio", false, 18264876576641677452], [2543237566893615891, "bytes", false, 5602759987719946778], [3930354675071354477, "percent_encoding", false, 15210865518228196295], [5204382251033773414, "tower_service", false, 15705307474958358534], [5402984160842549810, "build_script_build", false, 6343845230439266056], [5641730196288390710, "tokio_tungstenite", false, 18263431191773834107], [7470442545028885647, "mime", false, 3756144485891883700], [8252504589640438155, "sha1", false, 705136138388767469], [9433891360627596617, "serde_path_to_error", false, 16000923995223499017], [10633404241517405153, "serde", false, 17377261056403478855], [10821342338875855840, "tower_layer", false, 1060884011685702136], [11070927463981346568, "axum_core", false, 11209610653744703697], [11809678037142197677, "pin_project_lite", false, 11147781824329188078], [11995922566983883800, "tower", false, 8451309812214698602], [12509852874546367857, "serde_json", false, 9655182104837793913], [13606258873719457095, "http_body", false, 13877136918882320815], [14051957667571541382, "bitflags", false, 13811998120233463007], [14446744633799657975, "matchit", false, 11894103554554569659], [14663280588845858595, "itoa", false, 257244038882107002], [14796620158950075325, "hyper", false, 10590038958787051411], [15255313314640684218, "sync_wrapper", false, 18088887942789420202], [15501288286569156197, "serde_urlencoded", false, 8030363062554884176], [16005103308121562836, "multer", false, 8932449665524893870], [16476303074998891276, "futures_util", false, 15119982687840391673]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/axum-b3d2c38d8ec1d807/dep-lib-axum", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 17576717817111726285, "config": 2202906307356721367, "compile_kind": 14493662952515048101}