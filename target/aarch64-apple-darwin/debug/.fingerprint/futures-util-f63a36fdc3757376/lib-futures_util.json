{"rustc": 657458298742096754, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"channel\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"io\", \"memchr\", \"sink\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"portable-atomic\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 14098227409853078778, "profile": 16491756098474355655, "path": 9774881064298247872, "deps": [[461436706529125561, "futures_io", false, 9399740872366887197], [554324495028472449, "memchr", false, 13814225048810371401], [1910231660504989506, "futures_task", false, 10389562942305803143], [4761952582670444189, "pin_utils", false, 14383685071026805261], [5846781562065118163, "futures_channel", false, 11598994824850235611], [9396302785578940539, "futures_core", false, 4366664659718766829], [10080452282735337284, "futures_macro", false, 4587276383415371392], [11289432439818403777, "futures_sink", false, 385092885775113453], [11809678037142197677, "pin_project_lite", false, 16754346873387115223], [17040352472033410869, "slab", false, 11068591747843576456]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/futures-util-f63a36fdc3757376/dep-lib-futures_util", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 5677230335954518303, "config": 2202906307356721367, "compile_kind": 14493662952515048101}