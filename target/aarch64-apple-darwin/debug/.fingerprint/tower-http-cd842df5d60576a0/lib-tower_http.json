{"rustc": 657458298742096754, "features": "[\"cors\", \"default\", \"trace\", \"tracing\"]", "declared_features": "[\"add-extension\", \"async-compression\", \"auth\", \"base64\", \"catch-panic\", \"compression-br\", \"compression-deflate\", \"compression-full\", \"compression-gzip\", \"compression-zstd\", \"cors\", \"decompression-br\", \"decompression-deflate\", \"decompression-full\", \"decompression-gzip\", \"decompression-zstd\", \"default\", \"follow-redirect\", \"fs\", \"full\", \"httpdate\", \"iri-string\", \"limit\", \"map-request-body\", \"map-response-body\", \"metrics\", \"mime\", \"mime_guess\", \"normalize-path\", \"percent-encoding\", \"propagate-header\", \"redirect\", \"request-id\", \"sensitive-headers\", \"set-header\", \"set-status\", \"timeout\", \"tokio\", \"tokio-util\", \"tower\", \"trace\", \"tracing\", \"util\", \"uuid\", \"validate-request\"]", "target": 5946479460708793394, "profile": 4374887572363265115, "path": 770683400442405230, "deps": [[504931904268503175, "http", false, 15700821049539912839], [1232724694990601665, "http_range_header", false, 14995617828879962906], [2543237566893615891, "bytes", false, 11285958084381035690], [4722402946104583944, "bitflags", false, 2165309080323476574], [5204382251033773414, "tower_service", false, 3780885632544697223], [9396302785578940539, "futures_core", false, 4366664659718766829], [10821342338875855840, "tower_layer", false, 15813761809476068863], [11809678037142197677, "pin_project_lite", false, 16754346873387115223], [13606258873719457095, "http_body", false, 1775941832943516538], [16132118061651035107, "tracing", false, 8020496456261047502], [16476303074998891276, "futures_util", false, 14799717098828253815]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/tower-http-cd842df5d60576a0/dep-lib-tower_http", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 6562849041221450444, "config": 2202906307356721367, "compile_kind": 14493662952515048101}