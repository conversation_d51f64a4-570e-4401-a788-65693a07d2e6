{"rustc": 657458298742096754, "features": "[]", "declared_features": "[]", "target": 1962341611837544381, "profile": 3797293754785534760, "path": 15189957918497775029, "deps": [[902078471074753561, "async_trait", false, 4723537573814239992], [2070739116102306658, "tokio", false, 1796335879530522362], [6690072151007321369, "hyper", false, 3234024846777890179], [7009065068979121087, "http_body_util", false, 8732168627196586305], [8244776183334334055, "once_cell", false, 3280466444085169116], [9253677898334269643, "base64", false, 2204226201774530404], [10633404241517405153, "serde", false, 17377261056403478855], [10840051415924403621, "deadpool", false, 2775498584886836328], [11268000880778227980, "hyper_util", false, 6579395358711614282], [11641382387439738731, "regex", false, 16296215625359503609], [12368485582061518913, "http", false, 497234280895428565], [12509852874546367857, "serde_json", false, 9655182104837793913], [14788668256999697643, "assert_json_diff", false, 2468507218935638122], [15399619262696441677, "log", false, 12197920241697778201], [15447401961974210701, "futures", false, 5085402383360922164], [18130989770956114225, "url", false, 5399618054563088784]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/wiremock-82e5edd1aa01874c/dep-lib-wiremock", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 11829039582575452724, "config": 2202906307356721367, "compile_kind": 14493662952515048101}