{"rustc": 657458298742096754, "features": "[\"allocator-api2\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"raw-entry\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 16815627201824848041, "profile": 4374887572363265115, "path": 18386749064320751168, "deps": [[2289252893304123003, "allocator_api2", false, 11551785273085039232], [7657838323326184233, "equivalent", false, 11495024362396602983], [9298736802812612957, "<PERSON><PERSON><PERSON>", false, 8536634570647861904]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/hashbrown-fcc70704391e7129/dep-lib-hashbrown", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 6228333144549390726, "config": 2202906307356721367, "compile_kind": 14493662952515048101}