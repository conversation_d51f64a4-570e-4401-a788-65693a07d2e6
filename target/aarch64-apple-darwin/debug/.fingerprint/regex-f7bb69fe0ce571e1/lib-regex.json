{"rustc": 657458298742096754, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 9315256552433306347, "profile": 4374887572363265115, "path": 15317552000121998624, "deps": [[554324495028472449, "memchr", false, 13814225048810371401], [6314779025451150414, "regex_automata", false, 5677693950041143231], [7325384046744447800, "aho_corasick", false, 6953189399913496920], [9111760993595911334, "regex_syntax", false, 204510617391570475]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/regex-f7bb69fe0ce571e1/dep-lib-regex", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 3256615787768725874, "config": 2202906307356721367, "compile_kind": 14493662952515048101}