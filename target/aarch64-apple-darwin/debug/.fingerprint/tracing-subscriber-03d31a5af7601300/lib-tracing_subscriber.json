{"rustc": 657458298742096754, "features": "[\"alloc\", \"ansi\", \"default\", \"env-filter\", \"fmt\", \"json\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing\", \"tracing-log\", \"tracing-serde\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 9156206050283006199, "profile": 6778381136756202675, "path": 17197901064513482735, "deps": [[3735647485472055247, "thread_local", false, 12029342879250501773], [5310583321894612108, "tracing_serde", false, 13093782305597874248], [8244776183334334055, "once_cell", false, 3280466444085169116], [8973061845687057626, "smallvec", false, 12616594077090729748], [10633404241517405153, "serde", false, 17377261056403478855], [11641382387439738731, "regex", false, 3811561582223653649], [11998755268370809021, "nu_ansi_term", false, 9052106586729464807], [12509852874546367857, "serde_json", false, 9655182104837793913], [12679427474704493495, "matchers", false, 15788341549534263293], [13909326142996790163, "tracing_log", false, 8117426171052937127], [15515836537549001135, "tracing_core", false, 17627900620544371202], [16132118061651035107, "tracing", false, 1632409348248456988], [16405267689229882368, "sharded_slab", false, 1821898215603809841]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/tracing-subscriber-03d31a5af7601300/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 12822423491602284083, "config": 2202906307356721367, "compile_kind": 14493662952515048101}