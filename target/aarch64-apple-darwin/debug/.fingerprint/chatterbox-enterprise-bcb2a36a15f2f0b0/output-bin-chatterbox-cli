{"$message_type":"diagnostic","message":"unused imports: `Confirm`, `Input`, `Select`, and `theme::ColorfulTheme`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":445,"byte_end":465,"line_start":14,"line_end":14,"column_start":17,"column_end":37,"is_primary":true,"text":[{"text":"use dialoguer::{theme::ColorfulTheme, Confirm, Input, Select};","highlight_start":17,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/cli.rs","byte_start":467,"byte_end":474,"line_start":14,"line_end":14,"column_start":39,"column_end":46,"is_primary":true,"text":[{"text":"use dialoguer::{theme::ColorfulTheme, Confirm, Input, Select};","highlight_start":39,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/cli.rs","byte_start":476,"byte_end":481,"line_start":14,"line_end":14,"column_start":48,"column_end":53,"is_primary":true,"text":[{"text":"use dialoguer::{theme::ColorfulTheme, Confirm, Input, Select};","highlight_start":48,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/cli.rs","byte_start":483,"byte_end":489,"line_start":14,"line_end":14,"column_start":55,"column_end":61,"is_primary":true,"text":[{"text":"use dialoguer::{theme::ColorfulTheme, Confirm, Input, Select};","highlight_start":55,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/bin/cli.rs","byte_start":429,"byte_end":492,"line_start":14,"line_end":15,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use dialoguer::{theme::ColorfulTheme, Confirm, Input, Select};","highlight_start":1,"highlight_end":63},{"text":"use indicatif::{ProgressBar, ProgressStyle};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Confirm`, `Input`, `Select`, and `theme::ColorfulTheme`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:14:17\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse dialoguer::{theme::ColorfulTheme, Confirm, Input, Select};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `timestamps`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":13802,"byte_end":13812,"line_start":430,"line_end":430,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    timestamps: bool,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/bin/cli.rs","byte_start":13802,"byte_end":13812,"line_start":430,"line_end":430,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    timestamps: bool,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":"_timestamps","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `timestamps`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:430:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m430\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    timestamps: bool,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_timestamps`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `model_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":6745,"byte_end":6758,"line_start":271,"line_end":271,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let model_manager = Arc::new(ModelManager::new(Arc::new(config.clone())).await?);","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/bin/cli.rs","byte_start":6745,"byte_end":6758,"line_start":271,"line_end":271,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"    let model_manager = Arc::new(ModelManager::new(Arc::new(config.clone())).await?);","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_model_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `model_manager`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:271:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m271\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let model_manager = Arc::new(ModelManager::new(Arc::new(config.clone())).await?);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_model_manager`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `watermark_engine`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":6831,"byte_end":6847,"line_start":272,"line_end":272,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    let watermark_engine = Arc::new(WatermarkEngine::new(1.0)?);","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/bin/cli.rs","byte_start":6831,"byte_end":6847,"line_start":272,"line_end":272,"column_start":9,"column_end":25,"is_primary":true,"text":[{"text":"    let watermark_engine = Arc::new(WatermarkEngine::new(1.0)?);","highlight_start":9,"highlight_end":25}],"label":null,"suggested_replacement":"_watermark_engine","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `watermark_engine`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:272:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m272\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let watermark_engine = Arc::new(WatermarkEngine::new(1.0)?);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_watermark_engine`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `telemetry`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":6896,"byte_end":6905,"line_start":273,"line_end":273,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    let telemetry = Arc::new(TelemetryCollector::new());","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/bin/cli.rs","byte_start":6896,"byte_end":6905,"line_start":273,"line_end":273,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    let telemetry = Arc::new(TelemetryCollector::new());","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":"_telemetry","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `telemetry`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:273:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m273\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let telemetry = Arc::new(TelemetryCollector::new());\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_telemetry`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `filter`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":7932,"byte_end":7938,"line_start":299,"line_end":299,"column_start":28,"column_end":34,"is_primary":true,"text":[{"text":"        Commands::Models { filter, detailed } => {","highlight_start":28,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/bin/cli.rs","byte_start":7932,"byte_end":7938,"line_start":299,"line_end":299,"column_start":28,"column_end":34,"is_primary":true,"text":[{"text":"        Commands::Models { filter, detailed } => {","highlight_start":28,"highlight_end":34}],"label":null,"suggested_replacement":"filter: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `filter`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:299:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m299\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Commands::Models { filter, detailed } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `filter: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `detailed`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":7940,"byte_end":7948,"line_start":299,"line_end":299,"column_start":36,"column_end":44,"is_primary":true,"text":[{"text":"        Commands::Models { filter, detailed } => {","highlight_start":36,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/bin/cli.rs","byte_start":7940,"byte_end":7948,"line_start":299,"line_end":299,"column_start":36,"column_end":44,"is_primary":true,"text":[{"text":"        Commands::Models { filter, detailed } => {","highlight_start":36,"highlight_end":44}],"label":null,"suggested_replacement":"detailed: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `detailed`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:299:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m299\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Commands::Models { filter, detailed } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `detailed: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `detailed`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":8054,"byte_end":8062,"line_start":302,"line_end":302,"column_start":28,"column_end":36,"is_primary":true,"text":[{"text":"        Commands::Status { detailed, watch, interval } => {","highlight_start":28,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/bin/cli.rs","byte_start":8054,"byte_end":8062,"line_start":302,"line_end":302,"column_start":28,"column_end":36,"is_primary":true,"text":[{"text":"        Commands::Status { detailed, watch, interval } => {","highlight_start":28,"highlight_end":36}],"label":null,"suggested_replacement":"detailed: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `detailed`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:302:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m302\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Commands::Status { detailed, watch, interval } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `detailed: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `watch`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":8064,"byte_end":8069,"line_start":302,"line_end":302,"column_start":38,"column_end":43,"is_primary":true,"text":[{"text":"        Commands::Status { detailed, watch, interval } => {","highlight_start":38,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/bin/cli.rs","byte_start":8064,"byte_end":8069,"line_start":302,"line_end":302,"column_start":38,"column_end":43,"is_primary":true,"text":[{"text":"        Commands::Status { detailed, watch, interval } => {","highlight_start":38,"highlight_end":43}],"label":null,"suggested_replacement":"watch: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `watch`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:302:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m302\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Commands::Status { detailed, watch, interval } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `watch: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `interval`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":8071,"byte_end":8079,"line_start":302,"line_end":302,"column_start":45,"column_end":53,"is_primary":true,"text":[{"text":"        Commands::Status { detailed, watch, interval } => {","highlight_start":45,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/bin/cli.rs","byte_start":8071,"byte_end":8079,"line_start":302,"line_end":302,"column_start":45,"column_end":53,"is_primary":true,"text":[{"text":"        Commands::Status { detailed, watch, interval } => {","highlight_start":45,"highlight_end":53}],"label":null,"suggested_replacement":"interval: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `interval`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:302:45\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m302\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Commands::Status { detailed, watch, interval } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `interval: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `input`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":8184,"byte_end":8189,"line_start":305,"line_end":305,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"        Commands::Batch { input, output, workers } => {","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/bin/cli.rs","byte_start":8184,"byte_end":8189,"line_start":305,"line_end":305,"column_start":27,"column_end":32,"is_primary":true,"text":[{"text":"        Commands::Batch { input, output, workers } => {","highlight_start":27,"highlight_end":32}],"label":null,"suggested_replacement":"input: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `input`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:305:27\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Commands::Batch { input, output, workers } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `input: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `output`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":8191,"byte_end":8197,"line_start":305,"line_end":305,"column_start":34,"column_end":40,"is_primary":true,"text":[{"text":"        Commands::Batch { input, output, workers } => {","highlight_start":34,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/bin/cli.rs","byte_start":8191,"byte_end":8197,"line_start":305,"line_end":305,"column_start":34,"column_end":40,"is_primary":true,"text":[{"text":"        Commands::Batch { input, output, workers } => {","highlight_start":34,"highlight_end":40}],"label":null,"suggested_replacement":"output: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `output`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:305:34\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Commands::Batch { input, output, workers } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `output: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `workers`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":8199,"byte_end":8206,"line_start":305,"line_end":305,"column_start":42,"column_end":49,"is_primary":true,"text":[{"text":"        Commands::Batch { input, output, workers } => {","highlight_start":42,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/bin/cli.rs","byte_start":8199,"byte_end":8206,"line_start":305,"line_end":305,"column_start":42,"column_end":49,"is_primary":true,"text":[{"text":"        Commands::Batch { input, output, workers } => {","highlight_start":42,"highlight_end":49}],"label":null,"suggested_replacement":"workers: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `workers`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:305:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Commands::Batch { input, output, workers } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `workers: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `test_type`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":8413,"byte_end":8422,"line_start":311,"line_end":311,"column_start":31,"column_end":40,"is_primary":true,"text":[{"text":"        Commands::Benchmark { test_type, iterations, concurrent } => {","highlight_start":31,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/bin/cli.rs","byte_start":8413,"byte_end":8422,"line_start":311,"line_end":311,"column_start":31,"column_end":40,"is_primary":true,"text":[{"text":"        Commands::Benchmark { test_type, iterations, concurrent } => {","highlight_start":31,"highlight_end":40}],"label":null,"suggested_replacement":"test_type: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `test_type`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:311:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m311\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Commands::Benchmark { test_type, iterations, concurrent } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `test_type: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `iterations`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":8424,"byte_end":8434,"line_start":311,"line_end":311,"column_start":42,"column_end":52,"is_primary":true,"text":[{"text":"        Commands::Benchmark { test_type, iterations, concurrent } => {","highlight_start":42,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/bin/cli.rs","byte_start":8424,"byte_end":8434,"line_start":311,"line_end":311,"column_start":42,"column_end":52,"is_primary":true,"text":[{"text":"        Commands::Benchmark { test_type, iterations, concurrent } => {","highlight_start":42,"highlight_end":52}],"label":null,"suggested_replacement":"iterations: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `iterations`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:311:42\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m311\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Commands::Benchmark { test_type, iterations, concurrent } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `iterations: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `concurrent`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":8436,"byte_end":8446,"line_start":311,"line_end":311,"column_start":54,"column_end":64,"is_primary":true,"text":[{"text":"        Commands::Benchmark { test_type, iterations, concurrent } => {","highlight_start":54,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"try ignoring the field","code":null,"level":"help","spans":[{"file_name":"src/bin/cli.rs","byte_start":8436,"byte_end":8446,"line_start":311,"line_end":311,"column_start":54,"column_end":64,"is_primary":true,"text":[{"text":"        Commands::Benchmark { test_type, iterations, concurrent } => {","highlight_start":54,"highlight_end":64}],"label":null,"suggested_replacement":"concurrent: _","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `concurrent`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:311:54\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m311\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Commands::Benchmark { test_type, iterations, concurrent } => {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try ignoring the field: `concurrent: _`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"struct `ModelInfo` is never constructed","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":5586,"byte_end":5595,"line_start":222,"line_end":222,"column_start":8,"column_end":17,"is_primary":true,"text":[{"text":"struct ModelInfo {","highlight_start":8,"highlight_end":17}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: struct `ModelInfo` is never constructed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:222:8\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m222\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct ModelInfo {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for the crate","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/cli.rs","byte_start":0,"byte_end":27145,"line_start":1,"line_end":703,"column_start":1,"column_end":2,"is_primary":true,"text":[{"text":"use chatterbox_enterprise::{","highlight_start":1,"highlight_end":29},{"text":"    accessibility::{AccessibilityManager, AccessibilityConfig, ConformanceLevel, InteractiveMode},","highlight_start":1,"highlight_end":99},{"text":"    config::{Config, AudioFormat},","highlight_start":1,"highlight_end":35},{"text":"    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},","highlight_start":1,"highlight_end":70},{"text":"    audio::AudioData,","highlight_start":1,"highlight_end":22},{"text":"    models::ModelManager,","highlight_start":1,"highlight_end":26},{"text":"    security::WatermarkEngine,","highlight_start":1,"highlight_end":31},{"text":"    telemetry::TelemetryCollector,","highlight_start":1,"highlight_end":35},{"text":"    Result,","highlight_start":1,"highlight_end":12},{"text":"};","highlight_start":1,"highlight_end":3},{"text":"use clap::{Parser, Subcommand};","highlight_start":1,"highlight_end":32},{"text":"use colored::*;","highlight_start":1,"highlight_end":16},{"text":"use console::Term;","highlight_start":1,"highlight_end":19},{"text":"use dialoguer::{theme::ColorfulTheme, Confirm, Input, Select};","highlight_start":1,"highlight_end":63},{"text":"use indicatif::{ProgressBar, ProgressStyle};","highlight_start":1,"highlight_end":45},{"text":"use std::fs;","highlight_start":1,"highlight_end":13},{"text":"use std::path::PathBuf;","highlight_start":1,"highlight_end":24},{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":20},{"text":"use std::time::Instant;","highlight_start":1,"highlight_end":24},{"text":"use tabled::{Table, Tabled};","highlight_start":1,"highlight_end":29},{"text":"use tokio;","highlight_start":1,"highlight_end":11},{"text":"","highlight_start":1,"highlight_end":1},{"text":"/// Chatterbox Enterprise CLI - Professional TTS/STT Command Line Interface","highlight_start":1,"highlight_end":76},{"text":"#[derive(Parser)]","highlight_start":1,"highlight_end":18},{"text":"#[command(name = \"chatterbox-cli\")]","highlight_start":1,"highlight_end":36},{"text":"#[command(about = \"Enterprise-grade Text-to-Speech and Speech-to-Text CLI\")]","highlight_start":1,"highlight_end":77},{"text":"#[command(version = \"1.0.0\")]","highlight_start":1,"highlight_end":30},{"text":"#[command(author = \"Chatterbox Enterprise\")]","highlight_start":1,"highlight_end":45},{"text":"struct Cli {","highlight_start":1,"highlight_end":13},{"text":"    /// Configuration file path","highlight_start":1,"highlight_end":32},{"text":"    #[arg(short, long, default_value = \"config/config.toml\")]","highlight_start":1,"highlight_end":62},{"text":"    config: PathBuf,","highlight_start":1,"highlight_end":21},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Verbose output","highlight_start":1,"highlight_end":23},{"text":"    #[arg(short, long)]","highlight_start":1,"highlight_end":24},{"text":"    verbose: bool,","highlight_start":1,"highlight_end":19},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Output format (json, table, plain)","highlight_start":1,"highlight_end":43},{"text":"    #[arg(short, long, default_value = \"table\")]","highlight_start":1,"highlight_end":49},{"text":"    format: String,","highlight_start":1,"highlight_end":20},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    #[command(subcommand)]","highlight_start":1,"highlight_end":27},{"text":"    command: Commands,","highlight_start":1,"highlight_end":23},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"#[derive(Subcommand)]","highlight_start":1,"highlight_end":22},{"text":"enum Commands {","highlight_start":1,"highlight_end":16},{"text":"    /// Text-to-Speech synthesis","highlight_start":1,"highlight_end":33},{"text":"    Tts {","highlight_start":1,"highlight_end":10},{"text":"        /// Text to synthesize","highlight_start":1,"highlight_end":31},{"text":"        #[arg(short, long)]","highlight_start":1,"highlight_end":28},{"text":"        text: Option<String>,","highlight_start":1,"highlight_end":30},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Voice model to use","highlight_start":1,"highlight_end":31},{"text":"        #[arg(short, long, default_value = \"en-us-female-1\")]","highlight_start":1,"highlight_end":62},{"text":"        voice: String,","highlight_start":1,"highlight_end":23},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Output audio file","highlight_start":1,"highlight_end":30},{"text":"        #[arg(short, long, default_value = \"output.wav\")]","highlight_start":1,"highlight_end":58},{"text":"        output: PathBuf,","highlight_start":1,"highlight_end":25},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Speaking rate (0.5-2.0)","highlight_start":1,"highlight_end":36},{"text":"        #[arg(long, default_value = \"1.0\")]","highlight_start":1,"highlight_end":44},{"text":"        rate: f32,","highlight_start":1,"highlight_end":19},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Pitch adjustment (-1.0 to 1.0)","highlight_start":1,"highlight_end":43},{"text":"        #[arg(long, default_value = \"0.0\")]","highlight_start":1,"highlight_end":44},{"text":"        pitch: f32,","highlight_start":1,"highlight_end":20},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Volume (0.0-2.0)","highlight_start":1,"highlight_end":29},{"text":"        #[arg(long, default_value = \"1.0\")]","highlight_start":1,"highlight_end":44},{"text":"        volume: f32,","highlight_start":1,"highlight_end":21},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Emotion (neutral, happy, sad, angry, excited)","highlight_start":1,"highlight_end":58},{"text":"        #[arg(long)]","highlight_start":1,"highlight_end":21},{"text":"        emotion: Option<String>,","highlight_start":1,"highlight_end":33},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Apply watermark","highlight_start":1,"highlight_end":28},{"text":"        #[arg(long)]","highlight_start":1,"highlight_end":21},{"text":"        watermark: bool,","highlight_start":1,"highlight_end":25},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Interactive mode","highlight_start":1,"highlight_end":29},{"text":"        #[arg(short, long)]","highlight_start":1,"highlight_end":28},{"text":"        interactive: bool,","highlight_start":1,"highlight_end":27},{"text":"    },","highlight_start":1,"highlight_end":7},{"text":"    /// Speech-to-Text recognition","highlight_start":1,"highlight_end":35},{"text":"    Stt {","highlight_start":1,"highlight_end":10},{"text":"        /// Input audio file","highlight_start":1,"highlight_end":29},{"text":"        #[arg(short, long)]","highlight_start":1,"highlight_end":28},{"text":"        input: PathBuf,","highlight_start":1,"highlight_end":24},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Language code (e.g., en-US, es-ES)","highlight_start":1,"highlight_end":47},{"text":"        #[arg(short, long)]","highlight_start":1,"highlight_end":28},{"text":"        language: Option<String>,","highlight_start":1,"highlight_end":34},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Output text file","highlight_start":1,"highlight_end":29},{"text":"        #[arg(short, long)]","highlight_start":1,"highlight_end":28},{"text":"        output: Option<PathBuf>,","highlight_start":1,"highlight_end":33},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Enable punctuation","highlight_start":1,"highlight_end":31},{"text":"        #[arg(long)]","highlight_start":1,"highlight_end":21},{"text":"        punctuation: bool,","highlight_start":1,"highlight_end":27},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Confidence threshold (0.0-1.0)","highlight_start":1,"highlight_end":43},{"text":"        #[arg(long, default_value = \"0.5\")]","highlight_start":1,"highlight_end":44},{"text":"        confidence: f32,","highlight_start":1,"highlight_end":25},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Enable word timestamps","highlight_start":1,"highlight_end":35},{"text":"        #[arg(long)]","highlight_start":1,"highlight_end":21},{"text":"        timestamps: bool,","highlight_start":1,"highlight_end":26},{"text":"    },","highlight_start":1,"highlight_end":7},{"text":"    /// List available models","highlight_start":1,"highlight_end":30},{"text":"    Models {","highlight_start":1,"highlight_end":13},{"text":"        /// Model type filter (tts, stt, all)","highlight_start":1,"highlight_end":46},{"text":"        #[arg(short, long, default_value = \"all\")]","highlight_start":1,"highlight_end":51},{"text":"        filter: String,","highlight_start":1,"highlight_end":24},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Show detailed information","highlight_start":1,"highlight_end":38},{"text":"        #[arg(short, long)]","highlight_start":1,"highlight_end":28},{"text":"        detailed: bool,","highlight_start":1,"highlight_end":24},{"text":"    },","highlight_start":1,"highlight_end":7},{"text":"    /// System health and status","highlight_start":1,"highlight_end":33},{"text":"    Status {","highlight_start":1,"highlight_end":13},{"text":"        /// Show detailed metrics","highlight_start":1,"highlight_end":34},{"text":"        #[arg(short, long)]","highlight_start":1,"highlight_end":28},{"text":"        detailed: bool,","highlight_start":1,"highlight_end":24},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Continuous monitoring","highlight_start":1,"highlight_end":34},{"text":"        #[arg(short, long)]","highlight_start":1,"highlight_end":28},{"text":"        watch: bool,","highlight_start":1,"highlight_end":21},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Refresh interval in seconds","highlight_start":1,"highlight_end":40},{"text":"        #[arg(long, default_value = \"5\")]","highlight_start":1,"highlight_end":42},{"text":"        interval: u64,","highlight_start":1,"highlight_end":23},{"text":"    },","highlight_start":1,"highlight_end":7},{"text":"    /// Batch processing","highlight_start":1,"highlight_end":25},{"text":"    Batch {","highlight_start":1,"highlight_end":12},{"text":"        /// Input file with commands","highlight_start":1,"highlight_end":37},{"text":"        #[arg(short, long)]","highlight_start":1,"highlight_end":28},{"text":"        input: PathBuf,","highlight_start":1,"highlight_end":24},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Output directory","highlight_start":1,"highlight_end":29},{"text":"        #[arg(short, long, default_value = \"batch_output\")]","highlight_start":1,"highlight_end":60},{"text":"        output: PathBuf,","highlight_start":1,"highlight_end":25},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Number of parallel workers","highlight_start":1,"highlight_end":39},{"text":"        #[arg(short, long, default_value = \"4\")]","highlight_start":1,"highlight_end":49},{"text":"        workers: usize,","highlight_start":1,"highlight_end":24},{"text":"    },","highlight_start":1,"highlight_end":7},{"text":"    /// Interactive shell","highlight_start":1,"highlight_end":26},{"text":"    Shell,","highlight_start":1,"highlight_end":11},{"text":"    /// Benchmark performance","highlight_start":1,"highlight_end":30},{"text":"    Benchmark {","highlight_start":1,"highlight_end":16},{"text":"        /// Test type (tts, stt, both)","highlight_start":1,"highlight_end":39},{"text":"        #[arg(short, long, default_value = \"both\")]","highlight_start":1,"highlight_end":52},{"text":"        test_type: String,","highlight_start":1,"highlight_end":27},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Number of iterations","highlight_start":1,"highlight_end":33},{"text":"        #[arg(short, long, default_value = \"10\")]","highlight_start":1,"highlight_end":50},{"text":"        iterations: usize,","highlight_start":1,"highlight_end":27},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Concurrent requests","highlight_start":1,"highlight_end":32},{"text":"        #[arg(short, long, default_value = \"1\")]","highlight_start":1,"highlight_end":49},{"text":"        concurrent: usize,","highlight_start":1,"highlight_end":27},{"text":"    },","highlight_start":1,"highlight_end":7},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Accessibility features and configuration","highlight_start":1,"highlight_end":49},{"text":"    Accessibility {","highlight_start":1,"highlight_end":20},{"text":"        #[command(subcommand)]","highlight_start":1,"highlight_end":31},{"text":"        command: AccessibilityCommand,","highlight_start":1,"highlight_end":39},{"text":"    },","highlight_start":1,"highlight_end":7},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"/// Accessibility subcommands","highlight_start":1,"highlight_end":30},{"text":"#[derive(Subcommand)]","highlight_start":1,"highlight_end":22},{"text":"enum AccessibilityCommand {","highlight_start":1,"highlight_end":28},{"text":"    /// Configure accessibility settings","highlight_start":1,"highlight_end":41},{"text":"    Configure {","highlight_start":1,"highlight_end":16},{"text":"        /// WCAG conformance level (bronze, silver, gold)","highlight_start":1,"highlight_end":58},{"text":"        #[arg(short, long, default_value = \"silver\")]","highlight_start":1,"highlight_end":54},{"text":"        level: String,","highlight_start":1,"highlight_end":23},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Enable voice control","highlight_start":1,"highlight_end":33},{"text":"        #[arg(long)]","highlight_start":1,"highlight_end":21},{"text":"        voice_control: bool,","highlight_start":1,"highlight_end":29},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Enable eye tracking","highlight_start":1,"highlight_end":32},{"text":"        #[arg(long)]","highlight_start":1,"highlight_end":21},{"text":"        eye_tracking: bool,","highlight_start":1,"highlight_end":28},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Enable gesture recognition","highlight_start":1,"highlight_end":39},{"text":"        #[arg(long)]","highlight_start":1,"highlight_end":21},{"text":"        gestures: bool,","highlight_start":1,"highlight_end":24},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Minimum contrast ratio","highlight_start":1,"highlight_end":35},{"text":"        #[arg(long, default_value = \"4.5\")]","highlight_start":1,"highlight_end":44},{"text":"        contrast_ratio: f64,","highlight_start":1,"highlight_end":29},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        /// Font size multiplier","highlight_start":1,"highlight_end":33},{"text":"        #[arg(long, default_value = \"1.0\")]","highlight_start":1,"highlight_end":44},{"text":"        font_size: f64,","highlight_start":1,"highlight_end":24},{"text":"    },","highlight_start":1,"highlight_end":7},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Switch interactive mode","highlight_start":1,"highlight_end":32},{"text":"    Mode {","highlight_start":1,"highlight_end":11},{"text":"        /// Interactive mode (standard, voice, eye-tracking, gesture, switch)","highlight_start":1,"highlight_end":78},{"text":"        mode: String,","highlight_start":1,"highlight_end":22},{"text":"    },","highlight_start":1,"highlight_end":7},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Test accessibility features","highlight_start":1,"highlight_end":36},{"text":"    Test {","highlight_start":1,"highlight_end":11},{"text":"        /// Feature to test (voice, eye-tracking, gesture, contrast, all)","highlight_start":1,"highlight_end":74},{"text":"        #[arg(default_value = \"all\")]","highlight_start":1,"highlight_end":38},{"text":"        feature: String,","highlight_start":1,"highlight_end":25},{"text":"    },","highlight_start":1,"highlight_end":7},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    /// Show accessibility status","highlight_start":1,"highlight_end":34},{"text":"    Status,","highlight_start":1,"highlight_end":12},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"#[derive(Tabled)]","highlight_start":1,"highlight_end":18},{"text":"struct ModelInfo {","highlight_start":1,"highlight_end":19},{"text":"    name: String,","highlight_start":1,"highlight_end":18},{"text":"    model_type: String,","highlight_start":1,"highlight_end":24},{"text":"    language: String,","highlight_start":1,"highlight_end":22},{"text":"    status: String,","highlight_start":1,"highlight_end":20},{"text":"    size: String,","highlight_start":1,"highlight_end":18},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"#[derive(Tabled)]","highlight_start":1,"highlight_end":18},{"text":"struct SystemMetric {","highlight_start":1,"highlight_end":22},{"text":"    metric: String,","highlight_start":1,"highlight_end":20},{"text":"    value: String,","highlight_start":1,"highlight_end":19},{"text":"    status: String,","highlight_start":1,"highlight_end":20},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"#[tokio::main]","highlight_start":1,"highlight_end":15},{"text":"async fn main() -> Result<()> {","highlight_start":1,"highlight_end":32},{"text":"    let cli = Cli::parse();","highlight_start":1,"highlight_end":28},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Initialize logging","highlight_start":1,"highlight_end":26},{"text":"    if cli.verbose {","highlight_start":1,"highlight_end":21},{"text":"        tracing_subscriber::fmt()","highlight_start":1,"highlight_end":34},{"text":"            .with_env_filter(\"debug\")","highlight_start":1,"highlight_end":38},{"text":"            .init();","highlight_start":1,"highlight_end":21},{"text":"    } else {","highlight_start":1,"highlight_end":13},{"text":"        tracing_subscriber::fmt()","highlight_start":1,"highlight_end":34},{"text":"            .with_env_filter(\"info\")","highlight_start":1,"highlight_end":37},{"text":"            .init();","highlight_start":1,"highlight_end":21},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Print banner","highlight_start":1,"highlight_end":20},{"text":"    print_banner();","highlight_start":1,"highlight_end":20},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Load configuration","highlight_start":1,"highlight_end":26},{"text":"    let config = if cli.config.exists() {","highlight_start":1,"highlight_end":42},{"text":"        Config::from_env()?","highlight_start":1,"highlight_end":28},{"text":"    } else {","highlight_start":1,"highlight_end":13},{"text":"        println!(\"{}\", \"⚠️  Using default configuration (config file not found)\".yellow());","highlight_start":1,"highlight_end":92},{"text":"        Config::default()","highlight_start":1,"highlight_end":26},{"text":"    };","highlight_start":1,"highlight_end":7},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Initialize engine","highlight_start":1,"highlight_end":25},{"text":"    let spinner = ProgressBar::new_spinner();","highlight_start":1,"highlight_end":46},{"text":"    spinner.set_style(ProgressStyle::default_spinner()","highlight_start":1,"highlight_end":55},{"text":"        .template(\"{spinner:.green} {msg}\")","highlight_start":1,"highlight_end":44},{"text":"        .unwrap());","highlight_start":1,"highlight_end":20},{"text":"    spinner.set_message(\"Initializing Chatterbox Engine...\");","highlight_start":1,"highlight_end":62},{"text":"    spinner.enable_steady_tick(std::time::Duration::from_millis(100));","highlight_start":1,"highlight_end":71},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    let model_manager = Arc::new(ModelManager::new(Arc::new(config.clone())).await?);","highlight_start":1,"highlight_end":86},{"text":"    let watermark_engine = Arc::new(WatermarkEngine::new(1.0)?);","highlight_start":1,"highlight_end":65},{"text":"    let telemetry = Arc::new(TelemetryCollector::new());","highlight_start":1,"highlight_end":57},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    let engine = ChatterboxEngine::new(config).await?;","highlight_start":1,"highlight_end":55},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    spinner.finish_with_message(\"✅ Engine initialized successfully\");","highlight_start":1,"highlight_end":70},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Execute command","highlight_start":1,"highlight_end":23},{"text":"    match cli.command {","highlight_start":1,"highlight_end":24},{"text":"        Commands::Tts {","highlight_start":1,"highlight_end":24},{"text":"            text, voice, output, rate, pitch, volume, emotion, watermark, interactive","highlight_start":1,"highlight_end":86},{"text":"        } => {","highlight_start":1,"highlight_end":15},{"text":"            if interactive {","highlight_start":1,"highlight_end":29},{"text":"                println!(\"Interactive TTS mode not yet implemented\");","highlight_start":1,"highlight_end":70},{"text":"            } else {","highlight_start":1,"highlight_end":21},{"text":"                let text = if let Some(t) = text {","highlight_start":1,"highlight_end":51},{"text":"                    t","highlight_start":1,"highlight_end":22},{"text":"                } else {","highlight_start":1,"highlight_end":25},{"text":"                    \"Hello from Chatterbox CLI\".to_string()","highlight_start":1,"highlight_end":60},{"text":"                };","highlight_start":1,"highlight_end":19},{"text":"","highlight_start":1,"highlight_end":1},{"text":"                run_tts(&engine, &text, &voice, &output, rate, pitch, volume, emotion, watermark, &cli.format).await?;","highlight_start":1,"highlight_end":119},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"        Commands::Stt { input, language, output, punctuation, confidence, timestamps } => {","highlight_start":1,"highlight_end":92},{"text":"            run_stt(&engine, &input, language, output, punctuation, confidence, timestamps, &cli.format).await?;","highlight_start":1,"highlight_end":113},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"        Commands::Models { filter, detailed } => {","highlight_start":1,"highlight_end":51},{"text":"            println!(\"Models command not yet implemented\");","highlight_start":1,"highlight_end":60},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"        Commands::Status { detailed, watch, interval } => {","highlight_start":1,"highlight_end":60},{"text":"            println!(\"Status command not yet implemented\");","highlight_start":1,"highlight_end":60},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"        Commands::Batch { input, output, workers } => {","highlight_start":1,"highlight_end":56},{"text":"            println!(\"Batch command not yet implemented\");","highlight_start":1,"highlight_end":59},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"        Commands::Shell => {","highlight_start":1,"highlight_end":29},{"text":"            println!(\"Shell command not yet implemented\");","highlight_start":1,"highlight_end":59},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"        Commands::Benchmark { test_type, iterations, concurrent } => {","highlight_start":1,"highlight_end":71},{"text":"            println!(\"Benchmark command not yet implemented\");","highlight_start":1,"highlight_end":63},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"        Commands::Accessibility { command } => {","highlight_start":1,"highlight_end":49},{"text":"            run_accessibility_command(command, &cli.format).await?;","highlight_start":1,"highlight_end":68},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    Ok(())","highlight_start":1,"highlight_end":11},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"fn print_banner() {","highlight_start":1,"highlight_end":20},{"text":"    let term = Term::stdout();","highlight_start":1,"highlight_end":31},{"text":"    let _ = term.clear_screen();","highlight_start":1,"highlight_end":33},{"text":"    ","highlight_start":1,"highlight_end":5},{"text":"    println!(\"{}\", r#\"","highlight_start":1,"highlight_end":23},{"text":"    ╔═══════════════════════════════════════════════════════════════╗","highlight_start":1,"highlight_end":70},{"text":"    ║                                                               ║","highlight_start":1,"highlight_end":70},{"text":"    ║   ██████╗██╗  ██╗ █████╗ ████████╗████████╗███████╗██████╗    ║","highlight_start":1,"highlight_end":70},{"text":"    ║  ██╔════╝██║  ██║██╔══██╗╚══██╔══╝╚══██╔══╝██╔════╝██╔══██╗   ║","highlight_start":1,"highlight_end":70},{"text":"    ║  ██║     ███████║███████║   ██║      ██║   █████╗  ██████╔╝   ║","highlight_start":1,"highlight_end":70},{"text":"    ║  ██║     ██╔══██║██╔══██║   ██║      ██║   ██╔══╝  ██╔══██╗   ║","highlight_start":1,"highlight_end":70},{"text":"    ║  ╚██████╗██║  ██║██║  ██║   ██║      ██║   ███████╗██║  ██║   ║","highlight_start":1,"highlight_end":70},{"text":"    ║   ╚═════╝╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝      ╚═╝   ╚══════╝╚═╝  ╚═╝   ║","highlight_start":1,"highlight_end":70},{"text":"    ║                                                               ║","highlight_start":1,"highlight_end":70},{"text":"    ║                    ENTERPRISE CLI v1.0.0                     ║","highlight_start":1,"highlight_end":69},{"text":"    ║              Professional TTS/STT Command Line               ║","highlight_start":1,"highlight_end":69},{"text":"    ║                                                               ║","highlight_start":1,"highlight_end":70},{"text":"    ╚═══════════════════════════════════════════════════════════════╝","highlight_start":1,"highlight_end":70},{"text":"    \"#.bright_cyan());","highlight_start":1,"highlight_end":23},{"text":"    ","highlight_start":1,"highlight_end":5},{"text":"    println!(\"{}\", \"🎧 Welcome to Chatterbox Enterprise CLI\".bright_green().bold());","highlight_start":1,"highlight_end":84},{"text":"    println!(\"{}\", \"   Enterprise-grade Text-to-Speech and Speech-to-Text\".bright_white());","highlight_start":1,"highlight_end":92},{"text":"    println!();","highlight_start":1,"highlight_end":16},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"async fn run_tts(","highlight_start":1,"highlight_end":18},{"text":"    engine: &ChatterboxEngine,","highlight_start":1,"highlight_end":31},{"text":"    text: &str,","highlight_start":1,"highlight_end":16},{"text":"    voice: &str,","highlight_start":1,"highlight_end":17},{"text":"    output: &PathBuf,","highlight_start":1,"highlight_end":22},{"text":"    rate: f32,","highlight_start":1,"highlight_end":15},{"text":"    pitch: f32,","highlight_start":1,"highlight_end":16},{"text":"    volume: f32,","highlight_start":1,"highlight_end":17},{"text":"    emotion: Option<String>,","highlight_start":1,"highlight_end":29},{"text":"    watermark: bool,","highlight_start":1,"highlight_end":21},{"text":"    format: &str,","highlight_start":1,"highlight_end":18},{"text":") -> Result<()> {","highlight_start":1,"highlight_end":18},{"text":"    let options = SynthesisOptions {","highlight_start":1,"highlight_end":37},{"text":"        voice_model: Some(voice.to_string()),","highlight_start":1,"highlight_end":46},{"text":"        speaking_rate: Some(rate),","highlight_start":1,"highlight_end":35},{"text":"        pitch: Some(pitch),","highlight_start":1,"highlight_end":28},{"text":"        volume: Some(volume),","highlight_start":1,"highlight_end":30},{"text":"        emotion,","highlight_start":1,"highlight_end":17},{"text":"        apply_watermark: Some(watermark),","highlight_start":1,"highlight_end":42},{"text":"        format: Some(AudioFormat::Wav),","highlight_start":1,"highlight_end":40},{"text":"        sample_rate: Some(22050),","highlight_start":1,"highlight_end":34},{"text":"        reference_audio: None,","highlight_start":1,"highlight_end":31},{"text":"    };","highlight_start":1,"highlight_end":7},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    println!(\"{}\", \"🎤 Starting TTS synthesis...\".bright_blue());","highlight_start":1,"highlight_end":65},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    let progress = ProgressBar::new_spinner();","highlight_start":1,"highlight_end":47},{"text":"    progress.set_style(ProgressStyle::default_spinner()","highlight_start":1,"highlight_end":56},{"text":"        .template(\"{spinner:.green} {msg}\")","highlight_start":1,"highlight_end":44},{"text":"        .unwrap());","highlight_start":1,"highlight_end":20},{"text":"    progress.set_message(\"Synthesizing speech...\");","highlight_start":1,"highlight_end":52},{"text":"    progress.enable_steady_tick(std::time::Duration::from_millis(100));","highlight_start":1,"highlight_end":72},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    let start_time = Instant::now();","highlight_start":1,"highlight_end":37},{"text":"    let result = engine.synthesize(text, Some(options)).await?;","highlight_start":1,"highlight_end":64},{"text":"    let duration = start_time.elapsed();","highlight_start":1,"highlight_end":41},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    progress.finish_with_message(\"✅ Synthesis completed\");","highlight_start":1,"highlight_end":59},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Save audio to file (simplified for demo)","highlight_start":1,"highlight_end":48},{"text":"    let audio_samples = result.audio.samples();","highlight_start":1,"highlight_end":48},{"text":"    println!(\"📁 Saving audio to: {}\", output.display());","highlight_start":1,"highlight_end":57},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // In a real implementation, you would save the actual WAV file","highlight_start":1,"highlight_end":68},{"text":"    fs::write(output, format!(\"Audio samples: {} samples\", audio_samples.len()))?;","highlight_start":1,"highlight_end":83},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    match format {","highlight_start":1,"highlight_end":19},{"text":"        \"json\" => {","highlight_start":1,"highlight_end":20},{"text":"            println!(\"{}\", serde_json::json!({","highlight_start":1,"highlight_end":47},{"text":"                \"status\": \"success\",","highlight_start":1,"highlight_end":37},{"text":"                \"duration_ms\": duration.as_millis(),","highlight_start":1,"highlight_end":53},{"text":"                \"audio_samples\": audio_samples.len(),","highlight_start":1,"highlight_end":54},{"text":"                \"sample_rate\": format!(\"{:?}\", result.audio.sample_rate()),","highlight_start":1,"highlight_end":76},{"text":"                \"output_file\": output.display().to_string(),","highlight_start":1,"highlight_end":61},{"text":"                \"watermark_applied\": result.watermarked","highlight_start":1,"highlight_end":56},{"text":"            }));","highlight_start":1,"highlight_end":17},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"        \"table\" => {","highlight_start":1,"highlight_end":21},{"text":"            let metrics = vec![","highlight_start":1,"highlight_end":32},{"text":"                SystemMetric { metric: \"Duration\".to_string(), value: format!(\"{:.2}ms\", duration.as_millis()), status: \"✅\".to_string() },","highlight_start":1,"highlight_end":139},{"text":"                SystemMetric { metric: \"Audio Samples\".to_string(), value: audio_samples.len().to_string(), status: \"✅\".to_string() },","highlight_start":1,"highlight_end":135},{"text":"                SystemMetric { metric: \"Sample Rate\".to_string(), value: format!(\"{:?}\", result.audio.sample_rate()), status: \"✅\".to_string() },","highlight_start":1,"highlight_end":145},{"text":"                SystemMetric { metric: \"Output File\".to_string(), value: output.display().to_string(), status: \"✅\".to_string() },","highlight_start":1,"highlight_end":130},{"text":"            ];","highlight_start":1,"highlight_end":15},{"text":"            println!(\"{}\", Table::new(metrics));","highlight_start":1,"highlight_end":49},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"        _ => {","highlight_start":1,"highlight_end":15},{"text":"            println!(\"✅ TTS synthesis completed in {:.2}ms\", duration.as_millis());","highlight_start":1,"highlight_end":84},{"text":"            println!(\"📊 Generated {} audio samples\", audio_samples.len());","highlight_start":1,"highlight_end":75},{"text":"            println!(\"📁 Saved to: {}\", output.display());","highlight_start":1,"highlight_end":58},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    Ok(())","highlight_start":1,"highlight_end":11},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"async fn run_stt(","highlight_start":1,"highlight_end":18},{"text":"    engine: &ChatterboxEngine,","highlight_start":1,"highlight_end":31},{"text":"    input: &PathBuf,","highlight_start":1,"highlight_end":21},{"text":"    language: Option<String>,","highlight_start":1,"highlight_end":30},{"text":"    output: Option<PathBuf>,","highlight_start":1,"highlight_end":29},{"text":"    punctuation: bool,","highlight_start":1,"highlight_end":23},{"text":"    confidence: f32,","highlight_start":1,"highlight_end":21},{"text":"    timestamps: bool,","highlight_start":1,"highlight_end":22},{"text":"    format: &str,","highlight_start":1,"highlight_end":18},{"text":") -> Result<()> {","highlight_start":1,"highlight_end":18},{"text":"    println!(\"{}\", \"🎧 Starting STT recognition...\".bright_blue());","highlight_start":1,"highlight_end":67},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Load audio file (simplified for demo)","highlight_start":1,"highlight_end":45},{"text":"    if !input.exists() {","highlight_start":1,"highlight_end":25},{"text":"        return Err(chatterbox_enterprise::ChatterboxError::validation(","highlight_start":1,"highlight_end":71},{"text":"            \"Input audio file not found\".to_string(),","highlight_start":1,"highlight_end":54},{"text":"            \"input_file\",","highlight_start":1,"highlight_end":26},{"text":"        ));","highlight_start":1,"highlight_end":12},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Create dummy audio data for demo","highlight_start":1,"highlight_end":40},{"text":"    let samples = vec![0.1f32; 22050]; // 1 second of test audio","highlight_start":1,"highlight_end":65},{"text":"    let audio = AudioData::new(samples, 22050, 1, AudioFormat::Wav);","highlight_start":1,"highlight_end":69},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    let options = RecognitionOptions {","highlight_start":1,"highlight_end":39},{"text":"        language,","highlight_start":1,"highlight_end":18},{"text":"        punctuation: Some(punctuation),","highlight_start":1,"highlight_end":40},{"text":"        confidence_threshold: Some(confidence),","highlight_start":1,"highlight_end":48},{"text":"        model: None,","highlight_start":1,"highlight_end":21},{"text":"        realtime: Some(false),","highlight_start":1,"highlight_end":31},{"text":"        max_duration: None,","highlight_start":1,"highlight_end":28},{"text":"    };","highlight_start":1,"highlight_end":7},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    let progress = ProgressBar::new_spinner();","highlight_start":1,"highlight_end":47},{"text":"    progress.set_style(ProgressStyle::default_spinner()","highlight_start":1,"highlight_end":56},{"text":"        .template(\"{spinner:.green} {msg}\")","highlight_start":1,"highlight_end":44},{"text":"        .unwrap());","highlight_start":1,"highlight_end":20},{"text":"    progress.set_message(\"Recognizing speech...\");","highlight_start":1,"highlight_end":51},{"text":"    progress.enable_steady_tick(std::time::Duration::from_millis(100));","highlight_start":1,"highlight_end":72},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    let start_time = Instant::now();","highlight_start":1,"highlight_end":37},{"text":"    let result = engine.recognize(audio?, Some(options)).await?;","highlight_start":1,"highlight_end":65},{"text":"    let duration = start_time.elapsed();","highlight_start":1,"highlight_end":41},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    progress.finish_with_message(\"✅ Recognition completed\");","highlight_start":1,"highlight_end":61},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Save text output if specified","highlight_start":1,"highlight_end":37},{"text":"    if let Some(output_path) = output {","highlight_start":1,"highlight_end":40},{"text":"        fs::write(&output_path, &result.text)?;","highlight_start":1,"highlight_end":48},{"text":"        println!(\"📁 Text saved to: {}\", output_path.display());","highlight_start":1,"highlight_end":64},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    match format {","highlight_start":1,"highlight_end":19},{"text":"        \"json\" => {","highlight_start":1,"highlight_end":20},{"text":"            println!(\"{}\", serde_json::json!({","highlight_start":1,"highlight_end":47},{"text":"                \"status\": \"success\",","highlight_start":1,"highlight_end":37},{"text":"                \"duration_ms\": duration.as_millis(),","highlight_start":1,"highlight_end":53},{"text":"                \"text\": result.text,","highlight_start":1,"highlight_end":37},{"text":"                \"confidence\": result.confidence,","highlight_start":1,"highlight_end":49},{"text":"                \"language\": result.language,","highlight_start":1,"highlight_end":45},{"text":"                \"word_count\": result.text.split_whitespace().count()","highlight_start":1,"highlight_end":69},{"text":"            }));","highlight_start":1,"highlight_end":17},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"        \"table\" => {","highlight_start":1,"highlight_end":21},{"text":"            let metrics = vec![","highlight_start":1,"highlight_end":32},{"text":"                SystemMetric { metric: \"Duration\".to_string(), value: format!(\"{:.2}ms\", duration.as_millis()), status: \"✅\".to_string() },","highlight_start":1,"highlight_end":139},{"text":"                SystemMetric { metric: \"Confidence\".to_string(), value: format!(\"{:.2}\", result.confidence), status: \"✅\".to_string() },","highlight_start":1,"highlight_end":136},{"text":"                SystemMetric { metric: \"Language\".to_string(), value: result.language.unwrap_or(\"auto\".to_string()), status: \"✅\".to_string() },","highlight_start":1,"highlight_end":144},{"text":"                SystemMetric { metric: \"Word Count\".to_string(), value: result.text.split_whitespace().count().to_string(), status: \"✅\".to_string() },","highlight_start":1,"highlight_end":151},{"text":"            ];","highlight_start":1,"highlight_end":15},{"text":"            println!(\"{}\", Table::new(metrics));","highlight_start":1,"highlight_end":49},{"text":"            println!(\"\\n📝 Recognized Text:\");","highlight_start":1,"highlight_end":46},{"text":"            println!(\"{}\", result.text.bright_white().bold());","highlight_start":1,"highlight_end":63},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"        _ => {","highlight_start":1,"highlight_end":15},{"text":"            println!(\"✅ STT recognition completed in {:.2}ms\", duration.as_millis());","highlight_start":1,"highlight_end":86},{"text":"            println!(\"📊 Confidence: {:.2}\", result.confidence);","highlight_start":1,"highlight_end":64},{"text":"            println!(\"📝 Text: {}\", result.text.bright_white().bold());","highlight_start":1,"highlight_end":71},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    Ok(())","highlight_start":1,"highlight_end":11},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"async fn run_accessibility_command(command: AccessibilityCommand, format: &str) -> Result<()> {","highlight_start":1,"highlight_end":96},{"text":"    match command {","highlight_start":1,"highlight_end":20},{"text":"        AccessibilityCommand::Configure {","highlight_start":1,"highlight_end":42},{"text":"            level, voice_control, eye_tracking, gestures, contrast_ratio, font_size","highlight_start":1,"highlight_end":84},{"text":"        } => {","highlight_start":1,"highlight_end":15},{"text":"            println!(\"{}\", \"🌟 Configuring WCAG 3.0 Accessibility Settings...\".bright_blue());","highlight_start":1,"highlight_end":94},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            let conformance_level = match level.as_str() {","highlight_start":1,"highlight_end":59},{"text":"                \"bronze\" => ConformanceLevel::Bronze,","highlight_start":1,"highlight_end":54},{"text":"                \"silver\" => ConformanceLevel::Silver,","highlight_start":1,"highlight_end":54},{"text":"                \"gold\" => ConformanceLevel::Gold,","highlight_start":1,"highlight_end":50},{"text":"                _ => {","highlight_start":1,"highlight_end":23},{"text":"                    println!(\"{}\", \"❌ Invalid conformance level. Use: bronze, silver, gold\".red());","highlight_start":1,"highlight_end":100},{"text":"                    return Ok(());","highlight_start":1,"highlight_end":35},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            };","highlight_start":1,"highlight_end":15},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            let config = AccessibilityConfig {","highlight_start":1,"highlight_end":47},{"text":"                conformance_level,","highlight_start":1,"highlight_end":35},{"text":"                screen_reader_enabled: true,","highlight_start":1,"highlight_end":45},{"text":"                voice_control_enabled: voice_control,","highlight_start":1,"highlight_end":54},{"text":"                eye_tracking_enabled: eye_tracking,","highlight_start":1,"highlight_end":52},{"text":"                gesture_recognition_enabled: gestures,","highlight_start":1,"highlight_end":55},{"text":"                adaptive_ui_enabled: true,","highlight_start":1,"highlight_end":43},{"text":"                min_contrast_ratio: contrast_ratio,","highlight_start":1,"highlight_end":52},{"text":"                font_size_multiplier: font_size,","highlight_start":1,"highlight_end":49},{"text":"                reduce_animations: false,","highlight_start":1,"highlight_end":42},{"text":"                high_contrast_mode: false,","highlight_start":1,"highlight_end":43},{"text":"                enhanced_focus_indicators: true,","highlight_start":1,"highlight_end":49},{"text":"            };","highlight_start":1,"highlight_end":15},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            let _accessibility_manager = AccessibilityManager::new(config)?;","highlight_start":1,"highlight_end":77},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            match format {","highlight_start":1,"highlight_end":27},{"text":"                \"json\" => {","highlight_start":1,"highlight_end":28},{"text":"                    println!(\"{}\", serde_json::json!({","highlight_start":1,"highlight_end":55},{"text":"                        \"status\": \"success\",","highlight_start":1,"highlight_end":45},{"text":"                        \"conformance_level\": level,","highlight_start":1,"highlight_end":52},{"text":"                        \"voice_control\": voice_control,","highlight_start":1,"highlight_end":56},{"text":"                        \"eye_tracking\": eye_tracking,","highlight_start":1,"highlight_end":54},{"text":"                        \"gestures\": gestures,","highlight_start":1,"highlight_end":46},{"text":"                        \"contrast_ratio\": contrast_ratio,","highlight_start":1,"highlight_end":58},{"text":"                        \"font_size_multiplier\": font_size","highlight_start":1,"highlight_end":58},{"text":"                    }));","highlight_start":1,"highlight_end":25},{"text":"                },","highlight_start":1,"highlight_end":19},{"text":"                _ => {","highlight_start":1,"highlight_end":23},{"text":"                    println!(\"✅ Accessibility configuration updated:\");","highlight_start":1,"highlight_end":72},{"text":"                    println!(\"   📊 WCAG 3.0 Conformance Level: {}\", level.to_uppercase().bright_green());","highlight_start":1,"highlight_end":106},{"text":"                    println!(\"   🎤 Voice Control: {}\", if voice_control { \"✅ Enabled\".green() } else { \"❌ Disabled\".red() });","highlight_start":1,"highlight_end":126},{"text":"                    println!(\"   👁️  Eye Tracking: {}\", if eye_tracking { \"✅ Enabled\".green() } else { \"❌ Disabled\".red() });","highlight_start":1,"highlight_end":126},{"text":"                    println!(\"   👋 Gesture Recognition: {}\", if gestures { \"✅ Enabled\".green() } else { \"❌ Disabled\".red() });","highlight_start":1,"highlight_end":127},{"text":"                    println!(\"   🎨 Contrast Ratio: {:.1}:1\", contrast_ratio);","highlight_start":1,"highlight_end":78},{"text":"                    println!(\"   📝 Font Size Multiplier: {:.1}x\", font_size);","highlight_start":1,"highlight_end":78},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        AccessibilityCommand::Mode { mode } => {","highlight_start":1,"highlight_end":49},{"text":"            println!(\"{}\", \"🔄 Switching Interactive Mode...\".bright_blue());","highlight_start":1,"highlight_end":77},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            let interactive_mode = match mode.as_str() {","highlight_start":1,"highlight_end":57},{"text":"                \"standard\" => InteractiveMode::Standard,","highlight_start":1,"highlight_end":57},{"text":"                \"voice\" => InteractiveMode::Voice,","highlight_start":1,"highlight_end":51},{"text":"                \"eye-tracking\" => InteractiveMode::EyeTracking,","highlight_start":1,"highlight_end":64},{"text":"                \"gesture\" => InteractiveMode::Gesture,","highlight_start":1,"highlight_end":55},{"text":"                \"switch\" => InteractiveMode::SwitchControl,","highlight_start":1,"highlight_end":60},{"text":"                \"head-tracking\" => InteractiveMode::HeadTracking,","highlight_start":1,"highlight_end":66},{"text":"                _ => {","highlight_start":1,"highlight_end":23},{"text":"                    println!(\"{}\", \"❌ Invalid mode. Use: standard, voice, eye-tracking, gesture, switch, head-tracking\".red());","highlight_start":1,"highlight_end":128},{"text":"                    return Ok(());","highlight_start":1,"highlight_end":35},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            };","highlight_start":1,"highlight_end":15},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            // Create a temporary accessibility manager for demo","highlight_start":1,"highlight_end":65},{"text":"            let config = AccessibilityConfig::default();","highlight_start":1,"highlight_end":57},{"text":"            let mut accessibility_manager = AccessibilityManager::new(config)?;","highlight_start":1,"highlight_end":80},{"text":"            accessibility_manager.switch_mode(interactive_mode.clone()).await?;","highlight_start":1,"highlight_end":80},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            match format {","highlight_start":1,"highlight_end":27},{"text":"                \"json\" => {","highlight_start":1,"highlight_end":28},{"text":"                    println!(\"{}\", serde_json::json!({","highlight_start":1,"highlight_end":55},{"text":"                        \"status\": \"success\",","highlight_start":1,"highlight_end":45},{"text":"                        \"mode\": mode,","highlight_start":1,"highlight_end":38},{"text":"                        \"description\": format!(\"Switched to {} mode\", mode)","highlight_start":1,"highlight_end":76},{"text":"                    }));","highlight_start":1,"highlight_end":25},{"text":"                },","highlight_start":1,"highlight_end":19},{"text":"                _ => {","highlight_start":1,"highlight_end":23},{"text":"                    println!(\"✅ Interactive mode switched to: {}\", mode.bright_green().bold());","highlight_start":1,"highlight_end":96},{"text":"                    match mode.as_str() {","highlight_start":1,"highlight_end":42},{"text":"                        \"voice\" => println!(\"   🎤 Say 'Help' for available voice commands\"),","highlight_start":1,"highlight_end":93},{"text":"                        \"eye-tracking\" => println!(\"   👁️  Look at elements to interact with them\"),","highlight_start":1,"highlight_end":101},{"text":"                        \"gesture\" => println!(\"   👋 Use gestures to navigate and interact\"),","highlight_start":1,"highlight_end":93},{"text":"                        \"switch\" => println!(\"   🔄 Use switch controls for navigation\"),","highlight_start":1,"highlight_end":89},{"text":"                        _ => println!(\"   ⌨️  Use keyboard and mouse for interaction\"),","highlight_start":1,"highlight_end":88},{"text":"                    }","highlight_start":1,"highlight_end":22},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        AccessibilityCommand::Test { feature } => {","highlight_start":1,"highlight_end":52},{"text":"            println!(\"{}\", \"🧪 Testing Accessibility Features...\".bright_blue());","highlight_start":1,"highlight_end":81},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            let progress = ProgressBar::new_spinner();","highlight_start":1,"highlight_end":55},{"text":"            progress.set_style(ProgressStyle::default_spinner()","highlight_start":1,"highlight_end":64},{"text":"                .template(\"{spinner:.green} {msg}\")","highlight_start":1,"highlight_end":52},{"text":"                .unwrap());","highlight_start":1,"highlight_end":28},{"text":"            progress.enable_steady_tick(std::time::Duration::from_millis(100));","highlight_start":1,"highlight_end":80},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            match feature.as_str() {","highlight_start":1,"highlight_end":37},{"text":"                \"voice\" => {","highlight_start":1,"highlight_end":29},{"text":"                    progress.set_message(\"Testing voice control...\");","highlight_start":1,"highlight_end":70},{"text":"                    tokio::time::sleep(std::time::Duration::from_millis(1000)).await;","highlight_start":1,"highlight_end":86},{"text":"                    progress.finish_with_message(\"✅ Voice control test completed\");","highlight_start":1,"highlight_end":84},{"text":"                    println!(\"   🎤 Voice recognition: {}\", \"Working\".green());","highlight_start":1,"highlight_end":79},{"text":"                    println!(\"   🔊 Text-to-speech: {}\", \"Working\".green());","highlight_start":1,"highlight_end":76},{"text":"                    println!(\"   📢 Voice commands: {}\", \"Available\".green());","highlight_start":1,"highlight_end":78},{"text":"                },","highlight_start":1,"highlight_end":19},{"text":"                \"eye-tracking\" => {","highlight_start":1,"highlight_end":36},{"text":"                    progress.set_message(\"Testing eye tracking...\");","highlight_start":1,"highlight_end":69},{"text":"                    tokio::time::sleep(std::time::Duration::from_millis(1500)).await;","highlight_start":1,"highlight_end":86},{"text":"                    progress.finish_with_message(\"✅ Eye tracking test completed\");","highlight_start":1,"highlight_end":83},{"text":"                    println!(\"   👁️  Gaze detection: {}\", \"Working\".green());","highlight_start":1,"highlight_end":78},{"text":"                    println!(\"   🎯 Fixation tracking: {}\", \"Working\".green());","highlight_start":1,"highlight_end":79},{"text":"                    println!(\"   ⏱️  Dwell activation: {}\", \"Available\".green());","highlight_start":1,"highlight_end":82},{"text":"                },","highlight_start":1,"highlight_end":19},{"text":"                \"gesture\" => {","highlight_start":1,"highlight_end":31},{"text":"                    progress.set_message(\"Testing gesture recognition...\");","highlight_start":1,"highlight_end":76},{"text":"                    tokio::time::sleep(std::time::Duration::from_millis(1200)).await;","highlight_start":1,"highlight_end":86},{"text":"                    progress.finish_with_message(\"✅ Gesture recognition test completed\");","highlight_start":1,"highlight_end":90},{"text":"                    println!(\"   👆 Touch gestures: {}\", \"Working\".green());","highlight_start":1,"highlight_end":76},{"text":"                    println!(\"   🤲 Multi-finger gestures: {}\", \"Working\".green());","highlight_start":1,"highlight_end":83},{"text":"                    println!(\"   🎭 Head gestures: {}\", \"Available\".green());","highlight_start":1,"highlight_end":77},{"text":"                },","highlight_start":1,"highlight_end":19},{"text":"                \"contrast\" => {","highlight_start":1,"highlight_end":32},{"text":"                    progress.set_message(\"Testing contrast analysis...\");","highlight_start":1,"highlight_end":74},{"text":"                    tokio::time::sleep(std::time::Duration::from_millis(800)).await;","highlight_start":1,"highlight_end":85},{"text":"                    progress.finish_with_message(\"✅ Contrast analysis test completed\");","highlight_start":1,"highlight_end":88},{"text":"                    println!(\"   🎨 Color contrast: {}\", \"4.5:1 (AA)\".green());","highlight_start":1,"highlight_end":79},{"text":"                    println!(\"   🌓 High contrast mode: {}\", \"Available\".green());","highlight_start":1,"highlight_end":82},{"text":"                    println!(\"   🔧 Auto-adjustment: {}\", \"Working\".green());","highlight_start":1,"highlight_end":77},{"text":"                },","highlight_start":1,"highlight_end":19},{"text":"                \"all\" => {","highlight_start":1,"highlight_end":27},{"text":"                    progress.set_message(\"Running comprehensive accessibility tests...\");","highlight_start":1,"highlight_end":90},{"text":"                    tokio::time::sleep(std::time::Duration::from_millis(3000)).await;","highlight_start":1,"highlight_end":86},{"text":"                    progress.finish_with_message(\"✅ All accessibility tests completed\");","highlight_start":1,"highlight_end":89},{"text":"","highlight_start":1,"highlight_end":1},{"text":"                    println!(\"📊 Accessibility Test Results:\");","highlight_start":1,"highlight_end":63},{"text":"                    println!(\"   🎤 Voice Control: {}\", \"✅ Pass\".green());","highlight_start":1,"highlight_end":74},{"text":"                    println!(\"   👁️  Eye Tracking: {}\", \"✅ Pass\".green());","highlight_start":1,"highlight_end":75},{"text":"                    println!(\"   👋 Gesture Recognition: {}\", \"✅ Pass\".green());","highlight_start":1,"highlight_end":80},{"text":"                    println!(\"   🎨 Contrast Analysis: {}\", \"✅ Pass\".green());","highlight_start":1,"highlight_end":78},{"text":"                    println!(\"   ⌨️  Keyboard Navigation: {}\", \"✅ Pass\".green());","highlight_start":1,"highlight_end":82},{"text":"                    println!(\"   🔊 Screen Reader Support: {}\", \"✅ Pass\".green());","highlight_start":1,"highlight_end":82},{"text":"                    println!(\"   🎯 ARIA Implementation: {}\", \"✅ Pass\".green());","highlight_start":1,"highlight_end":80},{"text":"                    println!(\"   🌟 WCAG 3.0 Compliance: {}\", \"✅ Silver Level\".green());","highlight_start":1,"highlight_end":88},{"text":"                },","highlight_start":1,"highlight_end":19},{"text":"                _ => {","highlight_start":1,"highlight_end":23},{"text":"                    println!(\"{}\", \"❌ Invalid feature. Use: voice, eye-tracking, gesture, contrast, all\".red());","highlight_start":1,"highlight_end":113},{"text":"                    return Ok(());","highlight_start":1,"highlight_end":35},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        },","highlight_start":1,"highlight_end":11},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        AccessibilityCommand::Status => {","highlight_start":1,"highlight_end":42},{"text":"            println!(\"{}\", \"📊 WCAG 3.0 Accessibility Status\".bright_blue());","highlight_start":1,"highlight_end":77},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            // Create a demo accessibility manager","highlight_start":1,"highlight_end":51},{"text":"            let config = AccessibilityConfig::default();","highlight_start":1,"highlight_end":57},{"text":"            let accessibility_manager = AccessibilityManager::new(config)?;","highlight_start":1,"highlight_end":76},{"text":"            let status = accessibility_manager.get_status();","highlight_start":1,"highlight_end":61},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            match format {","highlight_start":1,"highlight_end":27},{"text":"                \"json\" => {","highlight_start":1,"highlight_end":28},{"text":"                    println!(\"{}\", serde_json::json!({","highlight_start":1,"highlight_end":55},{"text":"                        \"conformance_level\": format!(\"{:?}\", status.conformance_level),","highlight_start":1,"highlight_end":88},{"text":"                        \"current_mode\": format!(\"{:?}\", status.current_mode),","highlight_start":1,"highlight_end":78},{"text":"                        \"features_enabled\": status.features_enabled,","highlight_start":1,"highlight_end":69},{"text":"                        \"contrast_ratio\": status.contrast_ratio,","highlight_start":1,"highlight_end":65},{"text":"                        \"font_size_multiplier\": status.font_size_multiplier","highlight_start":1,"highlight_end":76},{"text":"                    }));","highlight_start":1,"highlight_end":25},{"text":"                },","highlight_start":1,"highlight_end":19},{"text":"                _ => {","highlight_start":1,"highlight_end":23},{"text":"                    println!(\"🌟 WCAG 3.0 Conformance Level: {}\", format!(\"{:?}\", status.conformance_level).bright_green().bold());","highlight_start":1,"highlight_end":131},{"text":"                    println!(\"🔄 Current Interactive Mode: {}\", format!(\"{:?}\", status.current_mode).bright_cyan());","highlight_start":1,"highlight_end":116},{"text":"                    println!(\"🎨 Contrast Ratio: {:.1}:1\", status.contrast_ratio);","highlight_start":1,"highlight_end":82},{"text":"                    println!(\"📝 Font Size Multiplier: {:.1}x\", status.font_size_multiplier);","highlight_start":1,"highlight_end":93},{"text":"                    println!(\"\\n✨ Enabled Accessibility Features:\");","highlight_start":1,"highlight_end":69},{"text":"                    for feature in &status.features_enabled {","highlight_start":1,"highlight_end":62},{"text":"                        println!(\"   ✅ {}\", feature.green());","highlight_start":1,"highlight_end":62},{"text":"                    }","highlight_start":1,"highlight_end":22},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    Ok(())","highlight_start":1,"highlight_end":11},{"text":"}","highlight_start":1,"highlight_end":2}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"requested on the command line with `-W missing-docs`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: missing documentation for the crate\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/cli.rs:1:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m/\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chatterbox_enterprise::{\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    accessibility::{AccessibilityManager, AccessibilityConfig, ConformanceLevel, InteractiveMode},\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config::{Config, AudioFormat},\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m702\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Ok(())\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m703\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m}\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|_^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: requested on the command line with `-W missing-docs`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"18 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 18 warnings emitted\u001b[0m\n\n"}
