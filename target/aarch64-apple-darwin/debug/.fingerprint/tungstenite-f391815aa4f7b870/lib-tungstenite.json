{"rustc": 657458298742096754, "features": "[\"data-encoding\", \"handshake\", \"http\", \"httparse\", \"sha1\", \"url\"]", "declared_features": "[\"__rustls-tls\", \"data-encoding\", \"default\", \"handshake\", \"http\", \"httparse\", \"native-tls\", \"native-tls-crate\", \"native-tls-vendored\", \"rustls\", \"rustls-native-certs\", \"rustls-pki-types\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"sha1\", \"url\", \"webpki-roots\"]", "target": 13609099840216434247, "profile": 4374887572363265115, "path": 13871731744800795286, "deps": [[523978686976814572, "data_encoding", false, 18411012367076877055], [1632138251998637657, "httparse", false, 6938381878759279884], [2543237566893615891, "bytes", false, 11285958084381035690], [5910892534286594076, "rand", false, 9471981064599548653], [6388073677788589347, "utf8", false, 8633072968822076064], [8252504589640438155, "sha1", false, 1271555237853258607], [8926101378076943148, "byteorder", false, 8497819907663012272], [11266840602298992523, "thiserror", false, 7621806473835478958], [12368485582061518913, "http", false, 12477151170489463076], [15399619262696441677, "log", false, 10128934488753385547], [18130989770956114225, "url", false, 7123811269099291646]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/tungstenite-f391815aa4f7b870/dep-lib-tungstenite", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 10010679548380497168, "config": 2202906307356721367, "compile_kind": 14493662952515048101}