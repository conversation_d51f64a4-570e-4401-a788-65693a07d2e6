{"rustc": 657458298742096754, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 1236213820636157529, "profile": 7549465035008259043, "path": 15811406380822173917, "deps": [[1167534579069247419, "build_script_build", false, 16061947712556825875], [4722402946104583944, "bitflags", false, 16444296716632961664], [7762067171913260472, "libc", false, 3547979548778285370], [12407528770687128074, "libc_errno", false, 12776264336552360557]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/rustix-6d5cc25864091e33/dep-lib-rustix", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 7953970670347159126, "config": 2202906307356721367, "compile_kind": 14493662952515048101}