{"rustc": 657458298742096754, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 367108867326171846, "profile": 16491756098474355655, "path": 18020465671828189374, "deps": [[461436706529125561, "futures_io", false, 9399740872366887197], [1910231660504989506, "futures_task", false, 10389562942305803143], [5846781562065118163, "futures_channel", false, 11598994824850235611], [8083238378394459630, "futures_executor", false, 18305373280966247260], [9396302785578940539, "futures_core", false, 4366664659718766829], [11289432439818403777, "futures_sink", false, 385092885775113453], [16476303074998891276, "futures_util", false, 14799717098828253815]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/futures-b9ea265c2ed85021/dep-lib-futures", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 7593721274762670645, "config": 2202906307356721367, "compile_kind": 14493662952515048101}