{"rustc": 657458298742096754, "features": "[\"default\", \"rand\", \"serde_crate\", \"std\", \"u64_backend\"]", "declared_features": "[\"alloc\", \"asm\", \"batch\", \"batch_deterministic\", \"default\", \"legacy_compatibility\", \"merlin\", \"nightly\", \"rand\", \"rand_core\", \"serde\", \"serde_bytes\", \"serde_crate\", \"simd_backend\", \"std\", \"u32_backend\", \"u64_backend\"]", "target": 11260574056418161977, "profile": 4374887572363265115, "path": 10423549338768888261, "deps": [[288937492735761168, "sha2", false, 14962904566154236315], [2174722005733178090, "ed25519", false, 16681196386562565827], [5682154963376874164, "curve25519_dalek", false, 1795822075657469019], [10633404241517405153, "serde_crate", false, 7420612406277754670], [14778545527639897399, "rand", false, 16453157113514423325], [16255406213544131105, "zeroize", false, 4448680748894229927]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/ed25519-dalek-d91ccdeb85624d14/dep-lib-ed25519_dalek", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 14415899641967170644, "config": 2202906307356721367, "compile_kind": 14493662952515048101}