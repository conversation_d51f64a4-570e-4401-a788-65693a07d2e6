{"$message_type":"diagnostic","message":"unused imports: `AudioData`, `RecognitionOptions`, `SampleRate`, and `SynthesisOptions`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/tui.rs","byte_start":80,"byte_end":96,"line_start":3,"line_end":3,"column_start":32,"column_end":48,"is_primary":true,"text":[{"text":"    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},","highlight_start":32,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":98,"byte_end":116,"line_start":3,"line_end":3,"column_start":50,"column_end":68,"is_primary":true,"text":[{"text":"    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},","highlight_start":50,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":131,"byte_end":140,"line_start":4,"line_end":4,"column_start":13,"column_end":22,"is_primary":true,"text":[{"text":"    audio::{AudioData, SampleRate},","highlight_start":13,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":142,"byte_end":152,"line_start":4,"line_end":4,"column_start":24,"column_end":34,"is_primary":true,"text":[{"text":"    audio::{AudioData, SampleRate},","highlight_start":24,"highlight_end":34}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/bin/tui.rs","byte_start":78,"byte_end":116,"line_start":3,"line_end":3,"column_start":30,"column_end":68,"is_primary":true,"text":[{"text":"    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},","highlight_start":30,"highlight_end":68}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/bin/tui.rs","byte_start":61,"byte_end":62,"line_start":3,"line_end":3,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/bin/tui.rs","byte_start":116,"byte_end":117,"line_start":3,"line_end":3,"column_start":68,"column_end":69,"is_primary":true,"text":[{"text":"    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},","highlight_start":68,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/bin/tui.rs","byte_start":117,"byte_end":153,"line_start":3,"line_end":4,"column_start":69,"column_end":35,"is_primary":true,"text":[{"text":"    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},","highlight_start":69,"highlight_end":70},{"text":"    audio::{AudioData, SampleRate},","highlight_start":1,"highlight_end":35}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `AudioData`, `RecognitionOptions`, `SampleRate`, and `SynthesisOptions`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/tui.rs:3:32\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    audio::{AudioData, SampleRate},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `Clear`, `ListState`, `Margin`, `Text`, and `symbols`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/tui.rs","byte_start":592,"byte_end":598,"line_start":17,"line_end":17,"column_start":56,"column_end":62,"is_primary":true,"text":[{"text":"    layout::{Alignment, Constraint, Direction, Layout, Margin, Rect},","highlight_start":56,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":648,"byte_end":655,"line_start":19,"line_end":19,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    symbols,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":680,"byte_end":684,"line_start":20,"line_end":20,"column_start":24,"column_end":28,"is_primary":true,"text":[{"text":"    text::{Line, Span, Text},","highlight_start":24,"highlight_end":28}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":726,"byte_end":731,"line_start":22,"line_end":22,"column_start":25,"column_end":30,"is_primary":true,"text":[{"text":"        Block, Borders, Clear, Gauge, List, ListItem, ListState, Paragraph, Tabs, Wrap,","highlight_start":25,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":756,"byte_end":765,"line_start":22,"line_end":22,"column_start":55,"column_end":64,"is_primary":true,"text":[{"text":"        Block, Borders, Clear, Gauge, List, ListItem, ListState, Paragraph, Tabs, Wrap,","highlight_start":55,"highlight_end":64}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"src/bin/tui.rs","byte_start":590,"byte_end":598,"line_start":17,"line_end":17,"column_start":54,"column_end":62,"is_primary":true,"text":[{"text":"    layout::{Alignment, Constraint, Direction, Layout, Margin, Rect},","highlight_start":54,"highlight_end":62}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/bin/tui.rs","byte_start":642,"byte_end":655,"line_start":18,"line_end":19,"column_start":36,"column_end":12,"is_primary":true,"text":[{"text":"    style::{Color, Modifier, Style},","highlight_start":36,"highlight_end":37},{"text":"    symbols,","highlight_start":1,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/bin/tui.rs","byte_start":678,"byte_end":684,"line_start":20,"line_end":20,"column_start":22,"column_end":28,"is_primary":true,"text":[{"text":"    text::{Line, Span, Text},","highlight_start":22,"highlight_end":28}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/bin/tui.rs","byte_start":724,"byte_end":731,"line_start":22,"line_end":22,"column_start":23,"column_end":30,"is_primary":true,"text":[{"text":"        Block, Borders, Clear, Gauge, List, ListItem, ListState, Paragraph, Tabs, Wrap,","highlight_start":23,"highlight_end":30}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/bin/tui.rs","byte_start":754,"byte_end":765,"line_start":22,"line_end":22,"column_start":53,"column_end":64,"is_primary":true,"text":[{"text":"        Block, Borders, Clear, Gauge, List, ListItem, ListState, Paragraph, Tabs, Wrap,","highlight_start":53,"highlight_end":64}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `Clear`, `ListState`, `Margin`, `Text`, and `symbols`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/tui.rs:17:56\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    layout::{Alignment, Constraint, Direction, Layout, Margin, Rect},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    style::{Color, Modifier, Style},\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m19\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    symbols,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m20\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    text::{Line, Span, Text},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m21\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    widgets::{\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        Block, Borders, Clear, Gauge, List, ListItem, ListState, Paragraph, Tabs, Wrap,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `tokio::time::sleep`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/tui.rs","byte_start":922,"byte_end":940,"line_start":30,"line_end":30,"column_start":5,"column_end":23,"is_primary":true,"text":[{"text":"use tokio::time::sleep;","highlight_start":5,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/bin/tui.rs","byte_start":918,"byte_end":942,"line_start":30,"line_end":31,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use tokio::time::sleep;","highlight_start":1,"highlight_end":24},{"text":"","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `tokio::time::sleep`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/tui.rs:30:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m30\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tokio::time::sleep;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"hidden lifetime parameters in types are deprecated","code":{"code":"elided_lifetimes_in_paths","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/tui.rs","byte_start":10153,"byte_end":10158,"line_start":333,"line_end":333,"column_start":15,"column_end":20,"is_primary":true,"text":[{"text":"fn ui(f: &mut Frame, state: &AppState) {","highlight_start":15,"highlight_end":20}],"label":"expected lifetime parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`-W elided-lifetimes-in-paths` implied by `-W rust-2018-idioms`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"to override `-W rust-2018-idioms` add `#[allow(elided_lifetimes_in_paths)]`","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"indicate the anonymous lifetime","code":null,"level":"help","spans":[{"file_name":"src/bin/tui.rs","byte_start":10158,"byte_end":10158,"line_start":333,"line_end":333,"column_start":20,"column_end":20,"is_primary":true,"text":[{"text":"fn ui(f: &mut Frame, state: &AppState) {","highlight_start":20,"highlight_end":20}],"label":null,"suggested_replacement":"<'_>","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: hidden lifetime parameters in types are deprecated\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/tui.rs:333:15\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m333\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn ui(f: &mut Frame, state: &AppState) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mexpected lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `-W elided-lifetimes-in-paths` implied by `-W rust-2018-idioms`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: to override `-W rust-2018-idioms` add `#[allow(elided_lifetimes_in_paths)]`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: indicate the anonymous lifetime\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m333\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0mfn ui(f: &mut Frame\u001b[0m\u001b[0m\u001b[38;5;10m<'_>\u001b[0m\u001b[0m, state: &AppState) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[38;5;10m++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"hidden lifetime parameters in types are deprecated","code":{"code":"elided_lifetimes_in_paths","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/tui.rs","byte_start":12232,"byte_end":12237,"line_start":382,"line_end":382,"column_start":29,"column_end":34,"is_primary":true,"text":[{"text":"fn render_dashboard(f: &mut Frame, area: Rect, state: &AppState) {","highlight_start":29,"highlight_end":34}],"label":"expected lifetime parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"indicate the anonymous lifetime","code":null,"level":"help","spans":[{"file_name":"src/bin/tui.rs","byte_start":12237,"byte_end":12237,"line_start":382,"line_end":382,"column_start":34,"column_end":34,"is_primary":true,"text":[{"text":"fn render_dashboard(f: &mut Frame, area: Rect, state: &AppState) {","highlight_start":34,"highlight_end":34}],"label":null,"suggested_replacement":"<'_>","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: hidden lifetime parameters in types are deprecated\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/tui.rs:382:29\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m382\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn render_dashboard(f: &mut Frame, area: Rect, state: &AppState) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mexpected lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: indicate the anonymous lifetime\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m382\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0mfn render_dashboard(f: &mut Frame\u001b[0m\u001b[0m\u001b[38;5;10m<'_>\u001b[0m\u001b[0m, area: Rect, state: &AppState) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[38;5;10m++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"hidden lifetime parameters in types are deprecated","code":{"code":"elided_lifetimes_in_paths","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/tui.rs","byte_start":15468,"byte_end":15476,"line_start":463,"line_end":463,"column_start":26,"column_end":34,"is_primary":true,"text":[{"text":"    let model_items: Vec<ListItem> = state.models.iter().map(|model| {","highlight_start":26,"highlight_end":34}],"label":"expected lifetime parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"indicate the anonymous lifetime","code":null,"level":"help","spans":[{"file_name":"src/bin/tui.rs","byte_start":15476,"byte_end":15476,"line_start":463,"line_end":463,"column_start":34,"column_end":34,"is_primary":true,"text":[{"text":"    let model_items: Vec<ListItem> = state.models.iter().map(|model| {","highlight_start":34,"highlight_end":34}],"label":null,"suggested_replacement":"<'_>","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: hidden lifetime parameters in types are deprecated\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/tui.rs:463:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m463\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let model_items: Vec<ListItem> = state.models.iter().map(|model| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mexpected lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: indicate the anonymous lifetime\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m463\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0m    let model_items: Vec<ListItem\u001b[0m\u001b[0m\u001b[38;5;10m<'_>\u001b[0m\u001b[0m> = state.models.iter().map(|model| {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[38;5;10m++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"hidden lifetime parameters in types are deprecated","code":{"code":"elided_lifetimes_in_paths","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/tui.rs","byte_start":16294,"byte_end":16299,"line_start":485,"line_end":485,"column_start":28,"column_end":33,"is_primary":true,"text":[{"text":"fn render_tts_mode(f: &mut Frame, area: Rect, state: &AppState) {","highlight_start":28,"highlight_end":33}],"label":"expected lifetime parameter","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"indicate the anonymous lifetime","code":null,"level":"help","spans":[{"file_name":"src/bin/tui.rs","byte_start":16299,"byte_end":16299,"line_start":485,"line_end":485,"column_start":33,"column_end":33,"is_primary":true,"text":[{"text":"fn render_tts_mode(f: &mut Frame, area: Rect, state: &AppState) {","highlight_start":33,"highlight_end":33}],"label":null,"suggested_replacement":"<'_>","suggestion_applicability":"Unspecified","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: hidden lifetime parameters in types are deprecated\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/tui.rs:485:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m485\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn render_tts_mode(f: &mut Frame, area: Rect, state: &AppState) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mexpected lifetime parameter\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: indicate the anonymous lifetime\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m485\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m| \u001b[0m\u001b[0mfn render_tts_mode(f: &mut Frame\u001b[0m\u001b[0m\u001b[38;5;10m<'_>\u001b[0m\u001b[0m, area: Rect, state: &AppState) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[38;5;10m++++\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `model_manager`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/tui.rs","byte_start":4281,"byte_end":4294,"line_start":159,"line_end":159,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"        let model_manager = Arc::new(ModelManager::new(Arc::new(config.clone())).await?);","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/bin/tui.rs","byte_start":4281,"byte_end":4294,"line_start":159,"line_end":159,"column_start":13,"column_end":26,"is_primary":true,"text":[{"text":"        let model_manager = Arc::new(ModelManager::new(Arc::new(config.clone())).await?);","highlight_start":13,"highlight_end":26}],"label":null,"suggested_replacement":"_model_manager","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `model_manager`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/tui.rs:159:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m159\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let model_manager = Arc::new(ModelManager::new(Arc::new(config.clone())).await?);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_model_manager`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `watermark_engine`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/tui.rs","byte_start":4371,"byte_end":4387,"line_start":160,"line_end":160,"column_start":13,"column_end":29,"is_primary":true,"text":[{"text":"        let watermark_engine = Arc::new(WatermarkEngine::new(1.0)?);","highlight_start":13,"highlight_end":29}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"src/bin/tui.rs","byte_start":4371,"byte_end":4387,"line_start":160,"line_end":160,"column_start":13,"column_end":29,"is_primary":true,"text":[{"text":"        let watermark_engine = Arc::new(WatermarkEngine::new(1.0)?);","highlight_start":13,"highlight_end":29}],"label":null,"suggested_replacement":"_watermark_engine","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `watermark_engine`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/tui.rs:160:13\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m160\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let watermark_engine = Arc::new(WatermarkEngine::new(1.0)?);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_watermark_engine`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Backend`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/tui.rs","byte_start":509,"byte_end":516,"line_start":16,"line_end":16,"column_start":15,"column_end":22,"is_primary":true,"text":[{"text":"    backend::{Backend, CrosstermBackend},","highlight_start":15,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Backend`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/tui.rs:16:15\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    backend::{Backend, CrosstermBackend},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `logs` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/tui.rs","byte_start":1206,"byte_end":1214,"line_start":51,"line_end":51,"column_start":8,"column_end":16,"is_primary":false,"text":[{"text":"struct AppState {","highlight_start":8,"highlight_end":16}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":1388,"byte_end":1392,"line_start":59,"line_end":59,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    logs: Vec<LogEntry>,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`AppState` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `logs` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/tui.rs:59:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m51\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct AppState {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m59\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    logs: Vec<LogEntry>,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `AppState` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `model_type` and `language` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/tui.rs","byte_start":1518,"byte_end":1527,"line_start":66,"line_end":66,"column_start":8,"column_end":17,"is_primary":false,"text":[{"text":"struct ModelInfo {","highlight_start":8,"highlight_end":17}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":1552,"byte_end":1562,"line_start":68,"line_end":68,"column_start":5,"column_end":15,"is_primary":true,"text":[{"text":"    model_type: String,","highlight_start":5,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":1576,"byte_end":1584,"line_start":69,"line_end":69,"column_start":5,"column_end":13,"is_primary":true,"text":[{"text":"    language: String,","highlight_start":5,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`ModelInfo` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `model_type` and `language` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/tui.rs:68:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct ModelInfo {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m67\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    name: String,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m68\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    model_type: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m69\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    language: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `ModelInfo` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `models_loaded` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/tui.rs","byte_start":1671,"byte_end":1684,"line_start":75,"line_end":75,"column_start":8,"column_end":21,"is_primary":false,"text":[{"text":"struct SystemMetrics {","highlight_start":8,"highlight_end":21}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":1856,"byte_end":1869,"line_start":83,"line_end":83,"column_start":5,"column_end":18,"is_primary":true,"text":[{"text":"    models_loaded: u32,","highlight_start":5,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`SystemMetrics` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `models_loaded` is never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/tui.rs:83:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m75\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct SystemMetrics {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m83\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    models_loaded: u32,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `SystemMetrics` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `timestamp`, `level`, `message`, and `component` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/tui.rs","byte_start":1910,"byte_end":1918,"line_start":87,"line_end":87,"column_start":8,"column_end":16,"is_primary":false,"text":[{"text":"struct LogEntry {","highlight_start":8,"highlight_end":16}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":1925,"byte_end":1934,"line_start":88,"line_end":88,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    timestamp: String,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":1948,"byte_end":1953,"line_start":89,"line_end":89,"column_start":5,"column_end":10,"is_primary":true,"text":[{"text":"    level: String,","highlight_start":5,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":1967,"byte_end":1974,"line_start":90,"line_end":90,"column_start":5,"column_end":12,"is_primary":true,"text":[{"text":"    message: String,","highlight_start":5,"highlight_end":12}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":1988,"byte_end":1997,"line_start":91,"line_end":91,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    component: String,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`LogEntry` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `timestamp`, `level`, `message`, and `component` are never read\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/tui.rs:88:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m87\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct LogEntry {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m88\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    timestamp: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m89\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    level: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m90\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    message: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m91\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    component: String,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `LogEntry` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"fields `engine` and `telemetry` are never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/tui.rs","byte_start":4029,"byte_end":4032,"line_start":149,"line_end":149,"column_start":8,"column_end":11,"is_primary":false,"text":[{"text":"struct App {","highlight_start":8,"highlight_end":11}],"label":"fields in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":4060,"byte_end":4066,"line_start":151,"line_end":151,"column_start":5,"column_end":11,"is_primary":true,"text":[{"text":"    engine: Arc<ChatterboxEngine>,","highlight_start":5,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/bin/tui.rs","byte_start":4095,"byte_end":4104,"line_start":152,"line_end":152,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    telemetry: Arc<TelemetryCollector>,","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: fields `engine` and `telemetry` are never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/tui.rs:151:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m149\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mstruct App {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m---\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfields in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m150\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    state: AppState,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m151\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    engine: Arc<ChatterboxEngine>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m152\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    telemetry: Arc<TelemetryCollector>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"missing documentation for the crate","code":{"code":"missing_docs","explanation":null},"level":"warning","spans":[{"file_name":"src/bin/tui.rs","byte_start":0,"byte_end":17614,"line_start":1,"line_end":519,"column_start":1,"column_end":2,"is_primary":true,"text":[{"text":"use chatterbox_enterprise::{","highlight_start":1,"highlight_end":29},{"text":"    config::Config,","highlight_start":1,"highlight_end":20},{"text":"    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},","highlight_start":1,"highlight_end":70},{"text":"    audio::{AudioData, SampleRate},","highlight_start":1,"highlight_end":36},{"text":"    models::ModelManager,","highlight_start":1,"highlight_end":26},{"text":"    security::WatermarkEngine,","highlight_start":1,"highlight_end":31},{"text":"    telemetry::TelemetryCollector,","highlight_start":1,"highlight_end":35},{"text":"    Result,","highlight_start":1,"highlight_end":12},{"text":"};","highlight_start":1,"highlight_end":3},{"text":"use crossterm::{","highlight_start":1,"highlight_end":17},{"text":"    event::{self, DisableMouseCapture, EnableMouseCapture, Event, KeyCode, KeyEventKind},","highlight_start":1,"highlight_end":90},{"text":"    execute,","highlight_start":1,"highlight_end":13},{"text":"    terminal::{disable_raw_mode, enable_raw_mode, EnterAlternateScreen, LeaveAlternateScreen},","highlight_start":1,"highlight_end":95},{"text":"};","highlight_start":1,"highlight_end":3},{"text":"use ratatui::{","highlight_start":1,"highlight_end":15},{"text":"    backend::{Backend, CrosstermBackend},","highlight_start":1,"highlight_end":42},{"text":"    layout::{Alignment, Constraint, Direction, Layout, Margin, Rect},","highlight_start":1,"highlight_end":70},{"text":"    style::{Color, Modifier, Style},","highlight_start":1,"highlight_end":37},{"text":"    symbols,","highlight_start":1,"highlight_end":13},{"text":"    text::{Line, Span, Text},","highlight_start":1,"highlight_end":30},{"text":"    widgets::{","highlight_start":1,"highlight_end":15},{"text":"        Block, Borders, Clear, Gauge, List, ListItem, ListState, Paragraph, Tabs, Wrap,","highlight_start":1,"highlight_end":88},{"text":"        BarChart,","highlight_start":1,"highlight_end":18},{"text":"    },","highlight_start":1,"highlight_end":7},{"text":"    Frame, Terminal,","highlight_start":1,"highlight_end":21},{"text":"};","highlight_start":1,"highlight_end":3},{"text":"use std::sync::Arc;","highlight_start":1,"highlight_end":20},{"text":"use std::time::{Duration, Instant};","highlight_start":1,"highlight_end":36},{"text":"use tokio::sync::mpsc;","highlight_start":1,"highlight_end":23},{"text":"use tokio::time::sleep;","highlight_start":1,"highlight_end":24},{"text":"","highlight_start":1,"highlight_end":1},{"text":"#[derive(Debug, Clone)]","highlight_start":1,"highlight_end":24},{"text":"enum AppEvent {","highlight_start":1,"highlight_end":16},{"text":"    Tick,","highlight_start":1,"highlight_end":10},{"text":"    Key(KeyCode),","highlight_start":1,"highlight_end":18},{"text":"    Resize,","highlight_start":1,"highlight_end":12},{"text":"    UpdateMetrics,","highlight_start":1,"highlight_end":19},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"#[derive(Debug, Clone, PartialEq)]","highlight_start":1,"highlight_end":35},{"text":"enum AppMode {","highlight_start":1,"highlight_end":15},{"text":"    Dashboard,","highlight_start":1,"highlight_end":15},{"text":"    TtsMode,","highlight_start":1,"highlight_end":13},{"text":"    SttMode,","highlight_start":1,"highlight_end":13},{"text":"    Models,","highlight_start":1,"highlight_end":12},{"text":"    Settings,","highlight_start":1,"highlight_end":14},{"text":"    Logs,","highlight_start":1,"highlight_end":10},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"#[derive(Debug, Clone)]","highlight_start":1,"highlight_end":24},{"text":"struct AppState {","highlight_start":1,"highlight_end":18},{"text":"    mode: AppMode,","highlight_start":1,"highlight_end":19},{"text":"    selected_tab: usize,","highlight_start":1,"highlight_end":25},{"text":"    tts_text: String,","highlight_start":1,"highlight_end":22},{"text":"    tts_voice: String,","highlight_start":1,"highlight_end":23},{"text":"    stt_file: String,","highlight_start":1,"highlight_end":22},{"text":"    models: Vec<ModelInfo>,","highlight_start":1,"highlight_end":28},{"text":"    metrics: SystemMetrics,","highlight_start":1,"highlight_end":28},{"text":"    logs: Vec<LogEntry>,","highlight_start":1,"highlight_end":25},{"text":"    should_quit: bool,","highlight_start":1,"highlight_end":23},{"text":"    popup_visible: bool,","highlight_start":1,"highlight_end":25},{"text":"    popup_message: String,","highlight_start":1,"highlight_end":27},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"#[derive(Debug, Clone)]","highlight_start":1,"highlight_end":24},{"text":"struct ModelInfo {","highlight_start":1,"highlight_end":19},{"text":"    name: String,","highlight_start":1,"highlight_end":18},{"text":"    model_type: String,","highlight_start":1,"highlight_end":24},{"text":"    language: String,","highlight_start":1,"highlight_end":22},{"text":"    status: String,","highlight_start":1,"highlight_end":20},{"text":"    memory_usage: u64,","highlight_start":1,"highlight_end":23},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"#[derive(Debug, Clone)]","highlight_start":1,"highlight_end":24},{"text":"struct SystemMetrics {","highlight_start":1,"highlight_end":23},{"text":"    cpu_usage: f64,","highlight_start":1,"highlight_end":20},{"text":"    memory_usage: f64,","highlight_start":1,"highlight_end":23},{"text":"    gpu_usage: f64,","highlight_start":1,"highlight_end":20},{"text":"    active_connections: u32,","highlight_start":1,"highlight_end":29},{"text":"    requests_per_second: f64,","highlight_start":1,"highlight_end":30},{"text":"    error_rate: f64,","highlight_start":1,"highlight_end":21},{"text":"    uptime: Duration,","highlight_start":1,"highlight_end":22},{"text":"    models_loaded: u32,","highlight_start":1,"highlight_end":24},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"#[derive(Debug, Clone)]","highlight_start":1,"highlight_end":24},{"text":"struct LogEntry {","highlight_start":1,"highlight_end":18},{"text":"    timestamp: String,","highlight_start":1,"highlight_end":23},{"text":"    level: String,","highlight_start":1,"highlight_end":19},{"text":"    message: String,","highlight_start":1,"highlight_end":21},{"text":"    component: String,","highlight_start":1,"highlight_end":23},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"impl Default for AppState {","highlight_start":1,"highlight_end":28},{"text":"    fn default() -> Self {","highlight_start":1,"highlight_end":27},{"text":"        Self {","highlight_start":1,"highlight_end":15},{"text":"            mode: AppMode::Dashboard,","highlight_start":1,"highlight_end":38},{"text":"            selected_tab: 0,","highlight_start":1,"highlight_end":29},{"text":"            tts_text: String::new(),","highlight_start":1,"highlight_end":37},{"text":"            tts_voice: \"en-us-female-1\".to_string(),","highlight_start":1,"highlight_end":53},{"text":"            stt_file: String::new(),","highlight_start":1,"highlight_end":37},{"text":"            models: vec![","highlight_start":1,"highlight_end":26},{"text":"                ModelInfo {","highlight_start":1,"highlight_end":28},{"text":"                    name: \"en-us-female-1\".to_string(),","highlight_start":1,"highlight_end":56},{"text":"                    model_type: \"TTS\".to_string(),","highlight_start":1,"highlight_end":51},{"text":"                    language: \"en-US\".to_string(),","highlight_start":1,"highlight_end":51},{"text":"                    status: \"Loaded\".to_string(),","highlight_start":1,"highlight_end":50},{"text":"                    memory_usage: 512,","highlight_start":1,"highlight_end":39},{"text":"                },","highlight_start":1,"highlight_end":19},{"text":"                ModelInfo {","highlight_start":1,"highlight_end":28},{"text":"                    name: \"whisper-large-v3\".to_string(),","highlight_start":1,"highlight_end":58},{"text":"                    model_type: \"STT\".to_string(),","highlight_start":1,"highlight_end":51},{"text":"                    language: \"Multi\".to_string(),","highlight_start":1,"highlight_end":51},{"text":"                    status: \"Loaded\".to_string(),","highlight_start":1,"highlight_end":50},{"text":"                    memory_usage: 1024,","highlight_start":1,"highlight_end":40},{"text":"                },","highlight_start":1,"highlight_end":19},{"text":"            ],","highlight_start":1,"highlight_end":15},{"text":"            metrics: SystemMetrics {","highlight_start":1,"highlight_end":37},{"text":"                cpu_usage: 45.2,","highlight_start":1,"highlight_end":33},{"text":"                memory_usage: 67.8,","highlight_start":1,"highlight_end":36},{"text":"                gpu_usage: 23.1,","highlight_start":1,"highlight_end":33},{"text":"                active_connections: 12,","highlight_start":1,"highlight_end":40},{"text":"                requests_per_second: 15.7,","highlight_start":1,"highlight_end":43},{"text":"                error_rate: 0.02,","highlight_start":1,"highlight_end":34},{"text":"                uptime: Duration::from_secs(3600),","highlight_start":1,"highlight_end":51},{"text":"                models_loaded: 2,","highlight_start":1,"highlight_end":34},{"text":"            },","highlight_start":1,"highlight_end":15},{"text":"            logs: vec![","highlight_start":1,"highlight_end":24},{"text":"                LogEntry {","highlight_start":1,"highlight_end":27},{"text":"                    timestamp: \"2024-01-01 12:00:00\".to_string(),","highlight_start":1,"highlight_end":66},{"text":"                    level: \"INFO\".to_string(),","highlight_start":1,"highlight_end":47},{"text":"                    message: \"Chatterbox Engine initialized successfully\".to_string(),","highlight_start":1,"highlight_end":87},{"text":"                    component: \"Engine\".to_string(),","highlight_start":1,"highlight_end":53},{"text":"                },","highlight_start":1,"highlight_end":19},{"text":"                LogEntry {","highlight_start":1,"highlight_end":27},{"text":"                    timestamp: \"2024-01-01 12:00:01\".to_string(),","highlight_start":1,"highlight_end":66},{"text":"                    level: \"INFO\".to_string(),","highlight_start":1,"highlight_end":47},{"text":"                    message: \"TTS model loaded: en-us-female-1\".to_string(),","highlight_start":1,"highlight_end":77},{"text":"                    component: \"ModelManager\".to_string(),","highlight_start":1,"highlight_end":59},{"text":"                },","highlight_start":1,"highlight_end":19},{"text":"            ],","highlight_start":1,"highlight_end":15},{"text":"            should_quit: false,","highlight_start":1,"highlight_end":32},{"text":"            popup_visible: false,","highlight_start":1,"highlight_end":34},{"text":"            popup_message: String::new(),","highlight_start":1,"highlight_end":42},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"struct App {","highlight_start":1,"highlight_end":13},{"text":"    state: AppState,","highlight_start":1,"highlight_end":21},{"text":"    engine: Arc<ChatterboxEngine>,","highlight_start":1,"highlight_end":35},{"text":"    telemetry: Arc<TelemetryCollector>,","highlight_start":1,"highlight_end":40},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"impl App {","highlight_start":1,"highlight_end":11},{"text":"    async fn new() -> Result<Self> {","highlight_start":1,"highlight_end":37},{"text":"        // Initialize configuration and engine","highlight_start":1,"highlight_end":47},{"text":"        let config = Config::default();","highlight_start":1,"highlight_end":40},{"text":"        let model_manager = Arc::new(ModelManager::new(Arc::new(config.clone())).await?);","highlight_start":1,"highlight_end":90},{"text":"        let watermark_engine = Arc::new(WatermarkEngine::new(1.0)?);","highlight_start":1,"highlight_end":69},{"text":"        let telemetry = Arc::new(TelemetryCollector::new());","highlight_start":1,"highlight_end":61},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        let engine = Arc::new(ChatterboxEngine::new(config).await?);","highlight_start":1,"highlight_end":69},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        Ok(Self {","highlight_start":1,"highlight_end":18},{"text":"            state: AppState::default(),","highlight_start":1,"highlight_end":40},{"text":"            engine,","highlight_start":1,"highlight_end":20},{"text":"            telemetry,","highlight_start":1,"highlight_end":23},{"text":"        })","highlight_start":1,"highlight_end":11},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    fn handle_key_event(&mut self, key: KeyCode) {","highlight_start":1,"highlight_end":51},{"text":"        match key {","highlight_start":1,"highlight_end":20},{"text":"            KeyCode::Char('q') => self.state.should_quit = true,","highlight_start":1,"highlight_end":65},{"text":"            KeyCode::Esc => {","highlight_start":1,"highlight_end":30},{"text":"                if self.state.popup_visible {","highlight_start":1,"highlight_end":46},{"text":"                    self.state.popup_visible = false;","highlight_start":1,"highlight_end":54},{"text":"                } else {","highlight_start":1,"highlight_end":25},{"text":"                    self.state.mode = AppMode::Dashboard;","highlight_start":1,"highlight_end":58},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            },","highlight_start":1,"highlight_end":15},{"text":"            KeyCode::Tab => {","highlight_start":1,"highlight_end":30},{"text":"                self.state.selected_tab = (self.state.selected_tab + 1) % 6;","highlight_start":1,"highlight_end":77},{"text":"                self.state.mode = match self.state.selected_tab {","highlight_start":1,"highlight_end":66},{"text":"                    0 => AppMode::Dashboard,","highlight_start":1,"highlight_end":45},{"text":"                    1 => AppMode::TtsMode,","highlight_start":1,"highlight_end":43},{"text":"                    2 => AppMode::SttMode,","highlight_start":1,"highlight_end":43},{"text":"                    3 => AppMode::Models,","highlight_start":1,"highlight_end":42},{"text":"                    4 => AppMode::Settings,","highlight_start":1,"highlight_end":44},{"text":"                    5 => AppMode::Logs,","highlight_start":1,"highlight_end":40},{"text":"                    _ => AppMode::Dashboard,","highlight_start":1,"highlight_end":45},{"text":"                };","highlight_start":1,"highlight_end":19},{"text":"            },","highlight_start":1,"highlight_end":15},{"text":"            KeyCode::Enter => {","highlight_start":1,"highlight_end":32},{"text":"                match self.state.mode {","highlight_start":1,"highlight_end":40},{"text":"                    AppMode::TtsMode => {","highlight_start":1,"highlight_end":42},{"text":"                        if !self.state.tts_text.is_empty() {","highlight_start":1,"highlight_end":61},{"text":"                            self.state.popup_message = \"TTS synthesis started...\".to_string();","highlight_start":1,"highlight_end":95},{"text":"                            self.state.popup_visible = true;","highlight_start":1,"highlight_end":61},{"text":"                        }","highlight_start":1,"highlight_end":26},{"text":"                    },","highlight_start":1,"highlight_end":23},{"text":"                    AppMode::SttMode => {","highlight_start":1,"highlight_end":42},{"text":"                        if !self.state.stt_file.is_empty() {","highlight_start":1,"highlight_end":61},{"text":"                            self.state.popup_message = \"STT recognition started...\".to_string();","highlight_start":1,"highlight_end":97},{"text":"                            self.state.popup_visible = true;","highlight_start":1,"highlight_end":61},{"text":"                        }","highlight_start":1,"highlight_end":26},{"text":"                    },","highlight_start":1,"highlight_end":23},{"text":"                    _ => {},","highlight_start":1,"highlight_end":29},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            },","highlight_start":1,"highlight_end":15},{"text":"            KeyCode::Char(c) => {","highlight_start":1,"highlight_end":34},{"text":"                match self.state.mode {","highlight_start":1,"highlight_end":40},{"text":"                    AppMode::TtsMode => {","highlight_start":1,"highlight_end":42},{"text":"                        self.state.tts_text.push(c);","highlight_start":1,"highlight_end":53},{"text":"                    },","highlight_start":1,"highlight_end":23},{"text":"                    AppMode::SttMode => {","highlight_start":1,"highlight_end":42},{"text":"                        self.state.stt_file.push(c);","highlight_start":1,"highlight_end":53},{"text":"                    },","highlight_start":1,"highlight_end":23},{"text":"                    _ => {},","highlight_start":1,"highlight_end":29},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            },","highlight_start":1,"highlight_end":15},{"text":"            KeyCode::Backspace => {","highlight_start":1,"highlight_end":36},{"text":"                match self.state.mode {","highlight_start":1,"highlight_end":40},{"text":"                    AppMode::TtsMode => {","highlight_start":1,"highlight_end":42},{"text":"                        self.state.tts_text.pop();","highlight_start":1,"highlight_end":51},{"text":"                    },","highlight_start":1,"highlight_end":23},{"text":"                    AppMode::SttMode => {","highlight_start":1,"highlight_end":42},{"text":"                        self.state.stt_file.pop();","highlight_start":1,"highlight_end":51},{"text":"                    },","highlight_start":1,"highlight_end":23},{"text":"                    _ => {},","highlight_start":1,"highlight_end":29},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            },","highlight_start":1,"highlight_end":15},{"text":"            _ => {},","highlight_start":1,"highlight_end":21},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    async fn update_metrics(&mut self) {","highlight_start":1,"highlight_end":41},{"text":"        // Simulate metric updates","highlight_start":1,"highlight_end":35},{"text":"        self.state.metrics.cpu_usage = 40.0 + (rand::random::<f64>() * 20.0);","highlight_start":1,"highlight_end":78},{"text":"        self.state.metrics.memory_usage = 60.0 + (rand::random::<f64>() * 20.0);","highlight_start":1,"highlight_end":81},{"text":"        self.state.metrics.gpu_usage = 20.0 + (rand::random::<f64>() * 30.0);","highlight_start":1,"highlight_end":78},{"text":"        self.state.metrics.requests_per_second = 10.0 + (rand::random::<f64>() * 20.0);","highlight_start":1,"highlight_end":88},{"text":"        self.state.metrics.error_rate = rand::random::<f64>() * 0.1;","highlight_start":1,"highlight_end":69},{"text":"        self.state.metrics.uptime += Duration::from_secs(1);","highlight_start":1,"highlight_end":61},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"#[tokio::main]","highlight_start":1,"highlight_end":15},{"text":"async fn main() -> Result<()> {","highlight_start":1,"highlight_end":32},{"text":"    // Initialize terminal","highlight_start":1,"highlight_end":27},{"text":"    enable_raw_mode()?;","highlight_start":1,"highlight_end":24},{"text":"    let mut stdout = std::io::stdout();","highlight_start":1,"highlight_end":40},{"text":"    execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;","highlight_start":1,"highlight_end":65},{"text":"    let backend = CrosstermBackend::new(stdout);","highlight_start":1,"highlight_end":49},{"text":"    let mut terminal = Terminal::new(backend)?;","highlight_start":1,"highlight_end":48},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Create app","highlight_start":1,"highlight_end":18},{"text":"    let mut app = App::new().await?;","highlight_start":1,"highlight_end":37},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Create event channel","highlight_start":1,"highlight_end":28},{"text":"    let (tx, mut rx) = mpsc::unbounded_channel();","highlight_start":1,"highlight_end":50},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Spawn event handler","highlight_start":1,"highlight_end":27},{"text":"    let tx_clone = tx.clone();","highlight_start":1,"highlight_end":31},{"text":"    tokio::spawn(async move {","highlight_start":1,"highlight_end":30},{"text":"        let mut last_tick = Instant::now();","highlight_start":1,"highlight_end":44},{"text":"        let tick_rate = Duration::from_millis(250);","highlight_start":1,"highlight_end":52},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        loop {","highlight_start":1,"highlight_end":15},{"text":"            let timeout = tick_rate","highlight_start":1,"highlight_end":36},{"text":"                .checked_sub(last_tick.elapsed())","highlight_start":1,"highlight_end":50},{"text":"                .unwrap_or_else(|| Duration::from_secs(0));","highlight_start":1,"highlight_end":60},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            if event::poll(timeout).unwrap() {","highlight_start":1,"highlight_end":47},{"text":"                if let Ok(event) = event::read() {","highlight_start":1,"highlight_end":51},{"text":"                    match event {","highlight_start":1,"highlight_end":34},{"text":"                        Event::Key(key) => {","highlight_start":1,"highlight_end":45},{"text":"                            if key.kind == KeyEventKind::Press {","highlight_start":1,"highlight_end":65},{"text":"                                let _ = tx_clone.send(AppEvent::Key(key.code));","highlight_start":1,"highlight_end":80},{"text":"                            }","highlight_start":1,"highlight_end":30},{"text":"                        },","highlight_start":1,"highlight_end":27},{"text":"                        Event::Resize(_, _) => {","highlight_start":1,"highlight_end":49},{"text":"                            let _ = tx_clone.send(AppEvent::Resize);","highlight_start":1,"highlight_end":69},{"text":"                        },","highlight_start":1,"highlight_end":27},{"text":"                        _ => {},","highlight_start":1,"highlight_end":33},{"text":"                    }","highlight_start":1,"highlight_end":22},{"text":"                }","highlight_start":1,"highlight_end":18},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"","highlight_start":1,"highlight_end":1},{"text":"            if last_tick.elapsed() >= tick_rate {","highlight_start":1,"highlight_end":50},{"text":"                let _ = tx_clone.send(AppEvent::Tick);","highlight_start":1,"highlight_end":55},{"text":"                last_tick = Instant::now();","highlight_start":1,"highlight_end":44},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"    });","highlight_start":1,"highlight_end":8},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Spawn metrics updater","highlight_start":1,"highlight_end":29},{"text":"    let tx_metrics = tx.clone();","highlight_start":1,"highlight_end":33},{"text":"    tokio::spawn(async move {","highlight_start":1,"highlight_end":30},{"text":"        let mut interval = tokio::time::interval(Duration::from_secs(2));","highlight_start":1,"highlight_end":74},{"text":"        loop {","highlight_start":1,"highlight_end":15},{"text":"            interval.tick().await;","highlight_start":1,"highlight_end":35},{"text":"            let _ = tx_metrics.send(AppEvent::UpdateMetrics);","highlight_start":1,"highlight_end":62},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"    });","highlight_start":1,"highlight_end":8},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Main loop","highlight_start":1,"highlight_end":17},{"text":"    while !app.state.should_quit {","highlight_start":1,"highlight_end":35},{"text":"        terminal.draw(|f| ui(f, &app.state))?;","highlight_start":1,"highlight_end":47},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        if let Some(event) = rx.recv().await {","highlight_start":1,"highlight_end":47},{"text":"            match event {","highlight_start":1,"highlight_end":26},{"text":"                AppEvent::Key(key) => app.handle_key_event(key),","highlight_start":1,"highlight_end":65},{"text":"                AppEvent::Tick => {},","highlight_start":1,"highlight_end":38},{"text":"                AppEvent::Resize => {},","highlight_start":1,"highlight_end":40},{"text":"                AppEvent::UpdateMetrics => app.update_metrics().await,","highlight_start":1,"highlight_end":71},{"text":"            }","highlight_start":1,"highlight_end":14},{"text":"        }","highlight_start":1,"highlight_end":10},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Restore terminal","highlight_start":1,"highlight_end":24},{"text":"    disable_raw_mode()?;","highlight_start":1,"highlight_end":25},{"text":"    execute!(","highlight_start":1,"highlight_end":14},{"text":"        terminal.backend_mut(),","highlight_start":1,"highlight_end":32},{"text":"        LeaveAlternateScreen,","highlight_start":1,"highlight_end":30},{"text":"        DisableMouseCapture","highlight_start":1,"highlight_end":28},{"text":"    )?;","highlight_start":1,"highlight_end":8},{"text":"    terminal.show_cursor()?;","highlight_start":1,"highlight_end":29},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    Ok(())","highlight_start":1,"highlight_end":11},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"fn ui(f: &mut Frame, state: &AppState) {","highlight_start":1,"highlight_end":41},{"text":"    let chunks = Layout::default()","highlight_start":1,"highlight_end":35},{"text":"        .direction(Direction::Vertical)","highlight_start":1,"highlight_end":40},{"text":"        .constraints([","highlight_start":1,"highlight_end":23},{"text":"            Constraint::Length(3),  // Header","highlight_start":1,"highlight_end":46},{"text":"            Constraint::Min(0),     // Main content","highlight_start":1,"highlight_end":52},{"text":"            Constraint::Length(3),  // Footer","highlight_start":1,"highlight_end":46},{"text":"        ])","highlight_start":1,"highlight_end":11},{"text":"        .split(f.size());","highlight_start":1,"highlight_end":26},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Header with tabs","highlight_start":1,"highlight_end":24},{"text":"    let tab_titles = vec![\"Dashboard\", \"TTS\", \"STT\", \"Models\", \"Settings\", \"Logs\"];","highlight_start":1,"highlight_end":84},{"text":"    let tabs = Tabs::new(tab_titles)","highlight_start":1,"highlight_end":37},{"text":"        .block(Block::default().borders(Borders::ALL).title(\"Chatterbox Enterprise TUI\"))","highlight_start":1,"highlight_end":90},{"text":"        .style(Style::default().fg(Color::White))","highlight_start":1,"highlight_end":50},{"text":"        .highlight_style(Style::default().fg(Color::Yellow).add_modifier(Modifier::BOLD))","highlight_start":1,"highlight_end":90},{"text":"        .select(state.selected_tab);","highlight_start":1,"highlight_end":37},{"text":"    f.render_widget(tabs, chunks[0]);","highlight_start":1,"highlight_end":38},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Main content based on selected mode","highlight_start":1,"highlight_end":43},{"text":"    match state.mode {","highlight_start":1,"highlight_end":23},{"text":"        AppMode::Dashboard => render_dashboard(f, chunks[1], state),","highlight_start":1,"highlight_end":69},{"text":"        AppMode::TtsMode => render_tts_mode(f, chunks[1], state),","highlight_start":1,"highlight_end":66},{"text":"        AppMode::SttMode => render_tts_mode(f, chunks[1], state), // Placeholder","highlight_start":1,"highlight_end":81},{"text":"        AppMode::Models => render_tts_mode(f, chunks[1], state), // Placeholder","highlight_start":1,"highlight_end":80},{"text":"        AppMode::Settings => render_tts_mode(f, chunks[1], state), // Placeholder","highlight_start":1,"highlight_end":82},{"text":"        AppMode::Logs => render_tts_mode(f, chunks[1], state), // Placeholder","highlight_start":1,"highlight_end":78},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Footer with help","highlight_start":1,"highlight_end":24},{"text":"    let help_text = match state.mode {","highlight_start":1,"highlight_end":39},{"text":"        AppMode::Dashboard => \"Tab: Switch tabs | q: Quit | ESC: Back to dashboard\",","highlight_start":1,"highlight_end":85},{"text":"        AppMode::TtsMode => \"Type text, Enter: Synthesize | Tab: Switch tabs | q: Quit\",","highlight_start":1,"highlight_end":89},{"text":"        AppMode::SttMode => \"Type file path, Enter: Recognize | Tab: Switch tabs | q: Quit\",","highlight_start":1,"highlight_end":93},{"text":"        _ => \"Tab: Switch tabs | q: Quit | ESC: Back to dashboard\",","highlight_start":1,"highlight_end":68},{"text":"    };","highlight_start":1,"highlight_end":7},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    let footer = Paragraph::new(help_text)","highlight_start":1,"highlight_end":43},{"text":"        .block(Block::default().borders(Borders::ALL).title(\"Help\"))","highlight_start":1,"highlight_end":69},{"text":"        .style(Style::default().fg(Color::Gray))","highlight_start":1,"highlight_end":49},{"text":"        .alignment(Alignment::Center);","highlight_start":1,"highlight_end":39},{"text":"    f.render_widget(footer, chunks[2]);","highlight_start":1,"highlight_end":40},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Popup if visible (placeholder)","highlight_start":1,"highlight_end":38},{"text":"    if state.popup_visible {","highlight_start":1,"highlight_end":29},{"text":"        // Popup rendering would go here","highlight_start":1,"highlight_end":41},{"text":"    }","highlight_start":1,"highlight_end":6},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"fn render_dashboard(f: &mut Frame, area: Rect, state: &AppState) {","highlight_start":1,"highlight_end":67},{"text":"    let chunks = Layout::default()","highlight_start":1,"highlight_end":35},{"text":"        .direction(Direction::Vertical)","highlight_start":1,"highlight_end":40},{"text":"        .constraints([","highlight_start":1,"highlight_end":23},{"text":"            Constraint::Length(8),  // System metrics","highlight_start":1,"highlight_end":54},{"text":"            Constraint::Min(0),     // Charts and details","highlight_start":1,"highlight_end":58},{"text":"        ])","highlight_start":1,"highlight_end":11},{"text":"        .split(area);","highlight_start":1,"highlight_end":22},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // System metrics","highlight_start":1,"highlight_end":22},{"text":"    let metrics_chunks = Layout::default()","highlight_start":1,"highlight_end":43},{"text":"        .direction(Direction::Horizontal)","highlight_start":1,"highlight_end":42},{"text":"        .constraints([","highlight_start":1,"highlight_end":23},{"text":"            Constraint::Percentage(25),","highlight_start":1,"highlight_end":40},{"text":"            Constraint::Percentage(25),","highlight_start":1,"highlight_end":40},{"text":"            Constraint::Percentage(25),","highlight_start":1,"highlight_end":40},{"text":"            Constraint::Percentage(25),","highlight_start":1,"highlight_end":40},{"text":"        ])","highlight_start":1,"highlight_end":11},{"text":"        .split(chunks[0]);","highlight_start":1,"highlight_end":27},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // CPU Usage","highlight_start":1,"highlight_end":17},{"text":"    let cpu_gauge = Gauge::default()","highlight_start":1,"highlight_end":37},{"text":"        .block(Block::default().borders(Borders::ALL).title(\"CPU Usage\"))","highlight_start":1,"highlight_end":74},{"text":"        .gauge_style(Style::default().fg(Color::Green))","highlight_start":1,"highlight_end":56},{"text":"        .percent(state.metrics.cpu_usage as u16)","highlight_start":1,"highlight_end":49},{"text":"        .label(format!(\"{:.1}%\", state.metrics.cpu_usage));","highlight_start":1,"highlight_end":60},{"text":"    f.render_widget(cpu_gauge, metrics_chunks[0]);","highlight_start":1,"highlight_end":51},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Memory Usage","highlight_start":1,"highlight_end":20},{"text":"    let memory_gauge = Gauge::default()","highlight_start":1,"highlight_end":40},{"text":"        .block(Block::default().borders(Borders::ALL).title(\"Memory Usage\"))","highlight_start":1,"highlight_end":77},{"text":"        .gauge_style(Style::default().fg(Color::Blue))","highlight_start":1,"highlight_end":55},{"text":"        .percent(state.metrics.memory_usage as u16)","highlight_start":1,"highlight_end":52},{"text":"        .label(format!(\"{:.1}%\", state.metrics.memory_usage));","highlight_start":1,"highlight_end":63},{"text":"    f.render_widget(memory_gauge, metrics_chunks[1]);","highlight_start":1,"highlight_end":54},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // GPU Usage","highlight_start":1,"highlight_end":17},{"text":"    let gpu_gauge = Gauge::default()","highlight_start":1,"highlight_end":37},{"text":"        .block(Block::default().borders(Borders::ALL).title(\"GPU Usage\"))","highlight_start":1,"highlight_end":74},{"text":"        .gauge_style(Style::default().fg(Color::Magenta))","highlight_start":1,"highlight_end":58},{"text":"        .percent(state.metrics.gpu_usage as u16)","highlight_start":1,"highlight_end":49},{"text":"        .label(format!(\"{:.1}%\", state.metrics.gpu_usage));","highlight_start":1,"highlight_end":60},{"text":"    f.render_widget(gpu_gauge, metrics_chunks[2]);","highlight_start":1,"highlight_end":51},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Active Connections","highlight_start":1,"highlight_end":26},{"text":"    let connections_text = format!(","highlight_start":1,"highlight_end":36},{"text":"        \"Active: {}\\nRPS: {:.1}\\nErrors: {:.3}%\\nUptime: {}h\",","highlight_start":1,"highlight_end":63},{"text":"        state.metrics.active_connections,","highlight_start":1,"highlight_end":42},{"text":"        state.metrics.requests_per_second,","highlight_start":1,"highlight_end":43},{"text":"        state.metrics.error_rate * 100.0,","highlight_start":1,"highlight_end":42},{"text":"        state.metrics.uptime.as_secs() / 3600","highlight_start":1,"highlight_end":46},{"text":"    );","highlight_start":1,"highlight_end":7},{"text":"    let connections_widget = Paragraph::new(connections_text)","highlight_start":1,"highlight_end":62},{"text":"        .block(Block::default().borders(Borders::ALL).title(\"System Stats\"))","highlight_start":1,"highlight_end":77},{"text":"        .style(Style::default().fg(Color::White));","highlight_start":1,"highlight_end":51},{"text":"    f.render_widget(connections_widget, metrics_chunks[3]);","highlight_start":1,"highlight_end":60},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Performance chart area","highlight_start":1,"highlight_end":30},{"text":"    let chart_chunks = Layout::default()","highlight_start":1,"highlight_end":41},{"text":"        .direction(Direction::Horizontal)","highlight_start":1,"highlight_end":42},{"text":"        .constraints([Constraint::Percentage(50), Constraint::Percentage(50)])","highlight_start":1,"highlight_end":79},{"text":"        .split(chunks[1]);","highlight_start":1,"highlight_end":27},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Request rate chart (simplified)","highlight_start":1,"highlight_end":39},{"text":"    let request_data = vec![","highlight_start":1,"highlight_end":29},{"text":"        (\"00:00\", state.metrics.requests_per_second as u64),","highlight_start":1,"highlight_end":61},{"text":"        (\"00:01\", (state.metrics.requests_per_second * 1.1) as u64),","highlight_start":1,"highlight_end":69},{"text":"        (\"00:02\", (state.metrics.requests_per_second * 0.9) as u64),","highlight_start":1,"highlight_end":69},{"text":"        (\"00:03\", (state.metrics.requests_per_second * 1.2) as u64),","highlight_start":1,"highlight_end":69},{"text":"        (\"00:04\", state.metrics.requests_per_second as u64),","highlight_start":1,"highlight_end":61},{"text":"    ];","highlight_start":1,"highlight_end":7},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    let request_chart = BarChart::default()","highlight_start":1,"highlight_end":44},{"text":"        .block(Block::default().borders(Borders::ALL).title(\"Request Rate (RPS)\"))","highlight_start":1,"highlight_end":83},{"text":"        .data(&request_data)","highlight_start":1,"highlight_end":29},{"text":"        .bar_width(8)","highlight_start":1,"highlight_end":22},{"text":"        .bar_style(Style::default().fg(Color::Yellow))","highlight_start":1,"highlight_end":55},{"text":"        .value_style(Style::default().fg(Color::White));","highlight_start":1,"highlight_end":57},{"text":"    f.render_widget(request_chart, chart_chunks[0]);","highlight_start":1,"highlight_end":53},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Model status","highlight_start":1,"highlight_end":20},{"text":"    let model_items: Vec<ListItem> = state.models.iter().map(|model| {","highlight_start":1,"highlight_end":71},{"text":"        let status_color = match model.status.as_str() {","highlight_start":1,"highlight_end":57},{"text":"            \"Loaded\" => Color::Green,","highlight_start":1,"highlight_end":38},{"text":"            \"Loading\" => Color::Yellow,","highlight_start":1,"highlight_end":40},{"text":"            \"Error\" => Color::Red,","highlight_start":1,"highlight_end":35},{"text":"            _ => Color::Gray,","highlight_start":1,"highlight_end":30},{"text":"        };","highlight_start":1,"highlight_end":11},{"text":"","highlight_start":1,"highlight_end":1},{"text":"        ListItem::new(Line::from(vec![","highlight_start":1,"highlight_end":39},{"text":"            Span::styled(&model.name, Style::default().fg(Color::White)),","highlight_start":1,"highlight_end":74},{"text":"            Span::raw(\" - \"),","highlight_start":1,"highlight_end":30},{"text":"            Span::styled(&model.status, Style::default().fg(status_color)),","highlight_start":1,"highlight_end":76},{"text":"            Span::raw(format!(\" ({}MB)\", model.memory_usage)),","highlight_start":1,"highlight_end":63},{"text":"        ]))","highlight_start":1,"highlight_end":12},{"text":"    }).collect();","highlight_start":1,"highlight_end":18},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    let models_list = List::new(model_items)","highlight_start":1,"highlight_end":45},{"text":"        .block(Block::default().borders(Borders::ALL).title(\"Loaded Models\"))","highlight_start":1,"highlight_end":78},{"text":"        .style(Style::default().fg(Color::White));","highlight_start":1,"highlight_end":51},{"text":"    f.render_widget(models_list, chart_chunks[1]);","highlight_start":1,"highlight_end":51},{"text":"}","highlight_start":1,"highlight_end":2},{"text":"","highlight_start":1,"highlight_end":1},{"text":"fn render_tts_mode(f: &mut Frame, area: Rect, state: &AppState) {","highlight_start":1,"highlight_end":66},{"text":"    let chunks = Layout::default()","highlight_start":1,"highlight_end":35},{"text":"        .direction(Direction::Vertical)","highlight_start":1,"highlight_end":40},{"text":"        .constraints([","highlight_start":1,"highlight_end":23},{"text":"            Constraint::Length(3),  // Voice selection","highlight_start":1,"highlight_end":55},{"text":"            Constraint::Min(5),     // Text input","highlight_start":1,"highlight_end":50},{"text":"            Constraint::Length(3),  // Status","highlight_start":1,"highlight_end":46},{"text":"        ])","highlight_start":1,"highlight_end":11},{"text":"        .split(area);","highlight_start":1,"highlight_end":22},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Voice selection","highlight_start":1,"highlight_end":23},{"text":"    let voice_text = format!(\"Selected Voice: {}\", state.tts_voice);","highlight_start":1,"highlight_end":69},{"text":"    let voice_widget = Paragraph::new(voice_text)","highlight_start":1,"highlight_end":50},{"text":"        .block(Block::default().borders(Borders::ALL).title(\"Voice Model\"))","highlight_start":1,"highlight_end":76},{"text":"        .style(Style::default().fg(Color::Cyan));","highlight_start":1,"highlight_end":50},{"text":"    f.render_widget(voice_widget, chunks[0]);","highlight_start":1,"highlight_end":46},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Text input","highlight_start":1,"highlight_end":18},{"text":"    let text_widget = Paragraph::new(state.tts_text.as_str())","highlight_start":1,"highlight_end":62},{"text":"        .block(Block::default().borders(Borders::ALL).title(\"Text to Synthesize\"))","highlight_start":1,"highlight_end":83},{"text":"        .style(Style::default().fg(Color::White))","highlight_start":1,"highlight_end":50},{"text":"        .wrap(Wrap { trim: true });","highlight_start":1,"highlight_end":36},{"text":"    f.render_widget(text_widget, chunks[1]);","highlight_start":1,"highlight_end":45},{"text":"","highlight_start":1,"highlight_end":1},{"text":"    // Status","highlight_start":1,"highlight_end":14},{"text":"    let status_text = if state.tts_text.is_empty() {","highlight_start":1,"highlight_end":53},{"text":"        \"Enter text to synthesize...\"","highlight_start":1,"highlight_end":38},{"text":"    } else {","highlight_start":1,"highlight_end":13},{"text":"        \"Press Enter to start synthesis\"","highlight_start":1,"highlight_end":41},{"text":"    };","highlight_start":1,"highlight_end":7},{"text":"    let status_widget = Paragraph::new(status_text)","highlight_start":1,"highlight_end":52},{"text":"        .block(Block::default().borders(Borders::ALL).title(\"Status\"))","highlight_start":1,"highlight_end":71},{"text":"        .style(Style::default().fg(Color::Green));","highlight_start":1,"highlight_end":51},{"text":"    f.render_widget(status_widget, chunks[2]);","highlight_start":1,"highlight_end":47},{"text":"}","highlight_start":1,"highlight_end":2}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"requested on the command line with `-W missing-docs`","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: missing documentation for the crate\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/bin/tui.rs:1:1\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m/\u001b[0m\u001b[0m \u001b[0m\u001b[0muse chatterbox_enterprise::{\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    config::Config,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m3\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    audio::{AudioData, SampleRate},\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m518\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    f.render_widget(status_widget, chunks[2]);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m519\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m}\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|_^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: requested on the command line with `-W missing-docs`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"16 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 16 warnings emitted\u001b[0m\n\n"}
