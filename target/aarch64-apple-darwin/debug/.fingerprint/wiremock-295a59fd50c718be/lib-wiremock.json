{"rustc": 657458298742096754, "features": "[]", "declared_features": "[]", "target": 1962341611837544381, "profile": 4374887572363265115, "path": 15189957918497775029, "deps": [[902078471074753561, "async_trait", false, 4723537573814239992], [2070739116102306658, "tokio", false, 8569350391102415802], [6690072151007321369, "hyper", false, 15632731755558843783], [7009065068979121087, "http_body_util", false, 6614264961954982111], [8244776183334334055, "once_cell", false, 17236654666591032151], [9253677898334269643, "base64", false, 8903416241417351076], [10633404241517405153, "serde", false, 7420612406277754670], [10840051415924403621, "deadpool", false, 10303010149370348767], [11268000880778227980, "hyper_util", false, 1032929265729432536], [11641382387439738731, "regex", false, 11609625493149959071], [12368485582061518913, "http", false, 12477151170489463076], [12509852874546367857, "serde_json", false, 797747138307893778], [14788668256999697643, "assert_json_diff", false, 4841862895113108680], [15399619262696441677, "log", false, 10128934488753385547], [15447401961974210701, "futures", false, 10382177876458617892], [18130989770956114225, "url", false, 7123811269099291646]], "local": [{"CheckDepInfo": {"dep_info": "aarch64-apple-darwin/debug/.fingerprint/wiremock-295a59fd50c718be/dep-lib-wiremock", "checksum": false}}], "rustflags": ["--cfg", "wasmedge", "--cfg", "tokio_unstable"], "metadata": 11829039582575452724, "config": 2202906307356721367, "compile_kind": 14493662952515048101}