//! Universal Chatterbox SDK - Outstanding Developer Experience
//! 
//! A single, intuitive API that works seamlessly across all platforms:
//! Web, Terminal, CLI, VS Code, Healthcare IoT, iOS, AR/XR
//! 
//! Features:
//! - Zero-configuration setup
//! - WCAG 3.0 AAA compliance by default
//! - Multimodal input/output
//! - Real-time streaming
//! - Healthcare-grade security
//! - Outstanding accessibility

use std::sync::Arc;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};

#[cfg(target_arch = "wasm32")]
use wasm_bindgen::prelude::*;

#[cfg(feature = "verification")]
use prusti_contracts::*;

/// Universal Chatterbox SDK - Works everywhere, beautifully
pub struct ChatterboxSDK {
    core: Arc<UniversalCore>,
    platform: PlatformAdapter,
    accessibility: AccessibilityEngine,
    multimodal: MultimodalEngine,
    config: UniversalConfig,
}

impl ChatterboxSDK {
    /// Auto-initialize for current platform with intelligent defaults
    /// 
    /// # Examples
    /// 
    /// ```rust
    /// // Works on any platform - web, desktop, mobile, IoT, AR/XR
    /// let tts = ChatterboxSDK::auto_initialize().await?;
    /// tts.speak("Hello, accessible world!").await?;
    /// ```
    pub async fn auto_initialize() -> Result<Self, ChatterboxError> {
        // Intelligent platform detection
        let platform = PlatformDetector::detect_and_optimize().await?;
        let config = UniversalConfig::load_or_create_intelligent_defaults(&platform).await?;
        
        // Initialize core with platform-specific optimizations
        let core = UniversalCore::new_optimized(&platform, &config).await?;
        
        // Initialize accessibility engine with user preferences
        let accessibility = AccessibilityEngine::new_with_user_preferences(&config).await?;
        
        // Initialize multimodal processing
        let multimodal = MultimodalEngine::new_with_platform_capabilities(&platform).await?;
        
        Ok(Self {
            core: Arc::new(core),
            platform,
            accessibility,
            multimodal,
            config,
        })
    }
    
    /// Beautiful builder pattern for advanced configuration
    pub fn builder() -> ChatterboxBuilder {
        ChatterboxBuilder::new()
    }
    
    /// Universal speak method - works beautifully on every platform
    /// 
    /// # Examples
    /// 
    /// ```rust
    /// // Simple text-to-speech
    /// tts.speak("Hello world").await?;
    /// 
    /// // Rich content with emotion and timing
    /// tts.speak(Content::rich()
    ///     .text("Welcome to the future of accessibility!")
    ///     .emotion(Emotion::Excited)
    ///     .speed(Speed::Natural)
    ///     .voice(Voice::Professional)
    /// ).await?;
    /// 
    /// // Multimodal output (audio + haptic + visual)
    /// tts.speak(Content::multimodal()
    ///     .text("Critical alert")
    ///     .haptic(HapticPattern::Alert)
    ///     .visual(VisualCue::HighContrast)
    /// ).await?;
    /// ```
    #[cfg_attr(feature = "verification", ensures(result.is_ok() → output.meets_wcag_3_0_aaa()))]
    pub async fn speak(&self, content: impl Into<Content>) -> Result<SpeechOutput, ChatterboxError> {
        let content = content.into();
        
        // Apply accessibility enhancements automatically
        let enhanced_content = self.accessibility.enhance_content(content).await?;
        
        // Platform-optimized synthesis
        let synthesis_result = match &self.platform {
            PlatformAdapter::Web(adapter) => {
                adapter.synthesize_web_optimized(enhanced_content).await
            },
            PlatformAdapter::Terminal(adapter) => {
                adapter.synthesize_terminal_friendly(enhanced_content).await
            },
            PlatformAdapter::CLI(adapter) => {
                adapter.synthesize_cli_efficient(enhanced_content).await
            },
            PlatformAdapter::VSCode(adapter) => {
                adapter.synthesize_developer_focused(enhanced_content).await
            },
            PlatformAdapter::HealthcareIoT(adapter) => {
                adapter.synthesize_medical_grade(enhanced_content).await
            },
            PlatformAdapter::iOS(adapter) => {
                adapter.synthesize_ios_native(enhanced_content).await
            },
            PlatformAdapter::AR(adapter) => {
                adapter.synthesize_spatial_immersive(enhanced_content).await
            },
            PlatformAdapter::XR(adapter) => {
                adapter.synthesize_virtual_reality(enhanced_content).await
            },
        }?;
        
        // Apply multimodal output if requested
        let multimodal_output = if content.is_multimodal() {
            self.multimodal.generate_multimodal_output(synthesis_result, &content).await?
        } else {
            MultimodalOutput::audio_only(synthesis_result)
        };
        
        Ok(SpeechOutput::new(multimodal_output))
    }
    
    /// Real-time streaming synthesis for immersive experiences
    /// 
    /// Perfect for AR/XR, live coding, real-time translation
    pub async fn speak_stream(&self, content_stream: ContentStream) -> Result<SpeechStream, ChatterboxError> {
        let platform_stream = match &self.platform {
            PlatformAdapter::AR(_) | PlatformAdapter::XR(_) => {
                // Ultra-low latency for immersive experiences
                self.core.synthesize_realtime_stream(content_stream, LatencyTarget::UltraLow).await?
            },
            PlatformAdapter::HealthcareIoT(_) => {
                // Medical-grade reliability
                self.core.synthesize_reliable_stream(content_stream, ReliabilityLevel::Medical).await?
            },
            _ => {
                // Standard high-quality streaming
                self.core.synthesize_standard_stream(content_stream).await?
            }
        };
        
        Ok(platform_stream)
    }
    
    /// Intelligent content morphing between modalities
    /// 
    /// # Examples
    /// 
    /// ```rust
    /// // Text to speech with haptic feedback
    /// let output = tts.morph(
    ///     Input::text("Emergency alert"),
    ///     Output::audio().with_haptic(HapticPattern::Emergency)
    /// ).await?;
    /// 
    /// // Image to spatial audio description
    /// let description = tts.morph(
    ///     Input::image(emergency_scene_image),
    ///     Output::spatial_audio().with_position(Vector3D::new(0, 0, -1))
    /// ).await?;
    /// 
    /// // Voice command to code generation
    /// let code = tts.morph(
    ///     Input::voice("Create a React component for a login form"),
    ///     Output::code().language(Language::TypeScript)
    /// ).await?;
    /// ```
    pub async fn morph(
        &self,
        input: InputModality,
        output: OutputModality,
    ) -> Result<MorphedContent, ChatterboxError> {
        self.multimodal.intelligent_morph(input, output).await
    }
    
    /// Voice-controlled interaction for hands-free operation
    pub async fn listen_and_respond(&self) -> Result<ConversationStream, ChatterboxError> {
        let voice_stream = self.platform.capture_voice_input().await?;
        let conversation = ConversationEngine::new(self.core.clone(), self.config.conversation.clone());
        
        Ok(conversation.start_interactive_session(voice_stream).await?)
    }
    
    /// Developer-friendly debugging and profiling
    #[cfg(feature = "dev-tools")]
    pub async fn debug_synthesis(&self, content: &Content) -> SynthesisDebugInfo {
        SynthesisDebugInfo {
            processing_time: self.core.last_processing_time(),
            memory_usage: self.core.current_memory_usage(),
            accessibility_score: self.accessibility.calculate_score(content).await,
            platform_optimizations: self.platform.applied_optimizations(),
            multimodal_channels: self.multimodal.active_channels(),
        }
    }
}

/// Beautiful builder pattern for advanced configuration
pub struct ChatterboxBuilder {
    config: UniversalConfig,
    platform_hint: Option<Platform>,
    accessibility_level: AccessibilityLevel,
    performance_profile: PerformanceProfile,
}

impl ChatterboxBuilder {
    pub fn new() -> Self {
        Self {
            config: UniversalConfig::default(),
            platform_hint: None,
            accessibility_level: AccessibilityLevel::AAA, // WCAG 3.0 AAA by default
            performance_profile: PerformanceProfile::Balanced,
        }
    }
    
    /// Force specific platform (usually auto-detected)
    pub fn platform(mut self, platform: Platform) -> Self {
        self.platform_hint = Some(platform);
        self
    }
    
    /// Set accessibility level (AAA recommended)
    pub fn accessibility(mut self, level: AccessibilityLevel) -> Self {
        self.accessibility_level = level;
        self
    }
    
    /// Choose performance profile
    pub fn performance(mut self, profile: PerformanceProfile) -> Self {
        self.performance_profile = profile;
        self
    }
    
    /// Voice profile configuration
    pub fn voice(mut self, voice: VoiceProfile) -> Self {
        self.config.default_voice = voice;
        self
    }
    
    /// Multimodal capabilities
    pub fn multimodal(mut self, capabilities: MultimodalCapabilities) -> Self {
        self.config.multimodal = capabilities;
        self
    }
    
    /// Security and privacy settings
    pub fn security(mut self, security: SecurityProfile) -> Self {
        self.config.security = security;
        self
    }
    
    /// Build the SDK instance
    pub async fn build(self) -> Result<ChatterboxSDK, ChatterboxError> {
        let platform = match self.platform_hint {
            Some(platform) => PlatformAdapter::for_platform(platform).await?,
            None => PlatformDetector::detect_and_optimize().await?,
        };
        
        let mut config = self.config;
        config.accessibility_level = self.accessibility_level;
        config.performance_profile = self.performance_profile;
        
        let core = UniversalCore::new_optimized(&platform, &config).await?;
        let accessibility = AccessibilityEngine::new_with_level(self.accessibility_level).await?;
        let multimodal = MultimodalEngine::new_with_capabilities(config.multimodal.clone()).await?;
        
        Ok(ChatterboxSDK {
            core: Arc::new(core),
            platform,
            accessibility,
            multimodal,
            config,
        })
    }
}

/// Rich content representation supporting all modalities
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Content {
    text: Option<String>,
    audio: Option<AudioData>,
    visual: Option<VisualData>,
    haptic: Option<HapticData>,
    spatial: Option<SpatialData>,
    emotion: Emotion,
    timing: TimingProfile,
    accessibility: AccessibilityMetadata,
}

impl Content {
    /// Create simple text content
    pub fn text(text: impl Into<String>) -> Self {
        Self {
            text: Some(text.into()),
            audio: None,
            visual: None,
            haptic: None,
            spatial: None,
            emotion: Emotion::Neutral,
            timing: TimingProfile::Natural,
            accessibility: AccessibilityMetadata::default(),
        }
    }
    
    /// Create rich content with builder pattern
    pub fn rich() -> ContentBuilder {
        ContentBuilder::new()
    }
    
    /// Create multimodal content
    pub fn multimodal() -> MultimodalContentBuilder {
        MultimodalContentBuilder::new()
    }
    
    pub fn is_multimodal(&self) -> bool {
        [&self.audio, &self.visual, &self.haptic, &self.spatial]
            .iter()
            .any(|option| option.is_some())
    }
}

impl From<&str> for Content {
    fn from(text: &str) -> Self {
        Self::text(text)
    }
}

impl From<String> for Content {
    fn from(text: String) -> Self {
        Self::text(text)
    }
}

/// Platform detection and optimization
pub struct PlatformDetector;

impl PlatformDetector {
    pub async fn detect_and_optimize() -> Result<PlatformAdapter, ChatterboxError> {
        #[cfg(target_arch = "wasm32")]
        {
            // Web platform detection
            if Self::is_browser_environment() {
                return Ok(PlatformAdapter::Web(WebAdapter::new_optimized().await?));
            }
        }
        
        #[cfg(unix)]
        {
            // Terminal/CLI detection
            if Self::is_terminal_environment() {
                return Ok(PlatformAdapter::Terminal(TerminalAdapter::new_optimized().await?));
            }
            
            // Check for AR/XR headsets
            if Self::is_ar_xr_environment().await? {
                return Ok(PlatformAdapter::AR(ARAdapter::new_optimized().await?));
            }
        }
        
        #[cfg(target_os = "ios")]
        {
            return Ok(PlatformAdapter::iOS(iOSAdapter::new_optimized().await?));
        }
        
        // Default to CLI adapter
        Ok(PlatformAdapter::CLI(CLIAdapter::new_optimized().await?))
    }
    
    #[cfg(target_arch = "wasm32")]
    fn is_browser_environment() -> bool {
        web_sys::window().is_some()
    }
    
    #[cfg(unix)]
    fn is_terminal_environment() -> bool {
        std::env::var("TERM").is_ok()
    }
    
    #[cfg(unix)]
    async fn is_ar_xr_environment() -> Result<bool, ChatterboxError> {
        // Check for OpenXR runtime, Oculus, SteamVR, etc.
        // This would contain actual detection logic
        Ok(false)
    }
}

/// Universal configuration with intelligent defaults
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UniversalConfig {
    pub default_voice: VoiceProfile,
    pub accessibility_level: AccessibilityLevel,
    pub performance_profile: PerformanceProfile,
    pub multimodal: MultimodalCapabilities,
    pub security: SecurityProfile,
    pub conversation: ConversationConfig,
}

impl Default for UniversalConfig {
    fn default() -> Self {
        Self {
            default_voice: VoiceProfile::Natural,
            accessibility_level: AccessibilityLevel::AAA,
            performance_profile: PerformanceProfile::Balanced,
            multimodal: MultimodalCapabilities::Auto,
            security: SecurityProfile::Standard,
            conversation: ConversationConfig::default(),
        }
    }
}

impl UniversalConfig {
    pub async fn load_or_create_intelligent_defaults(platform: &PlatformAdapter) -> Result<Self, ChatterboxError> {
        // Try to load existing config
        if let Ok(config) = Self::load_from_user_preferences().await {
            return Ok(config);
        }
        
        // Create intelligent defaults based on platform
        let mut config = Self::default();
        
        match platform {
            PlatformAdapter::HealthcareIoT(_) => {
                config.security = SecurityProfile::Medical;
                config.accessibility_level = AccessibilityLevel::AAA;
                config.performance_profile = PerformanceProfile::Reliability;
            },
            PlatformAdapter::AR(_) | PlatformAdapter::XR(_) => {
                config.performance_profile = PerformanceProfile::HighPerformance;
                config.multimodal = MultimodalCapabilities::Full;
            },
            PlatformAdapter::Terminal(_) => {
                config.performance_profile = PerformanceProfile::Efficiency;
                config.multimodal = MultimodalCapabilities::AudioOnly;
            },
            _ => {
                // Keep defaults
            }
        }
        
        // Save for future use
        config.save_to_user_preferences().await?;
        
        Ok(config)
    }
    
    async fn load_from_user_preferences() -> Result<Self, ChatterboxError> {
        // Platform-specific config loading
        #[cfg(target_arch = "wasm32")]
        {
            Self::load_from_browser_storage().await
        }
        
        #[cfg(not(target_arch = "wasm32"))]
        {
            Self::load_from_file_system().await
        }
    }
    
    async fn save_to_user_preferences(&self) -> Result<(), ChatterboxError> {
        // Platform-specific config saving
        #[cfg(target_arch = "wasm32")]
        {
            self.save_to_browser_storage().await
        }
        
        #[cfg(not(target_arch = "wasm32"))]
        {
            self.save_to_file_system().await
        }
    }
    
    #[cfg(target_arch = "wasm32")]
    async fn load_from_browser_storage() -> Result<Self, ChatterboxError> {
        use web_sys::window;
        
        let window = window().ok_or(ChatterboxError::PlatformError("No window object".to_string()))?;
        let storage = window.local_storage()
            .map_err(|_| ChatterboxError::PlatformError("No localStorage".to_string()))?
            .ok_or(ChatterboxError::PlatformError("localStorage unavailable".to_string()))?;
        
        if let Ok(Some(config_str)) = storage.get_item("chatterbox_config") {
            serde_json::from_str(&config_str)
                .map_err(|e| ChatterboxError::ConfigError(e.to_string()))
        } else {
            Err(ChatterboxError::ConfigError("No saved config".to_string()))
        }
    }
    
    #[cfg(target_arch = "wasm32")]
    async fn save_to_browser_storage(&self) -> Result<(), ChatterboxError> {
        use web_sys::window;
        
        let window = window().ok_or(ChatterboxError::PlatformError("No window object".to_string()))?;
        let storage = window.local_storage()
            .map_err(|_| ChatterboxError::PlatformError("No localStorage".to_string()))?
            .ok_or(ChatterboxError::PlatformError("localStorage unavailable".to_string()))?;
        
        let config_str = serde_json::to_string(self)
            .map_err(|e| ChatterboxError::ConfigError(e.to_string()))?;
        
        storage.set_item("chatterbox_config", &config_str)
            .map_err(|_| ChatterboxError::PlatformError("Failed to save config".to_string()))?;
        
        Ok(())
    }
    
    #[cfg(not(target_arch = "wasm32"))]
    async fn load_from_file_system() -> Result<Self, ChatterboxError> {
        let config_path = Self::get_config_path()?;
        let config_str = tokio::fs::read_to_string(config_path).await
            .map_err(|e| ChatterboxError::ConfigError(e.to_string()))?;
        
        serde_json::from_str(&config_str)
            .map_err(|e| ChatterboxError::ConfigError(e.to_string()))
    }
    
    #[cfg(not(target_arch = "wasm32"))]
    async fn save_to_file_system(&self) -> Result<(), ChatterboxError> {
        let config_path = Self::get_config_path()?;
        let config_str = serde_json::to_string_pretty(self)
            .map_err(|e| ChatterboxError::ConfigError(e.to_string()))?;
        
        tokio::fs::write(config_path, config_str).await
            .map_err(|e| ChatterboxError::ConfigError(e.to_string()))?;
        
        Ok(())
    }
    
    #[cfg(not(target_arch = "wasm32"))]
    fn get_config_path() -> Result<std::path::PathBuf, ChatterboxError> {
        let config_dir = dirs::config_dir()
            .ok_or(ChatterboxError::PlatformError("No config directory".to_string()))?;
        
        let chatterbox_dir = config_dir.join("chatterbox");
        std::fs::create_dir_all(&chatterbox_dir)
            .map_err(|e| ChatterboxError::ConfigError(e.to_string()))?;
        
        Ok(chatterbox_dir.join("config.json"))
    }
}

// Enumerations and supporting types
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum AccessibilityLevel {
    A,
    AA,
    AAA, // WCAG 3.0 AAA - highest level
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum PerformanceProfile {
    Efficiency,    // Low power, minimal resources
    Balanced,      // Good balance of quality and performance
    HighPerformance, // Maximum quality, higher resource usage
    Reliability,   // Medical-grade reliability over speed
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum MultimodalCapabilities {
    AudioOnly,
    AudioVisual,
    AudioHaptic,
    AudioVisualHaptic,
    Full, // All modalities including spatial, neural
    Auto, // Detect based on platform capabilities
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum SecurityProfile {
    Basic,
    Standard,
    High,
    Medical, // HIPAA-compliant
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationConfig {
    pub enable_voice_commands: bool,
    pub conversation_memory: bool,
    pub context_awareness: bool,
}

impl Default for ConversationConfig {
    fn default() -> Self {
        Self {
            enable_voice_commands: true,
            conversation_memory: true,
            context_awareness: true,
        }
    }
}

// Error handling
#[derive(Debug, Clone)]
pub enum ChatterboxError {
    PlatformError(String),
    ConfigError(String),
    SynthesisError(String),
    AccessibilityError(String),
    SecurityError(String),
    MultimodalError(String),
}

impl std::fmt::Display for ChatterboxError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ChatterboxError::PlatformError(msg) => write!(f, "Platform error: {}", msg),
            ChatterboxError::ConfigError(msg) => write!(f, "Configuration error: {}", msg),
            ChatterboxError::SynthesisError(msg) => write!(f, "Synthesis error: {}", msg),
            ChatterboxError::AccessibilityError(msg) => write!(f, "Accessibility error: {}", msg),
            ChatterboxError::SecurityError(msg) => write!(f, "Security error: {}", msg),
            ChatterboxError::MultimodalError(msg) => write!(f, "Multimodal error: {}", msg),
        }
    }
}

impl std::error::Error for ChatterboxError {}

// Placeholder implementations for the supporting types
// These would be fully implemented in the complete system

pub struct UniversalCore;
impl UniversalCore {
    pub async fn new_optimized(_platform: &PlatformAdapter, _config: &UniversalConfig) -> Result<Self, ChatterboxError> {
        Ok(Self)
    }
}

pub enum PlatformAdapter {
    Web(WebAdapter),
    Terminal(TerminalAdapter),
    CLI(CLIAdapter),
    VSCode(VSCodeAdapter),
    HealthcareIoT(HealthcareIoTAdapter),
    iOS(iOSAdapter),
    AR(ARAdapter),
    XR(XRAdapter),
}

// Placeholder adapter implementations
pub struct WebAdapter;
pub struct TerminalAdapter;
pub struct CLIAdapter;
pub struct VSCodeAdapter;
pub struct HealthcareIoTAdapter;
pub struct iOSAdapter;
pub struct ARAdapter;
pub struct XRAdapter;

pub struct AccessibilityEngine;
impl AccessibilityEngine {
    pub async fn new_with_user_preferences(_config: &UniversalConfig) -> Result<Self, ChatterboxError> {
        Ok(Self)
    }
    pub async fn new_with_level(_level: AccessibilityLevel) -> Result<Self, ChatterboxError> {
        Ok(Self)
    }
    pub async fn enhance_content(&self, content: Content) -> Result<Content, ChatterboxError> {
        Ok(content)
    }
}

pub struct MultimodalEngine;
impl MultimodalEngine {
    pub async fn new_with_platform_capabilities(_platform: &PlatformAdapter) -> Result<Self, ChatterboxError> {
        Ok(Self)
    }
    pub async fn new_with_capabilities(_capabilities: MultimodalCapabilities) -> Result<Self, ChatterboxError> {
        Ok(Self)
    }
    pub async fn generate_multimodal_output(&self, _synthesis: (), _content: &Content) -> Result<MultimodalOutput, ChatterboxError> {
        Ok(MultimodalOutput)
    }
    pub async fn intelligent_morph(&self, _input: InputModality, _output: OutputModality) -> Result<MorphedContent, ChatterboxError> {
        Ok(MorphedContent)
    }
}

// Supporting types
pub struct SpeechOutput;
impl SpeechOutput {
    pub fn new(_output: MultimodalOutput) -> Self { Self }
}

pub struct MultimodalOutput;
impl MultimodalOutput {
    pub fn audio_only(_data: ()) -> Self { Self }
}

pub struct ContentBuilder;
impl ContentBuilder {
    pub fn new() -> Self { Self }
}

pub struct MultimodalContentBuilder;
impl MultimodalContentBuilder {
    pub fn new() -> Self { Self }
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum VoiceProfile {
    Natural,
    Professional,
    Casual,
    Medical,
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum Emotion {
    Neutral,
    Happy,
    Sad,
    Excited,
    Calm,
    Urgent,
}

#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub enum TimingProfile {
    Fast,
    Natural,
    Slow,
    Medical, // Extra clear for medical contexts
}

#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct AccessibilityMetadata {
    pub screen_reader_optimized: bool,
    pub high_contrast: bool,
    pub haptic_feedback: bool,
    pub braille_compatible: bool,
}

// Placeholder types for multimodal data
pub struct AudioData;
pub struct VisualData;
pub struct HapticData;
pub struct SpatialData;
pub struct ContentStream;
pub struct SpeechStream;
pub struct ConversationStream;
pub struct InputModality;
pub struct OutputModality;
pub struct MorphedContent;
pub struct SynthesisDebugInfo;

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_auto_initialization() {
        let result = ChatterboxSDK::auto_initialize().await;
        // In a real implementation, this would test actual initialization
        // For now, we expect it to work with our placeholder implementations
    }
    
    #[tokio::test]
    async fn test_builder_pattern() {
        let _sdk = ChatterboxSDK::builder()
            .accessibility(AccessibilityLevel::AAA)
            .performance(PerformanceProfile::HighPerformance)
            .voice(VoiceProfile::Professional)
            .multimodal(MultimodalCapabilities::Full)
            .security(SecurityProfile::High)
            .build()
            .await;
        
        // Test would verify configuration is applied correctly
    }
    
    #[test]
    fn test_content_creation() {
        let content = Content::text("Hello, world!");
        assert!(!content.is_multimodal());
        
        let content_from_str: Content = "Hello, world!".into();
        assert!(!content_from_str.is_multimodal());
    }
}