# Multi-stage Dockerfile for Chatterbox Enterprise TTS/STT System
# MCStack v9r0 Compliant Container Build

# Build stage
FROM rust:1.75-slim as builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    ca-certificates \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy dependency files first for better caching
COPY Cargo.toml Cargo.lock build.rs ./

# Create dummy source to build dependencies
RUN mkdir src && echo "fn main() {}" > src/main.rs
RUN cargo build --release --features "mcstack-compliance,security-hardened,web-server"
RUN rm -rf src

# Copy actual source code
COPY src ./src

# Build the application
RUN cargo build --release --features "mcstack-compliance,security-hardened,web-server"

# Runtime stage
FROM debian:bookworm-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd -r chatterbox && useradd -r -g chatterbox chatterbox

# Create necessary directories
RUN mkdir -p /app/models /app/config /app/logs \
    && chown -R chatterbox:chatterbox /app

# Copy binary from builder stage
COPY --from=builder /app/target/release/chatterbox-server /usr/local/bin/chatterbox-server
RUN chmod +x /usr/local/bin/chatterbox-server

# Copy configuration
COPY config.toml /app/config/

# Switch to non-root user
USER chatterbox

# Set working directory
WORKDIR /app

# Environment variables
ENV CHATTERBOX_CONFIG=/app/config/config.toml
ENV RUST_LOG=info
ENV RUST_BACKTRACE=1

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/api/v1/health || exit 1

# Expose port
EXPOSE 8080

# Labels for metadata
LABEL org.opencontainers.image.title="Chatterbox Enterprise TTS/STT"
LABEL org.opencontainers.image.description="Enterprise-grade Text-to-Speech and Speech-to-Text system"
LABEL org.opencontainers.image.vendor="Enterprise Engineering"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.licenses="MIT"
LABEL mcstack.version="v9r0_enhanced"
LABEL mcstack.governance="GAL-3"
LABEL mcstack.security="high"

# Start the server
CMD ["chatterbox-server"]
