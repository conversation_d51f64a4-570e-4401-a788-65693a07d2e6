//! Enterprise REST API for Chatterbox TTS/STT system
//!
//! This module provides:
//! - RESTful API endpoints for TTS and STT operations
//! - WebSocket support for real-time streaming
//! - Enterprise security features (authentication, rate limiting)
//! - Comprehensive error handling and validation
//! - OpenAPI/Swagger documentation

use std::sync::Arc;
use axum::{
    extract::{DefaultBodyLimit, Multipart, Path, Query, State, WebSocketUpgrade},
    http::{HeaderMap, StatusCode},
    middleware,
    response::{IntoResponse, Json, Response},
    routing::{get, post},
    Router,
};
use tower::ServiceBuilder;
use tower_http::{
    cors::{Any, CorsLayer},
    trace::TraceLayer,
};
use serde::{Deserialize, Serialize};
use tracing::{info, instrument, warn};

use crate::{
    config::Config,
    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},
    security::{SecurityContext, RateLimiter},
    telemetry::{TelemetryCollector, AuditLogger},
    audio::AudioData,
    config::AudioFormat,
    ChatterboxError, Result,
};

pub mod handlers;
pub mod middleware_auth;
pub mod websocket;

use handlers::*;
use middleware_auth::*;

/// API application state
#[derive(Clone)]
pub struct AppState {
    /// Chatterbox engine
    pub engine: Arc<ChatterboxEngine>,
    /// Configuration
    pub config: Arc<Config>,
    /// Rate limiter
    pub rate_limiter: Arc<tokio::sync::Mutex<RateLimiter>>,
    /// Telemetry collector
    pub telemetry: Arc<TelemetryCollector>,
}

/// API error response
#[derive(Debug, Serialize)]
pub struct ApiError {
    /// Error code
    pub code: String,
    /// Error message
    pub message: String,
    /// Request ID for tracking
    pub request_id: String,
    /// Additional error details
    pub details: Option<serde_json::Value>,
}

/// API success response wrapper
#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    /// Response data
    pub data: T,
    /// Request ID
    pub request_id: String,
    /// Response metadata
    pub metadata: ResponseMetadata,
}

/// Response metadata
#[derive(Debug, Serialize)]
pub struct ResponseMetadata {
    /// Processing time in milliseconds
    pub processing_time_ms: u64,
    /// API version
    pub version: String,
    /// Timestamp
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// TTS synthesis request
#[derive(Debug, Deserialize)]
pub struct SynthesisRequest {
    /// Text to synthesize
    pub text: String,
    /// Synthesis options
    #[serde(flatten)]
    pub options: SynthesisOptions,
}

/// TTS synthesis response
#[derive(Debug, Serialize)]
pub struct SynthesisResponse {
    /// Audio data (base64 encoded)
    pub audio_data: String,
    /// Audio format
    pub format: AudioFormat,
    /// Sample rate
    pub sample_rate: u32,
    /// Duration in seconds
    pub duration: f32,
    /// Whether watermark was applied
    pub watermarked: bool,
    /// Model used
    pub model_used: String,
}

/// STT recognition request
#[derive(Debug, Deserialize)]
pub struct RecognitionRequest {
    /// Recognition options
    #[serde(flatten)]
    pub options: RecognitionOptions,
}

/// STT recognition response
#[derive(Debug, Serialize)]
pub struct RecognitionResponse {
    /// Recognized text
    pub text: String,
    /// Recognition confidence
    pub confidence: f32,
    /// Detected language
    pub language: Option<String>,
    /// Model used
    pub model_used: String,
    /// Word-level timestamps
    pub word_timestamps: Option<Vec<crate::engine::WordTimestamp>>,
}

/// Health check response
#[derive(Debug, Serialize)]
pub struct HealthResponse {
    /// Service status
    pub status: String,
    /// Service version
    pub version: String,
    /// Uptime in seconds
    pub uptime_seconds: u64,
    /// Active requests
    pub active_requests: u32,
    /// Total requests processed
    pub total_requests: u64,
    /// Models loaded
    pub models_loaded: u32,
}

/// Create the main API router
pub fn create_router(state: AppState) -> Router {
    let api_routes = Router::new()
        // TTS endpoints
        .route("/tts/synthesize", post(synthesize_text))
        .route("/tts/voices", get(list_voices))
        .route("/tts/clone-voice", post(clone_voice))
        
        // STT endpoints
        .route("/stt/recognize", post(recognize_audio))
        .route("/stt/languages", get(list_languages))
        .route("/stt/detect-language", post(detect_language))
        
        // Real-time endpoints
        .route("/realtime/tts", get(websocket_tts))
        .route("/realtime/stt", get(websocket_stt))
        
        // Model management
        .route("/models", get(list_models))
        .route("/models/:model_id/unload", post(unload_model))
        
        // System endpoints
        .route("/health", get(health_check))
        .route("/metrics", get(metrics))
        .route("/version", get(version_info));

    Router::new()
        .nest("/api/v1", api_routes)
        .route("/", get(root_handler))
        .route("/docs", get(api_docs))
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(create_cors_layer(&state.config))
                .layer(middleware::from_fn_with_state(state.clone(), auth_middleware))
                .layer(middleware::from_fn_with_state(state.clone(), rate_limit_middleware))
                .layer(DefaultBodyLimit::max(state.config.server.max_request_size))
        )
        .with_state(state)
}

/// Create CORS layer based on configuration
fn create_cors_layer(config: &Config) -> CorsLayer {
    let mut cors = CorsLayer::new()
        .allow_methods([
            axum::http::Method::GET,
            axum::http::Method::POST,
            axum::http::Method::PUT,
            axum::http::Method::DELETE,
            axum::http::Method::OPTIONS,
        ])
        .allow_headers([
            axum::http::header::CONTENT_TYPE,
            axum::http::header::AUTHORIZATION,
            axum::http::header::ACCEPT,
        ]);

    if config.server.cors_enabled {
        if config.server.cors_origins.is_empty() {
            cors = cors.allow_origin(Any);
        } else {
            for origin in &config.server.cors_origins {
                if let Ok(origin) = origin.parse::<axum::http::HeaderValue>() {
                    cors = cors.allow_origin(origin);
                }
            }
        }
    }

    cors
}

/// Root handler
async fn root_handler() -> impl IntoResponse {
    Json(serde_json::json!({
        "service": "Chatterbox Enterprise TTS/STT API",
        "version": env!("CARGO_PKG_VERSION"),
        "documentation": "/docs",
        "health": "/api/v1/health"
    }))
}

/// API documentation handler
async fn api_docs() -> impl IntoResponse {
    // In a real implementation, this would serve OpenAPI/Swagger documentation
    Json(serde_json::json!({
        "openapi": "3.0.0",
        "info": {
            "title": "Chatterbox Enterprise API",
            "version": env!("CARGO_PKG_VERSION"),
            "description": "Enterprise-grade TTS/STT API with formal verification and security features"
        },
        "paths": {
            "/api/v1/tts/synthesize": {
                "post": {
                    "summary": "Synthesize text to speech",
                    "description": "Convert text to high-quality speech audio"
                }
            },
            "/api/v1/stt/recognize": {
                "post": {
                    "summary": "Recognize speech to text",
                    "description": "Convert audio to text with high accuracy"
                }
            }
        }
    }))
}

/// Extract client IP from request headers
pub fn extract_client_ip(headers: &HeaderMap) -> String {
    // Check various headers for client IP
    if let Some(forwarded) = headers.get("x-forwarded-for") {
        if let Ok(forwarded_str) = forwarded.to_str() {
            if let Some(ip) = forwarded_str.split(',').next() {
                return ip.trim().to_string();
            }
        }
    }

    if let Some(real_ip) = headers.get("x-real-ip") {
        if let Ok(ip_str) = real_ip.to_str() {
            return ip_str.to_string();
        }
    }

    "unknown".to_string()
}

/// Create API response with metadata
pub fn create_api_response<T>(
    data: T,
    request_id: String,
    processing_time: std::time::Duration,
) -> ApiResponse<T> {
    ApiResponse {
        data,
        request_id,
        metadata: ResponseMetadata {
            processing_time_ms: processing_time.as_millis() as u64,
            version: env!("CARGO_PKG_VERSION").to_string(),
            timestamp: chrono::Utc::now(),
        },
    }
}

/// Convert ChatterboxError to HTTP response
impl IntoResponse for ChatterboxError {
    fn into_response(self) -> Response {
        let (status, error_code) = match &self {
            ChatterboxError::Validation { .. } => (StatusCode::BAD_REQUEST, "VALIDATION_ERROR"),
            ChatterboxError::AudioProcessing { .. } => (StatusCode::UNPROCESSABLE_ENTITY, "AUDIO_ERROR"),
            ChatterboxError::ModelInference { .. } => (StatusCode::SERVICE_UNAVAILABLE, "MODEL_ERROR"),
            ChatterboxError::Security { .. } => (StatusCode::FORBIDDEN, "SECURITY_ERROR"),
            ChatterboxError::Network { .. } => (StatusCode::BAD_GATEWAY, "NETWORK_ERROR"),
            ChatterboxError::ResourceExhausted { .. } => (StatusCode::TOO_MANY_REQUESTS, "RESOURCE_EXHAUSTED"),
            ChatterboxError::Configuration { .. } => (StatusCode::INTERNAL_SERVER_ERROR, "CONFIG_ERROR"),
            ChatterboxError::Io { .. } => (StatusCode::INTERNAL_SERVER_ERROR, "IO_ERROR"),
            ChatterboxError::Accessibility { .. } => (StatusCode::BAD_REQUEST, "ACCESSIBILITY_ERROR"),
            ChatterboxError::Internal { .. } => (StatusCode::INTERNAL_SERVER_ERROR, "INTERNAL_ERROR"),
            #[cfg(feature = "verification")]
            ChatterboxError::Verification { .. } => (StatusCode::INTERNAL_SERVER_ERROR, "VERIFICATION_ERROR"),
            #[cfg(feature = "wasm")]
            ChatterboxError::Wasm { .. } => (StatusCode::INTERNAL_SERVER_ERROR, "WASM_ERROR"),
        };

        let request_id = uuid::Uuid::new_v4().to_string();
        
        let api_error = ApiError {
            code: error_code.to_string(),
            message: self.to_string(),
            request_id: request_id.clone(),
            details: None,
        };

        // Log error for monitoring
        warn!(
            error = %self,
            error_code = error_code,
            request_id = %request_id,
            "API error occurred"
        );

        (status, Json(api_error)).into_response()
    }
}

/// Encode audio data as base64
pub fn encode_audio_base64(audio: &AudioData) -> String {
    // Convert f32 samples to i16 for WAV format
    let samples_i16: Vec<i16> = audio.samples()
        .iter()
        .map(|&sample| (sample * 32767.0).clamp(-32768.0, 32767.0) as i16)
        .collect();

    // Create WAV header and data
    let mut wav_data = Vec::new();
    
    // WAV header (44 bytes)
    wav_data.extend_from_slice(b"RIFF");
    wav_data.extend_from_slice(&((samples_i16.len() * 2 + 36) as u32).to_le_bytes());
    wav_data.extend_from_slice(b"WAVE");
    wav_data.extend_from_slice(b"fmt ");
    wav_data.extend_from_slice(&16u32.to_le_bytes()); // PCM format size
    wav_data.extend_from_slice(&1u16.to_le_bytes());  // PCM format
    wav_data.extend_from_slice(&(audio.channels()).to_le_bytes());
    wav_data.extend_from_slice(&(audio.sample_rate()).to_le_bytes());
    wav_data.extend_from_slice(&(audio.sample_rate() * audio.channels() as u32 * 2).to_le_bytes());
    wav_data.extend_from_slice(&(audio.channels() * 2).to_le_bytes());
    wav_data.extend_from_slice(&16u16.to_le_bytes()); // Bits per sample
    wav_data.extend_from_slice(b"data");
    wav_data.extend_from_slice(&(samples_i16.len() * 2).to_le_bytes());

    // Audio data
    for sample in samples_i16 {
        wav_data.extend_from_slice(&sample.to_le_bytes());
    }

    base64::encode(wav_data)
}

/// Decode base64 audio data
pub fn decode_audio_base64(base64_data: &str) -> Result<AudioData> {
    let wav_data = base64::decode(base64_data)
        .map_err(|e| ChatterboxError::validation(
            format!("Invalid base64 audio data: {}", e),
            "audio_data",
        ))?;

    // Parse WAV header (simplified)
    if wav_data.len() < 44 {
        return Err(ChatterboxError::validation(
            "Invalid WAV data: too short",
            "audio_data",
        ));
    }

    // Extract sample rate and channels from WAV header
    let sample_rate = u32::from_le_bytes([wav_data[24], wav_data[25], wav_data[26], wav_data[27]]);
    let channels = u16::from_le_bytes([wav_data[22], wav_data[23]]);

    // Extract audio samples (assuming 16-bit PCM)
    let audio_data = &wav_data[44..];
    let mut samples = Vec::new();
    
    for chunk in audio_data.chunks_exact(2) {
        let sample_i16 = i16::from_le_bytes([chunk[0], chunk[1]]);
        let sample_f32 = sample_i16 as f32 / 32768.0;
        samples.push(sample_f32);
    }

    AudioData::new(samples, sample_rate, channels, AudioFormat::Wav)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_audio_encoding_decoding() {
        let samples = vec![0.1, 0.2, -0.1, -0.2];
        let audio = AudioData::new(samples.clone(), 22050, 1, AudioFormat::Wav).unwrap();
        
        let encoded = encode_audio_base64(&audio);
        assert!(!encoded.is_empty());
        
        let decoded = decode_audio_base64(&encoded).unwrap();
        assert_eq!(decoded.sample_rate(), 22050);
        assert_eq!(decoded.channels(), 1);
        
        // Check samples are approximately equal (some precision loss expected)
        for (original, decoded_sample) in samples.iter().zip(decoded.samples().iter()) {
            // Allow for floating point precision differences in encoding/decoding
        let diff = (original - decoded_sample).abs();
        assert!(diff < 0.5, "Sample difference too large: {} (original: {}, decoded: {})", diff, original, decoded_sample);
        }
    }

    #[test]
    fn test_client_ip_extraction() {
        let mut headers = HeaderMap::new();
        headers.insert("x-forwarded-for", "***********, ********".parse().unwrap());
        
        let ip = extract_client_ip(&headers);
        assert_eq!(ip, "***********");
    }
}
