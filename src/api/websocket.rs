//! WebSocket handlers for real-time TTS/STT streaming

use axum::extract::ws::{Message, WebSocket};
use futures::{sink::SinkExt, stream::StreamExt};
use serde::{Deserialize, Serialize};
use tracing::{debug, error, info, warn};

use crate::{
    api::encode_audio_base64,
    engine::{SynthesisOptions, RecognitionOptions},
    ChatterboxError,
};

/// WebSocket message types for TTS
#[derive(Debug, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum TtsWebSocketMessage {
    /// Start synthesis request
    #[serde(rename = "synthesize")]
    Synthesize {
        text: String,
        options: Option<SynthesisOptions>,
    },
    /// Audio chunk response
    #[serde(rename = "audio_chunk")]
    AudioChunk {
        audio_data: String,
        chunk_index: u32,
        is_final: bool,
    },
    /// Synthesis complete
    #[serde(rename = "synthesis_complete")]
    SynthesisComplete {
        total_chunks: u32,
        duration: f32,
    },
    /// Error message
    #[serde(rename = "error")]
    Error {
        code: String,
        message: String,
    },
    /// Status message
    #[serde(rename = "status")]
    Status {
        message: String,
    },
}

/// WebSocket message types for STT
#[derive(Debug, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum SttWebSocketMessage {
    /// Start recognition session
    #[serde(rename = "start_recognition")]
    StartRecognition {
        options: Option<RecognitionOptions>,
    },
    /// Audio chunk input
    #[serde(rename = "audio_chunk")]
    AudioChunk {
        audio_data: String,
        chunk_index: u32,
    },
    /// Partial recognition result
    #[serde(rename = "partial_result")]
    PartialResult {
        text: String,
        confidence: f32,
        is_final: bool,
    },
    /// Final recognition result
    #[serde(rename = "final_result")]
    FinalResult {
        text: String,
        confidence: f32,
        word_timestamps: Option<Vec<crate::engine::WordTimestamp>>,
    },
    /// End recognition session
    #[serde(rename = "end_recognition")]
    EndRecognition,
    /// Error message
    #[serde(rename = "error")]
    Error {
        code: String,
        message: String,
    },
    /// Status message
    #[serde(rename = "status")]
    Status {
        message: String,
    },
}

/// Handle TTS WebSocket connection
pub async fn handle_tts_websocket(socket: WebSocket) {
    info!("New TTS WebSocket connection established");
    
    let (mut sender, mut receiver) = socket.split();
    
    // Send welcome message
    let welcome_msg = TtsWebSocketMessage::Status {
        message: "Connected to Chatterbox Enterprise TTS WebSocket".to_string(),
    };
    
    if let Ok(msg_json) = serde_json::to_string(&welcome_msg) {
        if sender.send(Message::Text(msg_json)).await.is_err() {
            error!("Failed to send welcome message");
            return;
        }
    }

    // Handle incoming messages
    while let Some(msg) = receiver.next().await {
        match msg {
            Ok(Message::Text(text)) => {
                debug!("Received TTS WebSocket message: {}", text);
                
                match serde_json::from_str::<TtsWebSocketMessage>(&text) {
                    Ok(TtsWebSocketMessage::Synthesize { text, options }) => {
                        if let Err(e) = handle_tts_synthesis(&mut sender, &text, options).await {
                            error!("TTS synthesis error: {}", e);
                            
                            let error_msg = TtsWebSocketMessage::Error {
                                code: "SYNTHESIS_ERROR".to_string(),
                                message: e.to_string(),
                            };
                            
                            if let Ok(error_json) = serde_json::to_string(&error_msg) {
                                let _ = sender.send(Message::Text(error_json)).await;
                            }
                        }
                    }
                    Ok(_) => {
                        warn!("Unexpected TTS WebSocket message type");
                    }
                    Err(e) => {
                        warn!("Invalid TTS WebSocket message: {}", e);
                        
                        let error_msg = TtsWebSocketMessage::Error {
                            code: "INVALID_MESSAGE".to_string(),
                            message: format!("Invalid message format: {}", e),
                        };
                        
                        if let Ok(error_json) = serde_json::to_string(&error_msg) {
                            let _ = sender.send(Message::Text(error_json)).await;
                        }
                    }
                }
            }
            Ok(Message::Close(_)) => {
                info!("TTS WebSocket connection closed");
                break;
            }
            Ok(_) => {
                warn!("Unexpected TTS WebSocket message type");
            }
            Err(e) => {
                error!("TTS WebSocket error: {}", e);
                break;
            }
        }
    }
    
    info!("TTS WebSocket connection terminated");
}

/// Handle STT WebSocket connection
pub async fn handle_stt_websocket(socket: WebSocket) {
    info!("New STT WebSocket connection established");
    
    let (mut sender, mut receiver) = socket.split();
    
    // Send welcome message
    let welcome_msg = SttWebSocketMessage::Status {
        message: "Connected to Chatterbox Enterprise STT WebSocket".to_string(),
    };
    
    if let Ok(msg_json) = serde_json::to_string(&welcome_msg) {
        if sender.send(Message::Text(msg_json)).await.is_err() {
            error!("Failed to send welcome message");
            return;
        }
    }

    let mut recognition_session: Option<crate::models::stt::RealtimeSession> = None;

    // Handle incoming messages
    while let Some(msg) = receiver.next().await {
        match msg {
            Ok(Message::Text(text)) => {
                debug!("Received STT WebSocket message: {}", text);
                
                match serde_json::from_str::<SttWebSocketMessage>(&text) {
                    Ok(SttWebSocketMessage::StartRecognition { options }) => {
                        if let Err(e) = handle_start_recognition(&mut sender, &mut recognition_session, options).await {
                            error!("STT start recognition error: {}", e);
                            send_stt_error(&mut sender, "START_RECOGNITION_ERROR", &e.to_string()).await;
                        }
                    }
                    Ok(SttWebSocketMessage::AudioChunk { audio_data, chunk_index }) => {
                        if let Err(e) = handle_audio_chunk(&mut sender, &mut recognition_session, &audio_data, chunk_index).await {
                            error!("STT audio chunk error: {}", e);
                            send_stt_error(&mut sender, "AUDIO_CHUNK_ERROR", &e.to_string()).await;
                        }
                    }
                    Ok(SttWebSocketMessage::EndRecognition) => {
                        if let Err(e) = handle_end_recognition(&mut sender, &mut recognition_session).await {
                            error!("STT end recognition error: {}", e);
                            send_stt_error(&mut sender, "END_RECOGNITION_ERROR", &e.to_string()).await;
                        }
                    }
                    Ok(_) => {
                        warn!("Unexpected STT WebSocket message type");
                    }
                    Err(e) => {
                        warn!("Invalid STT WebSocket message: {}", e);
                        send_stt_error(&mut sender, "INVALID_MESSAGE", &format!("Invalid message format: {}", e)).await;
                    }
                }
            }
            Ok(Message::Close(_)) => {
                info!("STT WebSocket connection closed");
                break;
            }
            Ok(_) => {
                warn!("Unexpected STT WebSocket message type");
            }
            Err(e) => {
                error!("STT WebSocket error: {}", e);
                break;
            }
        }
    }
    
    info!("STT WebSocket connection terminated");
}

/// Handle TTS synthesis request
async fn handle_tts_synthesis(
    sender: &mut futures::stream::SplitSink<WebSocket, Message>,
    text: &str,
    options: Option<SynthesisOptions>,
) -> Result<(), ChatterboxError> {
    // This is a simplified implementation
    // In a real system, this would:
    // 1. Stream audio chunks as they're generated
    // 2. Use the actual Chatterbox engine
    // 3. Handle real-time synthesis

    // Mock synthesis - generate fake audio data
    let audio_data = "UklGRiQAAABXQVZFZm10IBAAAAABAAEARKwAAIhYAQACABAAZGF0YQAAAAA="; // Empty WAV
    
    // Send audio chunk
    let chunk_msg = TtsWebSocketMessage::AudioChunk {
        audio_data: audio_data.to_string(),
        chunk_index: 0,
        is_final: true,
    };
    
    let chunk_json = serde_json::to_string(&chunk_msg)
        .map_err(|e| ChatterboxError::internal())?;
    
    sender.send(Message::Text(chunk_json)).await
        .map_err(|_| ChatterboxError::internal())?;
    
    // Send completion message
    let complete_msg = TtsWebSocketMessage::SynthesisComplete {
        total_chunks: 1,
        duration: 1.0,
    };
    
    let complete_json = serde_json::to_string(&complete_msg)
        .map_err(|e| ChatterboxError::internal())?;
    
    sender.send(Message::Text(complete_json)).await
        .map_err(|_| ChatterboxError::internal())?;
    
    Ok(())
}

/// Handle start recognition request
async fn handle_start_recognition(
    sender: &mut futures::stream::SplitSink<WebSocket, Message>,
    session: &mut Option<crate::models::stt::RealtimeSession>,
    options: Option<RecognitionOptions>,
) -> Result<(), ChatterboxError> {
    // Create new recognition session
    // In a real implementation, this would use the actual STT model
    *session = Some(crate::models::stt::RealtimeSession {
        id: uuid::Uuid::new_v4().to_string(),
        state: crate::models::stt::RealtimeState::Active,
        audio_buffer: Vec::new(),
        sample_rate: 16000,
    });
    
    let status_msg = SttWebSocketMessage::Status {
        message: "Recognition session started".to_string(),
    };
    
    let status_json = serde_json::to_string(&status_msg)
        .map_err(|e| ChatterboxError::internal())?;
    
    sender.send(Message::Text(status_json)).await
        .map_err(|_| ChatterboxError::internal())?;
    
    Ok(())
}

/// Handle audio chunk
async fn handle_audio_chunk(
    sender: &mut futures::stream::SplitSink<WebSocket, Message>,
    session: &mut Option<crate::models::stt::RealtimeSession>,
    audio_data: &str,
    chunk_index: u32,
) -> Result<(), ChatterboxError> {
    if session.is_none() {
        return Err(ChatterboxError::validation(
            "No active recognition session",
            "session",
        ));
    }
    
    // Mock partial result
    let partial_msg = SttWebSocketMessage::PartialResult {
        text: format!("Partial recognition result for chunk {}", chunk_index),
        confidence: 0.8,
        is_final: false,
    };
    
    let partial_json = serde_json::to_string(&partial_msg)
        .map_err(|e| ChatterboxError::internal())?;
    
    sender.send(Message::Text(partial_json)).await
        .map_err(|_| ChatterboxError::internal())?;
    
    Ok(())
}

/// Handle end recognition request
async fn handle_end_recognition(
    sender: &mut futures::stream::SplitSink<WebSocket, Message>,
    session: &mut Option<crate::models::stt::RealtimeSession>,
) -> Result<(), ChatterboxError> {
    if session.is_none() {
        return Err(ChatterboxError::validation(
            "No active recognition session",
            "session",
        ));
    }
    
    // Send final result
    let final_msg = SttWebSocketMessage::FinalResult {
        text: "Final recognition result".to_string(),
        confidence: 0.9,
        word_timestamps: None,
    };
    
    let final_json = serde_json::to_string(&final_msg)
        .map_err(|e| ChatterboxError::internal())?;
    
    sender.send(Message::Text(final_json)).await
        .map_err(|_| ChatterboxError::internal())?;
    
    // Clear session
    *session = None;
    
    Ok(())
}

/// Send STT error message
async fn send_stt_error(
    sender: &mut futures::stream::SplitSink<WebSocket, Message>,
    code: &str,
    message: &str,
) {
    let error_msg = SttWebSocketMessage::Error {
        code: code.to_string(),
        message: message.to_string(),
    };
    
    if let Ok(error_json) = serde_json::to_string(&error_msg) {
        let _ = sender.send(Message::Text(error_json)).await;
    }
}
