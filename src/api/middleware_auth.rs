//! Authentication and rate limiting middleware for Chatterbox Enterprise API

use axum::{
    extract::State,
    http::{HeaderMap, StatusCode, Request},
    middleware::Next,
    response::Response,
};
use tracing::{debug, warn};

use crate::{
    api::{AppState, extract_client_ip},
    security::SecurityContext,
    telemetry::AuditLogger,
    ChatterboxError,
};

/// Authentication middleware
pub async fn auth_middleware(
    State(state): State<AppState>,
    headers: HeaderMap,
    mut request: Request<axum::body::Body>,
    next: Next<axum::body::Body>,
) -> Result<Response, StatusCode> {
    let client_ip = extract_client_ip(&headers);
    
    // Create security context
    let mut security_context = SecurityContext::new(client_ip.clone());

    // Check for API key in Authorization header
    if let Some(auth_header) = headers.get("authorization") {
        if let Ok(auth_str) = auth_header.to_str() {
            if let Some(api_key) = auth_str.strip_prefix("Bearer ") {
                security_context = security_context.with_api_key(api_key.to_string());
            }
        }
    }

    // Check for API key in X-API-Key header
    if let Some(api_key_header) = headers.get("x-api-key") {
        if let Ok(api_key) = api_key_header.to_str() {
            security_context = security_context.with_api_key(api_key.to_string());
        }
    }

    // Validate API key if configured
    if let Some(ref expected_key) = state.config.security.api_key {
        if !security_context.validate_api_key(expected_key) {
            warn!(
                client_ip = %client_ip,
                "Authentication failed: invalid API key"
            );

            AuditLogger::security_event(
                "authentication_failed",
                None,
                &client_ip,
                "Invalid API key provided",
            );

            return Err(StatusCode::UNAUTHORIZED);
        }
    }

    // Add security context to request extensions
    request.extensions_mut().insert(security_context);

    debug!(
        client_ip = %client_ip,
        authenticated = true,
        "Request authenticated successfully"
    );

    Ok(next.run(request).await)
}

/// Rate limiting middleware
pub async fn rate_limit_middleware(
    State(state): State<AppState>,
    headers: HeaderMap,
    request: Request<axum::body::Body>,
    next: Next<axum::body::Body>,
) -> Result<Response, StatusCode> {
    // Skip rate limiting if disabled
    if !state.config.security.rate_limiting.enabled {
        return Ok(next.run(request).await);
    }

    let client_ip = extract_client_ip(&headers);
    
    // Check rate limit
    let mut rate_limiter = state.rate_limiter.lock().await;
    
    match rate_limiter.check_rate_limit(&client_ip) {
        Ok(allowed) => {
            if !allowed {
                warn!(
                    client_ip = %client_ip,
                    "Rate limit exceeded"
                );

                // Record rate limit event
                state.telemetry.record_rate_limit_exceeded(&client_ip);

                AuditLogger::security_event(
                    "rate_limit_exceeded",
                    None,
                    &client_ip,
                    "Request rate limit exceeded",
                );

                return Err(StatusCode::TOO_MANY_REQUESTS);
            }
        }
        Err(e) => {
            warn!(
                client_ip = %client_ip,
                error = %e,
                "Rate limit check failed"
            );
            return Err(StatusCode::INTERNAL_SERVER_ERROR);
        }
    }

    debug!(
        client_ip = %client_ip,
        current_count = rate_limiter.get_request_count(&client_ip),
        "Rate limit check passed"
    );

    Ok(next.run(request).await)
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::http::HeaderValue;

    #[test]
    fn test_extract_client_ip() {
        let mut headers = HeaderMap::new();
        headers.insert("x-forwarded-for", HeaderValue::from_static("***********"));
        
        let ip = extract_client_ip(&headers);
        assert_eq!(ip, "***********");
    }
}
