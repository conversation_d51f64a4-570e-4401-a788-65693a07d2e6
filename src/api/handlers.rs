//! API request handlers for Chatterbox Enterprise TTS/STT system

use std::time::Instant;
use axum::{
    extract::{Multipart, Path, Query, State, WebSocketUpgrade},
    http::HeaderMap,
    response::{IntoResponse, Json, Response},
};
use serde::{Deserialize, Serialize};
use tracing::{debug, instrument, warn};

use crate::{
    api::{
        AppState, ApiResponse, SynthesisRequest, SynthesisResponse, RecognitionRequest,
        RecognitionResponse, HealthResponse, create_api_response, encode_audio_base64,
        decode_audio_base64, extract_client_ip,
    },
    audio::AudioData,
    engine::{SynthesisOptions, RecognitionOptions},
    models::{ModelMetadata, ModelType},
    security::SecurityContext,
    telemetry::AuditLogger,
    ChatterboxError, Result,
};

/// Synthesize text to speech
#[instrument(skip(state, headers))]
pub async fn synthesize_text(
    State(state): State<AppState>,
    headers: <PERSON>er<PERSON>ap,
    <PERSON><PERSON>(request): J<PERSON><SynthesisRequest>,
) -> Result<Json<ApiResponse<SynthesisResponse>>> {
    let start_time = Instant::now();
    let request_id = uuid::Uuid::new_v4().to_string();
    let client_ip = extract_client_ip(&headers);

    // Record request
    state.telemetry.record_request("tts_synthesis");

    // Perform synthesis
    let result = state.engine.synthesize(&request.text, Some(request.options)).await?;

    // Encode audio as base64
    let audio_data = encode_audio_base64(&result.audio);

    let response_data = SynthesisResponse {
        audio_data,
        format: *result.audio.format(),
        sample_rate: result.audio.sample_rate(),
        duration: result.audio.duration(),
        watermarked: result.watermarked,
        model_used: result.model_used,
    };

    // Audit logging
    AuditLogger::access_event(
        "tts_synthesis",
        "synthesize",
        None,
        &client_ip,
        true,
    );

    let response = create_api_response(response_data, request_id, start_time.elapsed());
    Ok(Json(response))
}

/// Recognize speech from audio
#[instrument(skip(state, headers, multipart))]
pub async fn recognize_audio(
    State(state): State<AppState>,
    headers: HeaderMap,
    mut multipart: Multipart,
) -> Result<Json<ApiResponse<RecognitionResponse>>> {
    let start_time = Instant::now();
    let request_id = uuid::Uuid::new_v4().to_string();
    let client_ip = extract_client_ip(&headers);

    // Record request
    state.telemetry.record_request("stt_recognition");

    let mut audio_data: Option<AudioData> = None;
    let mut options = RecognitionOptions::default();

    // Parse multipart form data
    while let Some(field) = multipart.next_field().await.map_err(|e| {
        ChatterboxError::validation(format!("Invalid multipart data: {}", e), "multipart")
    })? {
        let name = field.name().unwrap_or("unknown");
        
        match name {
            "audio" => {
                let data = field.bytes().await.map_err(|e| {
                    ChatterboxError::validation(format!("Failed to read audio data: {}", e), "audio")
                })?;
                
                // For simplicity, assume the audio is base64 encoded
                let audio_base64 = String::from_utf8(data.to_vec()).map_err(|e| {
                    ChatterboxError::validation(format!("Invalid audio encoding: {}", e), "audio")
                })?;
                
                audio_data = Some(decode_audio_base64(&audio_base64)?);
            }
            "options" => {
                let options_data = field.bytes().await.map_err(|e| {
                    ChatterboxError::validation(format!("Failed to read options: {}", e), "options")
                })?;
                
                let options_str = String::from_utf8(options_data.to_vec()).map_err(|e| {
                    ChatterboxError::validation(format!("Invalid options encoding: {}", e), "options")
                })?;
                
                options = serde_json::from_str(&options_str).map_err(|e| {
                    ChatterboxError::validation(format!("Invalid options JSON: {}", e), "options")
                })?;
            }
            _ => {
                warn!("Unknown multipart field: {}", name);
            }
        }
    }

    let audio = audio_data.ok_or_else(|| {
        ChatterboxError::validation("Audio data is required", "audio")
    })?;

    // Perform recognition
    let result = state.engine.recognize(audio, Some(options)).await?;

    let response_data = RecognitionResponse {
        text: result.text,
        confidence: result.confidence,
        language: result.language,
        model_used: result.model_used,
        word_timestamps: result.word_timestamps,
    };

    // Audit logging
    AuditLogger::access_event(
        "stt_recognition",
        "recognize",
        None,
        &client_ip,
        true,
    );

    let response = create_api_response(response_data, request_id, start_time.elapsed());
    Ok(Json(response))
}

/// List available voices
#[instrument(skip(state))]
pub async fn list_voices(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<Vec<crate::models::tts::VoiceInfo>>>> {
    let start_time = Instant::now();
    let request_id = uuid::Uuid::new_v4().to_string();

    // Get default TTS model
    let model_name = &state.config.models.tts.default_model;
    let tts_model = state.engine.model_manager.get_tts_model(model_name).await?;
    
    let voices = tts_model.supported_voices().await?;

    let response = create_api_response(voices, request_id, start_time.elapsed());
    Ok(Json(response))
}

/// Clone a voice from reference audio
#[instrument(skip(state, headers, multipart))]
pub async fn clone_voice(
    State(state): State<AppState>,
    headers: HeaderMap,
    mut multipart: Multipart,
) -> Result<Json<ApiResponse<crate::models::tts::VoiceProfile>>> {
    let start_time = Instant::now();
    let request_id = uuid::Uuid::new_v4().to_string();
    let client_ip = extract_client_ip(&headers);

    let mut reference_audio: Option<AudioData> = None;
    let mut voice_name = String::new();

    // Parse multipart form data
    while let Some(field) = multipart.next_field().await.map_err(|e| {
        ChatterboxError::validation(format!("Invalid multipart data: {}", e), "multipart")
    })? {
        let name = field.name().unwrap_or("unknown");
        
        match name {
            "reference_audio" => {
                let data = field.bytes().await.map_err(|e| {
                    ChatterboxError::validation(format!("Failed to read audio data: {}", e), "audio")
                })?;
                
                let audio_base64 = String::from_utf8(data.to_vec()).map_err(|e| {
                    ChatterboxError::validation(format!("Invalid audio encoding: {}", e), "audio")
                })?;
                
                reference_audio = Some(decode_audio_base64(&audio_base64)?);
            }
            "voice_name" => {
                let data = field.bytes().await.map_err(|e| {
                    ChatterboxError::validation(format!("Failed to read voice name: {}", e), "voice_name")
                })?;
                
                voice_name = String::from_utf8(data.to_vec()).map_err(|e| {
                    ChatterboxError::validation(format!("Invalid voice name encoding: {}", e), "voice_name")
                })?;
            }
            _ => {
                warn!("Unknown multipart field: {}", name);
            }
        }
    }

    let audio = reference_audio.ok_or_else(|| {
        ChatterboxError::validation("Reference audio is required", "reference_audio")
    })?;

    if voice_name.is_empty() {
        return Err(ChatterboxError::validation("Voice name is required", "voice_name"));
    }

    // Get default TTS model and clone voice
    let model_name = &state.config.models.tts.default_model;
    let tts_model = state.engine.model_manager.get_tts_model(model_name).await?;
    
    let voice_profile = tts_model.clone_voice(&audio, &voice_name).await?;

    // Audit logging
    AuditLogger::access_event(
        "voice_cloning",
        "clone",
        None,
        &client_ip,
        true,
    );

    let response = create_api_response(voice_profile, request_id, start_time.elapsed());
    Ok(Json(response))
}

/// List supported languages
#[instrument(skip(state))]
pub async fn list_languages(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<Vec<crate::models::stt::LanguageInfo>>>> {
    let start_time = Instant::now();
    let request_id = uuid::Uuid::new_v4().to_string();

    // Get default STT model
    let model_name = &state.config.models.stt.default_model;
    let stt_model = state.engine.model_manager.get_stt_model(model_name).await?;
    
    let languages = stt_model.supported_languages().await?;

    let response = create_api_response(languages, request_id, start_time.elapsed());
    Ok(Json(response))
}

/// Detect language from audio
#[instrument(skip(state, headers, multipart))]
pub async fn detect_language(
    State(state): State<AppState>,
    headers: HeaderMap,
    mut multipart: Multipart,
) -> Result<Json<ApiResponse<crate::models::stt::LanguageDetectionResult>>> {
    let start_time = Instant::now();
    let request_id = uuid::Uuid::new_v4().to_string();
    let client_ip = extract_client_ip(&headers);

    let mut audio_data: Option<AudioData> = None;

    // Parse multipart form data
    while let Some(field) = multipart.next_field().await.map_err(|e| {
        ChatterboxError::validation(format!("Invalid multipart data: {}", e), "multipart")
    })? {
        let name = field.name().unwrap_or("unknown");
        
        if name == "audio" {
            let data = field.bytes().await.map_err(|e| {
                ChatterboxError::validation(format!("Failed to read audio data: {}", e), "audio")
            })?;
            
            let audio_base64 = String::from_utf8(data.to_vec()).map_err(|e| {
                ChatterboxError::validation(format!("Invalid audio encoding: {}", e), "audio")
            })?;
            
            audio_data = Some(decode_audio_base64(&audio_base64)?);
        }
    }

    let audio = audio_data.ok_or_else(|| {
        ChatterboxError::validation("Audio data is required", "audio")
    })?;

    // Get default STT model and detect language
    let model_name = &state.config.models.stt.default_model;
    let stt_model = state.engine.model_manager.get_stt_model(model_name).await?;
    
    let detection_result = stt_model.detect_language(&audio).await?;

    // Audit logging
    AuditLogger::access_event(
        "language_detection",
        "detect",
        None,
        &client_ip,
        true,
    );

    let response = create_api_response(detection_result, request_id, start_time.elapsed());
    Ok(Json(response))
}

/// List loaded models
#[instrument(skip(state))]
pub async fn list_models(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<Vec<ModelMetadata>>>> {
    let start_time = Instant::now();
    let request_id = uuid::Uuid::new_v4().to_string();

    let models = state.engine.model_manager.get_loaded_models_metadata().await;

    let response = create_api_response(models, request_id, start_time.elapsed());
    Ok(Json(response))
}

/// Unload a specific model
#[instrument(skip(state))]
pub async fn unload_model(
    State(state): State<AppState>,
    Path(model_id): Path<String>,
    Query(params): Query<UnloadModelParams>,
) -> Result<Json<ApiResponse<String>>> {
    let start_time = Instant::now();
    let request_id = uuid::Uuid::new_v4().to_string();

    let model_type = params.model_type.unwrap_or(ModelType::Tts);
    
    state.engine.model_manager.unload_model(&model_id, model_type).await?;

    let message = format!("Model '{}' unloaded successfully", model_id);
    let response = create_api_response(message, request_id, start_time.elapsed());
    Ok(Json(response))
}

#[derive(Debug, Deserialize)]
pub struct UnloadModelParams {
    pub model_type: Option<ModelType>,
}

/// Health check endpoint
#[instrument(skip(state))]
pub async fn health_check(
    State(state): State<AppState>,
) -> Result<Json<ApiResponse<HealthResponse>>> {
    let start_time = Instant::now();
    let request_id = uuid::Uuid::new_v4().to_string();

    let engine_status = state.engine.health_status().await?;

    let health_data = HealthResponse {
        status: if engine_status.is_healthy { "healthy".to_string() } else { "unhealthy".to_string() },
        version: env!("CARGO_PKG_VERSION").to_string(),
        uptime_seconds: engine_status.uptime.as_secs(),
        active_requests: engine_status.active_requests,
        total_requests: engine_status.total_requests,
        models_loaded: engine_status.models_loaded,
    };

    let response = create_api_response(health_data, request_id, start_time.elapsed());
    Ok(Json(response))
}

/// Metrics endpoint (Prometheus format)
#[instrument(skip(state))]
pub async fn metrics(
    State(state): State<AppState>,
) -> Result<Response> {
    // In a real implementation, this would export Prometheus metrics
    let metrics_data = format!(
        "# HELP chatterbox_uptime_seconds Total uptime in seconds\n\
         # TYPE chatterbox_uptime_seconds counter\n\
         chatterbox_uptime_seconds {}\n\
         \n\
         # HELP chatterbox_requests_total Total number of requests\n\
         # TYPE chatterbox_requests_total counter\n\
         chatterbox_requests_total {}\n",
        state.telemetry.uptime().as_secs(),
        0 // Would be actual request count
    );

    Ok(metrics_data.into_response())
}

/// Version information endpoint
#[instrument]
pub async fn version_info() -> Json<crate::VersionInfo> {
    Json(crate::version_info())
}

/// WebSocket handler for real-time TTS
pub async fn websocket_tts(ws: WebSocketUpgrade) -> Response {
    ws.on_upgrade(crate::api::websocket::handle_tts_websocket)
}

/// WebSocket handler for real-time STT
pub async fn websocket_stt(ws: WebSocketUpgrade) -> Response {
    ws.on_upgrade(crate::api::websocket::handle_stt_websocket)
}
