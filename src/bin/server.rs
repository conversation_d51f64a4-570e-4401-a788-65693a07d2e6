//! Enterprise Chatterbox TTS/STT Server
//!
//! This binary provides:
//! - High-performance HTTP/WebSocket server
//! - Enterprise security and monitoring
//! - Graceful shutdown handling
//! - Health checks and metrics endpoints
//! - Production-ready deployment features

use std::sync::Arc;
use tokio::signal;
use tracing::{error, info, warn};

use chatterbox_enterprise::{
    api::{create_router, AppState},
    config::Config,
    engine::ChatterboxEngine,
    security::RateLimiter,
    telemetry::TelemetryCollector,
    ChatterboxError, Result,
};

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize enterprise telemetry first
    chatterbox_enterprise::init().await?;

    info!("Starting Chatterbox Enterprise TTS/STT Server");
    info!("Version: {}", env!("CARGO_PKG_VERSION"));
    info!("MCStack v9r0 compliance: ACTIVE");

    // Load configuration
    let config = Config::from_env().map_err(|e| {
        error!("Failed to load configuration: {}", e);
        e
    })?;

    info!("Configuration loaded successfully");
    info!("Server will bind to: {}", config.bind_address());

    // Initialize Chatterbox engine
    info!("Initializing Chatterbox engine...");
    let engine = ChatterboxEngine::new(config.clone()).await.map_err(|e| {
        error!("Failed to initialize Chatterbox engine: {}", e);
        e
    })?;

    info!("Chatterbox engine initialized successfully");

    // Initialize rate limiter
    let rate_limiter = RateLimiter::new(
        config.security.rate_limiting.requests_per_minute,
        config.security.rate_limiting.burst_size,
    );

    // Initialize telemetry collector
    let telemetry = TelemetryCollector::new();

    // Create application state
    let app_state = AppState {
        engine: Arc::new(engine),
        config: Arc::new(config.clone()),
        rate_limiter: Arc::new(tokio::sync::Mutex::new(rate_limiter)),
        telemetry: Arc::new(telemetry),
    };

    // Create router with all routes and middleware
    let app = create_router(app_state.clone());

    // Create TCP listener
    let listener = tokio::net::TcpListener::bind(&config.bind_address())
        .await
        .map_err(|e| {
            error!("Failed to bind to {}: {}", config.bind_address(), e);
            ChatterboxError::configuration(
                format!("Failed to bind to address: {}", e),
                "server.bind_address",
            )
        })?;

    info!("Server listening on {}", config.bind_address());
    info!("API documentation available at: http://{}/docs", config.bind_address());
    info!("Health check available at: http://{}/api/v1/health", config.bind_address());

    // Start background tasks
    let health_monitor = start_health_monitor(app_state.clone());
    let metrics_collector = start_metrics_collector(app_state.clone());

    // Start server with graceful shutdown
    let server = axum::serve(listener, app)
        .with_graceful_shutdown(shutdown_signal());

    info!("🚀 Chatterbox Enterprise Server is ready!");
    info!("📊 Telemetry and monitoring active");
    info!("🔒 Security features enabled");
    info!("✅ Formal verification: {}", cfg!(feature = "verification"));

    // Run server and background tasks concurrently
    tokio::select! {
        result = server => {
            if let Err(e) = result {
                error!("Server error: {}", e);
                return Err(ChatterboxError::configuration(
                    format!("Server failed: {}", e),
                    "server.runtime",
                ));
            }
        }
        _ = health_monitor => {
            warn!("Health monitor task completed unexpectedly");
        }
        _ = metrics_collector => {
            warn!("Metrics collector task completed unexpectedly");
        }
    }

    info!("Chatterbox Enterprise Server shutdown complete");
    Ok(())
}

/// Handle graceful shutdown signals
async fn shutdown_signal() {
    let ctrl_c = async {
        signal::ctrl_c()
            .await
            .expect("Failed to install Ctrl+C handler");
    };

    #[cfg(unix)]
    let terminate = async {
        signal::unix::signal(signal::unix::SignalKind::terminate())
            .expect("Failed to install signal handler")
            .recv()
            .await;
    };

    #[cfg(not(unix))]
    let terminate = std::future::pending::<()>();

    tokio::select! {
        _ = ctrl_c => {
            info!("Received Ctrl+C, initiating graceful shutdown...");
        },
        _ = terminate => {
            info!("Received SIGTERM, initiating graceful shutdown...");
        },
    }

    info!("Shutdown signal received, cleaning up...");
}

/// Start health monitoring background task
async fn start_health_monitor(state: AppState) -> Result<()> {
    let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(30));

    loop {
        interval.tick().await;

        // Perform health checks
        match state.engine.health_status().await {
            Ok(status) => {
                if !status.is_healthy {
                    warn!(
                        active_requests = status.active_requests,
                        memory_usage = status.memory_usage,
                        "System health degraded"
                    );
                }

                // Update telemetry
                state.telemetry.update_active_connections(status.active_requests as i64);
                state.telemetry.update_models_loaded(status.models_loaded as i64);
                state.telemetry.update_memory_usage(status.memory_usage);
            }
            Err(e) => {
                error!("Health check failed: {}", e);
            }
        }
    }
}

/// Start metrics collection background task
async fn start_metrics_collector(state: AppState) -> Result<()> {
    let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(60));

    loop {
        interval.tick().await;

        // Collect and report metrics
        let uptime = state.telemetry.uptime();
        
        info!(
            uptime_seconds = uptime.as_secs(),
            "Metrics collection cycle completed"
        );

        // In a production system, this would:
        // - Export metrics to Prometheus
        // - Send telemetry to monitoring systems
        // - Generate alerts based on thresholds
        // - Update dashboards
    }
}

/// Print startup banner
fn print_banner() {
    println!(r#"
    ╔═══════════════════════════════════════════════════════════════╗
    ║                                                               ║
    ║   ██████╗██╗  ██╗ █████╗ ████████╗████████╗███████╗██████╗    ║
    ║  ██╔════╝██║  ██║██╔══██╗╚══██╔══╝╚══██╔══╝██╔════╝██╔══██╗   ║
    ║  ██║     ███████║███████║   ██║      ██║   █████╗  ██████╔╝   ║
    ║  ██║     ██╔══██║██╔══██║   ██║      ██║   ██╔══╝  ██╔══██╗   ║
    ║  ╚██████╗██║  ██║██║  ██║   ██║      ██║   ███████╗██║  ██║   ║
    ║   ╚═════╝╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝      ╚═╝   ╚══════╝╚═╝  ╚═╝   ║
    ║                                                               ║
    ║                    ENTERPRISE TTS/STT SYSTEM                 ║
    ║                                                               ║
    ║  🔒 MCStack v9r0 Compliant  |  🚀 Production Ready           ║
    ║  ✅ Formal Verification     |  📊 Enterprise Monitoring      ║
    ║  🛡️  Security Hardened      |  ⚡ High Performance           ║
    ║                                                               ║
    ╚═══════════════════════════════════════════════════════════════╝
    "#);
}

/// Display system information
async fn display_system_info(config: &Config) {
    info!("=== SYSTEM INFORMATION ===");
    info!("Version: {}", env!("CARGO_PKG_VERSION"));
    info!("Build: {} ({})", 
          option_env!("GIT_HASH").unwrap_or("unknown"),
          option_env!("BUILD_TIMESTAMP").unwrap_or("unknown"));
    info!("MCStack Version: v9r0_enhanced");
    info!("Governance Level: GAL-3");
    
    info!("=== CONFIGURATION ===");
    info!("Server: {}:{}", config.server.host, config.server.port);
    info!("Max Request Size: {} MB", config.server.max_request_size / 1024 / 1024);
    info!("Request Timeout: {}s", config.server.request_timeout);
    info!("CORS Enabled: {}", config.server.cors_enabled);
    
    info!("=== AUDIO CONFIGURATION ===");
    info!("Sample Rate: {} Hz", config.audio.sample_rate);
    info!("Buffer Size: {}", config.audio.buffer_size);
    info!("Max Duration: {}s", config.audio.max_duration);
    info!("Real-time Enabled: {}", config.audio.realtime_enabled);
    
    info!("=== SECURITY ===");
    info!("Watermarking: {}", config.security.watermarking_enabled);
    info!("Rate Limiting: {}", config.security.rate_limiting.enabled);
    info!("Audit Logging: {}", config.security.audit_logging);
    
    info!("=== FEATURES ===");
    info!("GPU Acceleration: {}", config.features.gpu_acceleration);
    info!("WASM Support: {}", cfg!(feature = "wasm"));
    info!("Formal Verification: {}", cfg!(feature = "verification"));
    info!("MCStack Compliance: {}", cfg!(feature = "mcstack-compliance"));
    
    info!("=== MODELS ===");
    info!("Cache Directory: {}", config.models.cache_dir.display());
    info!("Max Models in Memory: {}", config.models.max_models_in_memory);
    info!("Model Timeout: {}s", config.models.model_timeout);
    info!("Default TTS Model: {}", config.models.tts.default_model);
    info!("Default STT Model: {}", config.models.stt.default_model);
}

/// Validate system requirements
async fn validate_system_requirements() -> Result<()> {
    info!("Validating system requirements...");
    
    // Check available memory
    // In a real implementation, this would check actual system resources
    
    // Check disk space for model cache
    // In a real implementation, this would check available disk space
    
    // Validate GPU availability if enabled
    // In a real implementation, this would check CUDA/Metal availability
    
    info!("✅ System requirements validated");
    Ok(())
}

/// Initialize enterprise features
async fn initialize_enterprise_features(config: &Config) -> Result<()> {
    info!("Initializing enterprise features...");
    
    // Initialize formal verification if enabled
    #[cfg(feature = "verification")]
    {
        info!("🔍 Formal verification enabled");
        // Initialize verification runtime
    }
    
    // Initialize security features
    if config.security.watermarking_enabled {
        info!("🛡️  Perth watermarking enabled");
    }
    
    if config.security.rate_limiting.enabled {
        info!("⚡ Rate limiting enabled: {} req/min", 
              config.security.rate_limiting.requests_per_minute);
    }
    
    // Initialize telemetry
    if config.telemetry.tracing_enabled {
        info!("📊 Distributed tracing enabled");
    }
    
    if config.telemetry.metrics_enabled {
        info!("📈 Metrics collection enabled");
    }
    
    info!("✅ Enterprise features initialized");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_server_startup() {
        // Test that server can be configured without errors
        let config = Config::default();
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_banner_display() {
        // Test that banner can be printed without panicking
        print_banner();
    }
}
