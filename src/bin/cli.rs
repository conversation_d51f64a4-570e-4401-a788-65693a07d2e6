use chatterbox_enterprise::{
    config::Config,
    engine::{ChatterboxEngine, SynthesisOptions, RecognitionOptions},
    audio::{AudioData, SampleRate},
    models::ModelManager,
    security::WatermarkEngine,
    telemetry::TelemetryCollector,
    Result,
};
use clap::{Parser, Subcommand};
use colored::*;
use console::Term;
use dialoguer::{theme::ColorfulTheme, Confirm, Input, Select};
use indicatif::{ProgressBar, ProgressStyle};
use std::fs;
use std::path::PathBuf;
use std::sync::Arc;
use std::time::Instant;
use tabled::{Table, Tabled};
use tokio;

/// Chatterbox Enterprise CLI - Professional TTS/STT Command Line Interface
#[derive(Parser)]
#[command(name = "chatterbox-cli")]
#[command(about = "Enterprise-grade Text-to-Speech and Speech-to-Text CLI")]
#[command(version = "1.0.0")]
#[command(author = "Chatterbox Enterprise")]
struct Cli {
    /// Configuration file path
    #[arg(short, long, default_value = "config/config.toml")]
    config: PathBuf,

    /// Verbose output
    #[arg(short, long)]
    verbose: bool,

    /// Output format (json, table, plain)
    #[arg(short, long, default_value = "table")]
    format: String,

    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// Text-to-Speech synthesis
    Tts {
        /// Text to synthesize
        #[arg(short, long)]
        text: Option<String>,

        /// Voice model to use
        #[arg(short, long, default_value = "en-us-female-1")]
        voice: String,

        /// Output audio file
        #[arg(short, long, default_value = "output.wav")]
        output: PathBuf,

        /// Speaking rate (0.5-2.0)
        #[arg(long, default_value = "1.0")]
        rate: f32,

        /// Pitch adjustment (-1.0 to 1.0)
        #[arg(long, default_value = "0.0")]
        pitch: f32,

        /// Volume (0.0-2.0)
        #[arg(long, default_value = "1.0")]
        volume: f32,

        /// Emotion (neutral, happy, sad, angry, excited)
        #[arg(long)]
        emotion: Option<String>,

        /// Apply watermark
        #[arg(long)]
        watermark: bool,

        /// Interactive mode
        #[arg(short, long)]
        interactive: bool,
    },
    /// Speech-to-Text recognition
    Stt {
        /// Input audio file
        #[arg(short, long)]
        input: PathBuf,

        /// Language code (e.g., en-US, es-ES)
        #[arg(short, long)]
        language: Option<String>,

        /// Output text file
        #[arg(short, long)]
        output: Option<PathBuf>,

        /// Enable punctuation
        #[arg(long)]
        punctuation: bool,

        /// Confidence threshold (0.0-1.0)
        #[arg(long, default_value = "0.5")]
        confidence: f32,

        /// Enable word timestamps
        #[arg(long)]
        timestamps: bool,
    },
    /// List available models
    Models {
        /// Model type filter (tts, stt, all)
        #[arg(short, long, default_value = "all")]
        filter: String,

        /// Show detailed information
        #[arg(short, long)]
        detailed: bool,
    },
    /// System health and status
    Status {
        /// Show detailed metrics
        #[arg(short, long)]
        detailed: bool,

        /// Continuous monitoring
        #[arg(short, long)]
        watch: bool,

        /// Refresh interval in seconds
        #[arg(long, default_value = "5")]
        interval: u64,
    },
    /// Batch processing
    Batch {
        /// Input file with commands
        #[arg(short, long)]
        input: PathBuf,

        /// Output directory
        #[arg(short, long, default_value = "batch_output")]
        output: PathBuf,

        /// Number of parallel workers
        #[arg(short, long, default_value = "4")]
        workers: usize,
    },
    /// Interactive shell
    Shell,
    /// Benchmark performance
    Benchmark {
        /// Test type (tts, stt, both)
        #[arg(short, long, default_value = "both")]
        test_type: String,

        /// Number of iterations
        #[arg(short, long, default_value = "10")]
        iterations: usize,

        /// Concurrent requests
        #[arg(short, long, default_value = "1")]
        concurrent: usize,
    },
}

#[derive(Tabled)]
struct ModelInfo {
    name: String,
    model_type: String,
    language: String,
    status: String,
    size: String,
}

#[derive(Tabled)]
struct SystemMetric {
    metric: String,
    value: String,
    status: String,
}

#[tokio::main]
async fn main() -> Result<()> {
    let cli = Cli::parse();

    // Initialize logging
    if cli.verbose {
        tracing_subscriber::fmt()
            .with_env_filter("debug")
            .init();
    } else {
        tracing_subscriber::fmt()
            .with_env_filter("info")
            .init();
    }

    // Print banner
    print_banner();

    // Load configuration
    let config = if cli.config.exists() {
        Config::from_file(&cli.config)?
    } else {
        println!("{}", "⚠️  Using default configuration (config file not found)".yellow());
        Config::default()
    };

    // Initialize engine
    let spinner = ProgressBar::new_spinner();
    spinner.set_style(ProgressStyle::default_spinner()
        .template("{spinner:.green} {msg}")
        .unwrap());
    spinner.set_message("Initializing Chatterbox Engine...");
    spinner.enable_steady_tick(std::time::Duration::from_millis(100));

    let model_manager = Arc::new(ModelManager::new(config.clone()).await?);
    let watermark_engine = Arc::new(WatermarkEngine::new(&config)?);
    let telemetry = Arc::new(TelemetryCollector::new(&config)?);

    let engine = ChatterboxEngine::new(
        config,
        model_manager.clone(),
        watermark_engine,
        telemetry.clone(),
    ).await?;

    spinner.finish_with_message("✅ Engine initialized successfully");

    // Execute command
    match cli.command {
        Commands::Tts { 
            text, voice, output, rate, pitch, volume, emotion, watermark, interactive 
        } => {
            if interactive {
                run_interactive_tts(&engine, &cli.format).await?;
            } else {
                let text = if let Some(t) = text {
                    t
                } else {
                    Input::with_theme(&ColorfulTheme::default())
                        .with_prompt("Enter text to synthesize")
                        .interact_text()?
                };
                
                run_tts(&engine, &text, &voice, &output, rate, pitch, volume, emotion, watermark, &cli.format).await?;
            }
        },
        Commands::Stt { input, language, output, punctuation, confidence, timestamps } => {
            run_stt(&engine, &input, language, output, punctuation, confidence, timestamps, &cli.format).await?;
        },
        Commands::Models { filter, detailed } => {
            list_models(&model_manager, &filter, detailed, &cli.format).await?;
        },
        Commands::Status { detailed, watch, interval } => {
            if watch {
                run_status_watch(&engine, &telemetry, detailed, interval, &cli.format).await?;
            } else {
                show_status(&engine, &telemetry, detailed, &cli.format).await?;
            }
        },
        Commands::Batch { input, output, workers } => {
            run_batch_processing(&engine, &input, &output, workers, &cli.format).await?;
        },
        Commands::Shell => {
            run_interactive_shell(&engine, &cli.format).await?;
        },
        Commands::Benchmark { test_type, iterations, concurrent } => {
            run_benchmark(&engine, &test_type, iterations, concurrent, &cli.format).await?;
        },
    }

    Ok(())
}

fn print_banner() {
    let term = Term::stdout();
    let _ = term.clear_screen();
    
    println!("{}", r#"
    ╔═══════════════════════════════════════════════════════════════╗
    ║                                                               ║
    ║   ██████╗██╗  ██╗ █████╗ ████████╗████████╗███████╗██████╗    ║
    ║  ██╔════╝██║  ██║██╔══██╗╚══██╔══╝╚══██╔══╝██╔════╝██╔══██╗   ║
    ║  ██║     ███████║███████║   ██║      ██║   █████╗  ██████╔╝   ║
    ║  ██║     ██╔══██║██╔══██║   ██║      ██║   ██╔══╝  ██╔══██╗   ║
    ║  ╚██████╗██║  ██║██║  ██║   ██║      ██║   ███████╗██║  ██║   ║
    ║   ╚═════╝╚═╝  ╚═╝╚═╝  ╚═╝   ╚═╝      ╚═╝   ╚══════╝╚═╝  ╚═╝   ║
    ║                                                               ║
    ║                    ENTERPRISE CLI v1.0.0                     ║
    ║              Professional TTS/STT Command Line               ║
    ║                                                               ║
    ╚═══════════════════════════════════════════════════════════════╝
    "#.bright_cyan());
    
    println!("{}", "🎧 Welcome to Chatterbox Enterprise CLI".bright_green().bold());
    println!("{}", "   Enterprise-grade Text-to-Speech and Speech-to-Text".bright_white());
    println!();
}

async fn run_tts(
    engine: &ChatterboxEngine,
    text: &str,
    voice: &str,
    output: &PathBuf,
    rate: f32,
    pitch: f32,
    volume: f32,
    emotion: Option<String>,
    watermark: bool,
    format: &str,
) -> Result<()> {
    let options = SynthesisOptions {
        voice_model: voice.to_string(),
        speaking_rate: rate,
        pitch,
        volume,
        emotion,
        apply_watermark: watermark,
    };

    println!("{}", "🎤 Starting TTS synthesis...".bright_blue());

    let progress = ProgressBar::new_spinner();
    progress.set_style(ProgressStyle::default_spinner()
        .template("{spinner:.green} {msg}")
        .unwrap());
    progress.set_message("Synthesizing speech...");
    progress.enable_steady_tick(std::time::Duration::from_millis(100));

    let start_time = Instant::now();
    let result = engine.synthesize_speech(text, &options).await?;
    let duration = start_time.elapsed();

    progress.finish_with_message("✅ Synthesis completed");

    // Save audio to file (simplified for demo)
    let audio_samples = result.audio.samples();
    println!("📁 Saving audio to: {}", output.display());

    // In a real implementation, you would save the actual WAV file
    fs::write(output, format!("Audio samples: {} samples", audio_samples.len()))?;

    match format {
        "json" => {
            println!("{}", serde_json::json!({
                "status": "success",
                "duration_ms": duration.as_millis(),
                "audio_samples": audio_samples.len(),
                "sample_rate": format!("{:?}", result.audio.sample_rate()),
                "output_file": output.display().to_string(),
                "watermark_applied": result.metadata.as_ref().map(|m| m.watermark_applied).unwrap_or(false)
            }));
        },
        "table" => {
            let metrics = vec![
                SystemMetric { metric: "Duration".to_string(), value: format!("{:.2}ms", duration.as_millis()), status: "✅".to_string() },
                SystemMetric { metric: "Audio Samples".to_string(), value: audio_samples.len().to_string(), status: "✅".to_string() },
                SystemMetric { metric: "Sample Rate".to_string(), value: format!("{:?}", result.audio.sample_rate()), status: "✅".to_string() },
                SystemMetric { metric: "Output File".to_string(), value: output.display().to_string(), status: "✅".to_string() },
            ];
            println!("{}", Table::new(metrics));
        },
        _ => {
            println!("✅ TTS synthesis completed in {:.2}ms", duration.as_millis());
            println!("📊 Generated {} audio samples", audio_samples.len());
            println!("📁 Saved to: {}", output.display());
        }
    }

    Ok(())
}

async fn run_stt(
    engine: &ChatterboxEngine,
    input: &PathBuf,
    language: Option<String>,
    output: Option<PathBuf>,
    punctuation: bool,
    confidence: f32,
    timestamps: bool,
    format: &str,
) -> Result<()> {
    println!("{}", "🎧 Starting STT recognition...".bright_blue());

    // Load audio file (simplified for demo)
    if !input.exists() {
        return Err(chatterbox_enterprise::ChatterboxError::validation(
            "Input audio file not found".to_string(),
            "input_file",
        ));
    }

    // Create dummy audio data for demo
    let samples = vec![0.1f32; 22050]; // 1 second of test audio
    let audio = AudioData::new(samples, SampleRate::Hz22050);

    let options = RecognitionOptions {
        language,
        punctuation,
        confidence_threshold: confidence,
        enable_word_timestamps: timestamps,
    };

    let progress = ProgressBar::new_spinner();
    progress.set_style(ProgressStyle::default_spinner()
        .template("{spinner:.green} {msg}")
        .unwrap());
    progress.set_message("Recognizing speech...");
    progress.enable_steady_tick(std::time::Duration::from_millis(100));

    let start_time = Instant::now();
    let result = engine.recognize_speech(&audio, &options).await?;
    let duration = start_time.elapsed();

    progress.finish_with_message("✅ Recognition completed");

    // Save text output if specified
    if let Some(output_path) = output {
        fs::write(&output_path, &result.text)?;
        println!("📁 Text saved to: {}", output_path.display());
    }

    match format {
        "json" => {
            println!("{}", serde_json::json!({
                "status": "success",
                "duration_ms": duration.as_millis(),
                "text": result.text,
                "confidence": result.confidence,
                "language": result.language,
                "word_count": result.text.split_whitespace().count()
            }));
        },
        "table" => {
            let metrics = vec![
                SystemMetric { metric: "Duration".to_string(), value: format!("{:.2}ms", duration.as_millis()), status: "✅".to_string() },
                SystemMetric { metric: "Confidence".to_string(), value: format!("{:.2}", result.confidence), status: "✅".to_string() },
                SystemMetric { metric: "Language".to_string(), value: result.language.unwrap_or("auto".to_string()), status: "✅".to_string() },
                SystemMetric { metric: "Word Count".to_string(), value: result.text.split_whitespace().count().to_string(), status: "✅".to_string() },
            ];
            println!("{}", Table::new(metrics));
            println!("\n📝 Recognized Text:");
            println!("{}", result.text.bright_white().bold());
        },
        _ => {
            println!("✅ STT recognition completed in {:.2}ms", duration.as_millis());
            println!("📊 Confidence: {:.2}", result.confidence);
            println!("📝 Text: {}", result.text.bright_white().bold());
        }
    }

    Ok(())
}
