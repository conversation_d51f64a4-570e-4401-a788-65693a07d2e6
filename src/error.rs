//! Enterprise-grade error handling for Chatterbox TTS/STT system
//!
//! This module provides comprehensive error types with:
//! - Structured error information for debugging
//! - Security-aware error messages (no sensitive data leakage)
//! - Integration with enterprise monitoring systems
//! - Formal verification support

use std::fmt;
use thiserror::Error;
use tracing::{error, warn};

/// Result type alias for Chatterbox operations
pub type Result<T> = std::result::Result<T, ChatterboxError>;

/// Comprehensive error types for enterprise TTS/STT operations
#[derive(Error, Debug, Clone)]
pub enum ChatterboxError {
    /// Audio processing errors
    #[error("Audio processing failed: {message}")]
    AudioProcessing {
        /// Error message
        message: String,
        /// Error code for monitoring systems
        code: AudioErrorCode,
    },

    /// Neural model inference errors
    #[error("Model inference failed: {message}")]
    ModelInference {
        /// Error message
        message: String,
        /// Model type that failed
        model_type: String,
        /// Error code
        code: ModelErrorCode,
    },

    /// Configuration errors
    #[error("Configuration error: {message}")]
    Configuration {
        /// Error message
        message: String,
        /// Configuration section that failed
        section: String,
    },

    /// Security and watermarking errors
    #[error("Security operation failed: {message}")]
    Security {
        /// Error message (sanitized for logs)
        message: String,
        /// Security error type
        error_type: SecurityErrorType,
    },

    /// Network and API errors
    #[error("Network operation failed: {message}")]
    Network {
        /// Error message
        message: String,
        /// HTTP status code if applicable
        status_code: Option<u16>,
    },

    /// File system and I/O errors
    #[error("I/O operation failed: {message}")]
    Io {
        /// Error message
        message: String,
        /// File path if applicable (sanitized)
        path: Option<String>,
    },

    /// Validation errors for user input
    #[error("Validation failed: {message}")]
    Validation {
        /// Error message
        message: String,
        /// Field that failed validation
        field: String,
    },

    /// Resource exhaustion errors
    #[error("Resource limit exceeded: {message}")]
    ResourceExhausted {
        /// Error message
        message: String,
        /// Resource type
        resource_type: ResourceType,
    },

    /// Formal verification errors
    #[cfg(feature = "verification")]
    #[error("Verification failed: {message}")]
    Verification {
        /// Error message
        message: String,
        /// Verification type
        verification_type: VerificationType,
    },

    /// WASM-specific errors
    #[cfg(feature = "wasm")]
    #[error("WASM operation failed: {message}")]
    Wasm {
        /// Error message
        message: String,
        /// WASM error code
        code: WasmErrorCode,
    },

    /// Internal system errors (should not expose sensitive information)
    #[error("Internal system error occurred")]
    Internal {
        /// Internal error ID for tracking
        error_id: uuid::Uuid,
    },
}

/// Audio processing error codes
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum AudioErrorCode {
    /// Invalid audio format
    InvalidFormat,
    /// Unsupported sample rate
    UnsupportedSampleRate,
    /// Audio buffer overflow
    BufferOverflow,
    /// Audio processing timeout
    ProcessingTimeout,
    /// Codec error
    CodecError,
}

/// Model inference error codes
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ModelErrorCode {
    /// Model not loaded
    ModelNotLoaded,
    /// Invalid input dimensions
    InvalidInputDimensions,
    /// Model inference timeout
    InferenceTimeout,
    /// GPU memory exhausted
    GpuMemoryExhausted,
    /// Model corruption detected
    ModelCorruption,
}

/// Security error types
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum SecurityErrorType {
    /// Watermarking failed
    WatermarkingFailed,
    /// Cryptographic operation failed
    CryptographicFailure,
    /// Authentication failed
    AuthenticationFailed,
    /// Authorization denied
    AuthorizationDenied,
    /// Rate limit exceeded
    RateLimitExceeded,
}

/// Resource types for resource exhaustion errors
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum ResourceType {
    /// Memory exhausted
    Memory,
    /// CPU time exceeded
    CpuTime,
    /// Disk space exhausted
    DiskSpace,
    /// Network bandwidth exceeded
    NetworkBandwidth,
    /// Concurrent connections limit
    Connections,
}

/// Formal verification error types
#[cfg(feature = "verification")]
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum VerificationType {
    /// Precondition violation
    PreconditionViolation,
    /// Postcondition violation
    PostconditionViolation,
    /// Invariant violation
    InvariantViolation,
    /// Memory safety violation
    MemorySafetyViolation,
}

/// WASM-specific error codes
#[cfg(feature = "wasm")]
#[derive(Debug, Clone, Copy, PartialEq, Eq)]
pub enum WasmErrorCode {
    /// JavaScript interop failed
    JsInteropFailed,
    /// Memory allocation failed
    MemoryAllocationFailed,
    /// Invalid WASM state
    InvalidState,
    /// Browser API unavailable
    BrowserApiUnavailable,
}

impl ChatterboxError {
    /// Create an audio processing error
    pub fn audio_processing(message: impl Into<String>, code: AudioErrorCode) -> Self {
        let error = Self::AudioProcessing {
            message: message.into(),
            code,
        };
        error.log_error();
        error
    }

    /// Create a model inference error
    pub fn model_inference(
        message: impl Into<String>,
        model_type: impl Into<String>,
        code: ModelErrorCode,
    ) -> Self {
        let error = Self::ModelInference {
            message: message.into(),
            model_type: model_type.into(),
            code,
        };
        error.log_error();
        error
    }

    /// Create a configuration error
    pub fn configuration(message: impl Into<String>, section: impl Into<String>) -> Self {
        let error = Self::Configuration {
            message: message.into(),
            section: section.into(),
        };
        error.log_error();
        error
    }

    /// Create a security error (with sanitized logging)
    pub fn security(message: impl Into<String>, error_type: SecurityErrorType) -> Self {
        let error = Self::Security {
            message: message.into(),
            error_type,
        };
        // Security errors get special logging treatment
        warn!(
            error_type = ?error_type,
            "Security operation failed (details omitted for security)"
        );
        error
    }

    /// Create an internal error with tracking ID
    pub fn internal() -> Self {
        let error_id = uuid::Uuid::new_v4();
        error!(error_id = %error_id, "Internal system error occurred");
        Self::Internal { error_id }
    }

    /// Log the error with appropriate level and context
    fn log_error(&self) {
        match self {
            Self::AudioProcessing { code, .. } => {
                error!(error_code = ?code, error = %self, "Audio processing error");
            }
            Self::ModelInference { code, model_type, .. } => {
                error!(
                    error_code = ?code,
                    model_type = %model_type,
                    error = %self,
                    "Model inference error"
                );
            }
            Self::Configuration { section, .. } => {
                error!(section = %section, error = %self, "Configuration error");
            }
            Self::ResourceExhausted { resource_type, .. } => {
                warn!(resource_type = ?resource_type, error = %self, "Resource exhausted");
            }
            _ => {
                error!(error = %self, "Chatterbox error occurred");
            }
        }
    }

    /// Get error code for monitoring systems
    pub fn error_code(&self) -> String {
        match self {
            Self::AudioProcessing { code, .. } => format!("AUDIO_{:?}", code),
            Self::ModelInference { code, .. } => format!("MODEL_{:?}", code),
            Self::Configuration { .. } => "CONFIG_ERROR".to_string(),
            Self::Security { error_type, .. } => format!("SECURITY_{:?}", error_type),
            Self::Network { .. } => "NETWORK_ERROR".to_string(),
            Self::Io { .. } => "IO_ERROR".to_string(),
            Self::Validation { .. } => "VALIDATION_ERROR".to_string(),
            Self::ResourceExhausted { resource_type, .. } => {
                format!("RESOURCE_{:?}", resource_type)
            }
            #[cfg(feature = "verification")]
            Self::Verification { verification_type, .. } => {
                format!("VERIFICATION_{:?}", verification_type)
            }
            #[cfg(feature = "wasm")]
            Self::Wasm { code, .. } => format!("WASM_{:?}", code),
            Self::Internal { .. } => "INTERNAL_ERROR".to_string(),
        }
    }

    /// Check if error is retryable
    pub fn is_retryable(&self) -> bool {
        matches!(
            self,
            Self::Network { .. }
                | Self::ResourceExhausted {
                    resource_type: ResourceType::Memory | ResourceType::CpuTime,
                    ..
                }
                | Self::ModelInference {
                    code: ModelErrorCode::InferenceTimeout | ModelErrorCode::GpuMemoryExhausted,
                    ..
                }
        )
    }
}

// Implement From traits for common error types
impl From<std::io::Error> for ChatterboxError {
    fn from(err: std::io::Error) -> Self {
        Self::Io {
            message: err.to_string(),
            path: None,
        }
    }
}

impl From<serde_json::Error> for ChatterboxError {
    fn from(err: serde_json::Error) -> Self {
        Self::Configuration {
            message: format!("JSON parsing failed: {}", err),
            section: "json".to_string(),
        }
    }
}

impl From<config::ConfigError> for ChatterboxError {
    fn from(err: config::ConfigError) -> Self {
        Self::Configuration {
            message: err.to_string(),
            section: "config".to_string(),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_creation() {
        let error = ChatterboxError::audio_processing("Test error", AudioErrorCode::InvalidFormat);
        assert!(matches!(error, ChatterboxError::AudioProcessing { .. }));
        assert_eq!(error.error_code(), "AUDIO_InvalidFormat");
    }

    #[test]
    fn test_retryable_errors() {
        let retryable = ChatterboxError::Network {
            message: "Connection failed".to_string(),
            status_code: Some(503),
        };
        assert!(retryable.is_retryable());

        let non_retryable = ChatterboxError::Validation {
            message: "Invalid input".to_string(),
            field: "text".to_string(),
        };
        assert!(!non_retryable.is_retryable());
    }
}
