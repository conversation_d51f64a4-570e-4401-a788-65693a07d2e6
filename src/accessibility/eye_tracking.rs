//! Eye Tracking Interface for Accessibility
//! 
//! Implements eye tracking navigation and interaction for users with motor disabilities
//! following WCAG 3.0 guidelines for alternative input methods.

use serde::{Deserialize, Serialize};
use std::time::{Duration, SystemTime};
use tracing::{info, debug, warn};

use crate::Result;

/// Eye Tracking Configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct EyeTrackingConfig {
    /// Enable eye tracking
    pub enabled: bool,
    /// Dwell time for activation (milliseconds)
    pub dwell_time: u64,
    /// Smoothing factor for gaze data (0.0-1.0)
    pub smoothing_factor: f64,
    /// Minimum fixation duration (milliseconds)
    pub min_fixation_duration: u64,
    /// Maximum saccade velocity (pixels/second)
    pub max_saccade_velocity: f64,
    /// Calibration accuracy threshold
    pub calibration_threshold: f64,
    /// Enable gaze cursor
    pub show_gaze_cursor: bool,
    /// Cursor size in pixels
    pub cursor_size: u32,
    /// Enable dwell indicator
    pub show_dwell_indicator: bool,
    /// Interaction zones configuration
    pub interaction_zones: InteractionZonesConfig,
}

/// Interaction Zones Configuration
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct InteractionZonesConfig {
    /// Enable magnetic zones that attract gaze
    pub magnetic_zones: bool,
    /// Zone expansion factor for easier targeting
    pub zone_expansion: f64,
    /// Minimum zone size in pixels
    pub min_zone_size: u32,
    /// Enable zone highlighting
    pub highlight_zones: bool,
}

/// Gaze Point Data
#[derive(Debug, Clone, Copy)]
pub struct GazePoint {
    pub x: f64,
    pub y: f64,
    pub timestamp: SystemTime,
    pub confidence: f64,
    pub pupil_diameter: Option<f64>,
}

/// Eye Tracking Event Types
#[derive(Debug, Clone)]
pub enum EyeTrackingEvent {
    /// Gaze entered an element
    GazeEnter { element_id: String, point: GazePoint },
    /// Gaze left an element
    GazeLeave { element_id: String, point: GazePoint },
    /// Fixation detected on an element
    Fixation { element_id: String, duration: Duration, point: GazePoint },
    /// Dwell activation (long fixation)
    DwellActivation { element_id: String, duration: Duration, point: GazePoint },
    /// Saccade (rapid eye movement) detected
    Saccade { from: GazePoint, to: GazePoint, velocity: f64 },
    /// Blink detected
    Blink { duration: Duration },
    /// Calibration point
    CalibrationPoint { target: GazePoint, actual: GazePoint, error: f64 },
}

/// Fixation State
#[derive(Debug, Clone)]
pub struct FixationState {
    pub element_id: Option<String>,
    pub start_time: SystemTime,
    pub center_point: GazePoint,
    pub duration: Duration,
    pub is_dwelling: bool,
}

/// Eye Tracker Interface
#[derive(Debug)]
pub struct EyeTracker {
    config: EyeTrackingConfig,
    is_enabled: bool,
    is_calibrated: bool,
    current_gaze: Option<GazePoint>,
    fixation_state: Option<FixationState>,
    gaze_history: Vec<GazePoint>,
    interaction_zones: std::collections::HashMap<String, InteractionZone>,
    calibration_points: Vec<CalibrationPoint>,
    smoothed_gaze: Option<GazePoint>,
}

/// Interaction Zone for eye tracking
#[derive(Debug, Clone)]
pub struct InteractionZone {
    pub id: String,
    pub bounds: Rectangle,
    pub expanded_bounds: Rectangle,
    pub is_magnetic: bool,
    pub activation_threshold: Duration,
    pub is_active: bool,
}

/// Rectangle for zone bounds
#[derive(Debug, Clone, Copy)]
pub struct Rectangle {
    pub x: f64,
    pub y: f64,
    pub width: f64,
    pub height: f64,
}

/// Calibration Point
#[derive(Debug, Clone)]
pub struct CalibrationPoint {
    pub target: GazePoint,
    pub actual: GazePoint,
    pub error: f64,
}

impl Default for EyeTrackingConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            dwell_time: 1000, // 1 second
            smoothing_factor: 0.3,
            min_fixation_duration: 100, // 100ms
            max_saccade_velocity: 1000.0, // pixels/second
            calibration_threshold: 50.0, // pixels
            show_gaze_cursor: true,
            cursor_size: 20,
            show_dwell_indicator: true,
            interaction_zones: InteractionZonesConfig {
                magnetic_zones: true,
                zone_expansion: 1.2,
                min_zone_size: 40,
                highlight_zones: true,
            },
        }
    }
}

impl EyeTracker {
    /// Create a new eye tracker
    pub fn new() -> Result<Self> {
        info!("👁️ Initializing Eye Tracker");
        
        Ok(Self {
            config: EyeTrackingConfig::default(),
            is_enabled: false,
            is_calibrated: false,
            current_gaze: None,
            fixation_state: None,
            gaze_history: Vec::new(),
            interaction_zones: std::collections::HashMap::new(),
            calibration_points: Vec::new(),
            smoothed_gaze: None,
        })
    }

    /// Enable eye tracking
    pub async fn enable(&mut self) -> Result<()> {
        info!("👀 Enabling eye tracking");
        
        if !self.is_calibrated {
            warn!("⚠️ Eye tracker not calibrated - starting calibration");
            self.start_calibration().await?;
        }
        
        self.is_enabled = true;
        Ok(())
    }

    /// Disable eye tracking
    pub async fn disable(&mut self) -> Result<()> {
        info!("🚫 Disabling eye tracking");
        self.is_enabled = false;
        self.current_gaze = None;
        self.fixation_state = None;
        Ok(())
    }

    /// Start calibration process
    pub async fn start_calibration(&mut self) -> Result<()> {
        info!("🎯 Starting eye tracking calibration");
        
        // Clear previous calibration data
        self.calibration_points.clear();
        self.is_calibrated = false;
        
        // TODO: Implement actual calibration routine
        // This would typically show calibration targets and collect gaze data
        
        Ok(())
    }

    /// Add calibration point
    pub fn add_calibration_point(&mut self, target: GazePoint, actual: GazePoint) -> Result<()> {
        let error = ((target.x - actual.x).powi(2) + (target.y - actual.y).powi(2)).sqrt();
        
        let calibration_point = CalibrationPoint {
            target,
            actual,
            error,
        };
        
        debug!("🎯 Calibration point added: error = {:.2}px", error);
        self.calibration_points.push(calibration_point);
        
        // Check if calibration is complete and accurate
        if self.calibration_points.len() >= 9 { // 3x3 grid
            let average_error: f64 = self.calibration_points.iter()
                .map(|p| p.error)
                .sum::<f64>() / self.calibration_points.len() as f64;
            
            if average_error <= self.config.calibration_threshold {
                self.is_calibrated = true;
                info!("✅ Eye tracking calibration successful (avg error: {:.2}px)", average_error);
            } else {
                warn!("❌ Calibration accuracy insufficient (avg error: {:.2}px)", average_error);
            }
        }
        
        Ok(())
    }

    /// Process new gaze data
    pub async fn process_gaze_data(&mut self, raw_gaze: GazePoint) -> Result<Vec<EyeTrackingEvent>> {
        if !self.is_enabled || !self.is_calibrated {
            return Ok(Vec::new());
        }

        let mut events = Vec::new();
        
        // Apply smoothing
        let smoothed_gaze = self.apply_smoothing(raw_gaze);
        self.smoothed_gaze = Some(smoothed_gaze);
        self.current_gaze = Some(smoothed_gaze);
        
        // Add to history
        self.gaze_history.push(smoothed_gaze);
        if self.gaze_history.len() > 1000 {
            self.gaze_history.remove(0);
        }
        
        // Detect saccades
        if let Some(saccade_event) = self.detect_saccade(smoothed_gaze) {
            events.push(saccade_event);
        }
        
        // Check interaction zones
        let zone_events = self.check_interaction_zones(smoothed_gaze).await?;
        events.extend(zone_events);
        
        // Update fixation state
        let fixation_events = self.update_fixation_state(smoothed_gaze).await?;
        events.extend(fixation_events);
        
        Ok(events)
    }

    /// Apply smoothing to gaze data
    fn apply_smoothing(&self, raw_gaze: GazePoint) -> GazePoint {
        if let Some(previous) = self.smoothed_gaze {
            let factor = self.config.smoothing_factor;
            GazePoint {
                x: previous.x * (1.0 - factor) + raw_gaze.x * factor,
                y: previous.y * (1.0 - factor) + raw_gaze.y * factor,
                timestamp: raw_gaze.timestamp,
                confidence: raw_gaze.confidence,
                pupil_diameter: raw_gaze.pupil_diameter,
            }
        } else {
            raw_gaze
        }
    }

    /// Detect saccade (rapid eye movement)
    fn detect_saccade(&self, current_gaze: GazePoint) -> Option<EyeTrackingEvent> {
        if self.gaze_history.len() < 2 {
            return None;
        }
        
        let previous = self.gaze_history[self.gaze_history.len() - 2];
        let distance = ((current_gaze.x - previous.x).powi(2) + (current_gaze.y - previous.y).powi(2)).sqrt();
        let time_diff = current_gaze.timestamp.duration_since(previous.timestamp).ok()?.as_secs_f64();
        
        if time_diff > 0.0 {
            let velocity = distance / time_diff;
            
            if velocity > self.config.max_saccade_velocity {
                return Some(EyeTrackingEvent::Saccade {
                    from: previous,
                    to: current_gaze,
                    velocity,
                });
            }
        }
        
        None
    }

    /// Check interaction zones for gaze events
    async fn check_interaction_zones(&mut self, gaze: GazePoint) -> Result<Vec<EyeTrackingEvent>> {
        let mut events = Vec::new();
        
        for zone in self.interaction_zones.values_mut() {
            let in_zone = zone.expanded_bounds.contains_point(gaze.x, gaze.y);
            
            if in_zone && !zone.is_active {
                // Gaze entered zone
                zone.is_active = true;
                events.push(EyeTrackingEvent::GazeEnter {
                    element_id: zone.id.clone(),
                    point: gaze,
                });
            } else if !in_zone && zone.is_active {
                // Gaze left zone
                zone.is_active = false;
                events.push(EyeTrackingEvent::GazeLeave {
                    element_id: zone.id.clone(),
                    point: gaze,
                });
            }
        }
        
        Ok(events)
    }

    /// Update fixation state and detect fixations/dwells
    async fn update_fixation_state(&mut self, gaze: GazePoint) -> Result<Vec<EyeTrackingEvent>> {
        let mut events = Vec::new();
        
        // Find which zone the gaze is in
        let current_zone = self.interaction_zones.values()
            .find(|zone| zone.bounds.contains_point(gaze.x, gaze.y))
            .map(|zone| zone.id.clone());
        
        match &mut self.fixation_state {
            Some(fixation) => {
                // Check if still in same zone
                if fixation.element_id == current_zone {
                    // Update fixation duration
                    fixation.duration = gaze.timestamp.duration_since(fixation.start_time).unwrap_or_default();
                    
                    // Check for dwell activation
                    if !fixation.is_dwelling && fixation.duration.as_millis() >= self.config.dwell_time as u128 {
                        fixation.is_dwelling = true;
                        
                        if let Some(element_id) = &fixation.element_id {
                            events.push(EyeTrackingEvent::DwellActivation {
                                element_id: element_id.clone(),
                                duration: fixation.duration,
                                point: gaze,
                            });
                        }
                    }
                } else {
                    // Fixation ended, start new one if in a zone
                    if fixation.duration.as_millis() >= self.config.min_fixation_duration as u128 {
                        if let Some(element_id) = &fixation.element_id {
                            events.push(EyeTrackingEvent::Fixation {
                                element_id: element_id.clone(),
                                duration: fixation.duration,
                                point: fixation.center_point,
                            });
                        }
                    }
                    
                    // Start new fixation
                    self.fixation_state = current_zone.map(|zone_id| FixationState {
                        element_id: Some(zone_id),
                        start_time: gaze.timestamp,
                        center_point: gaze,
                        duration: Duration::from_millis(0),
                        is_dwelling: false,
                    });
                }
            }
            None => {
                // Start new fixation if in a zone
                self.fixation_state = current_zone.map(|zone_id| FixationState {
                    element_id: Some(zone_id),
                    start_time: gaze.timestamp,
                    center_point: gaze,
                    duration: Duration::from_millis(0),
                    is_dwelling: false,
                });
            }
        }
        
        Ok(events)
    }

    /// Register an interaction zone
    pub fn register_interaction_zone(&mut self, element_id: String, bounds: Rectangle) {
        let expanded_bounds = Rectangle {
            x: bounds.x - (bounds.width * (self.config.interaction_zones.zone_expansion - 1.0) / 2.0),
            y: bounds.y - (bounds.height * (self.config.interaction_zones.zone_expansion - 1.0) / 2.0),
            width: bounds.width * self.config.interaction_zones.zone_expansion,
            height: bounds.height * self.config.interaction_zones.zone_expansion,
        };
        
        let zone = InteractionZone {
            id: element_id.clone(),
            bounds,
            expanded_bounds,
            is_magnetic: self.config.interaction_zones.magnetic_zones,
            activation_threshold: Duration::from_millis(self.config.dwell_time),
            is_active: false,
        };
        
        debug!("👁️ Registered interaction zone: {}", element_id);
        self.interaction_zones.insert(element_id, zone);
    }

    /// Get current gaze position
    pub fn get_current_gaze(&self) -> Option<GazePoint> {
        self.current_gaze
    }

    /// Check if eye tracker is calibrated
    pub fn is_calibrated(&self) -> bool {
        self.is_calibrated
    }

    /// Update configuration
    pub fn update_config(&mut self, config: EyeTrackingConfig) {
        info!("⚙️ Updating eye tracking configuration");
        self.config = config;
    }
}

impl Rectangle {
    /// Check if a point is within the rectangle
    pub fn contains_point(&self, x: f64, y: f64) -> bool {
        x >= self.x && x <= self.x + self.width && y >= self.y && y <= self.y + self.height
    }
}
