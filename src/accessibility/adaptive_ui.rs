//! Adaptive UI Management for Accessibility
//! 
//! Implements dynamic UI adjustments based on user preferences and capabilities
//! following WCAG 3.0 guidelines for personalized accessibility.

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{info, debug, warn};

use crate::Result;

/// UI Adaptation Types
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum AdaptationType {
    FontSize,
    Contrast,
    ColorScheme,
    MotionReduction,
    FocusIndicator,
    ButtonSize,
    Spacing,
    Layout,
    TextSpacing,
    LineHeight,
}

/// Color Scheme Options
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ColorScheme {
    Default,
    HighContrast,
    DarkMode,
    LightMode,
    Monochrome,
    Custom(String),
}

/// Motion Preference
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum MotionPreference {
    NoPreference,
    Reduce,
    Remove,
}

/// Layout Preference
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum LayoutPreference {
    Default,
    Simplified,
    Compact,
    Spacious,
    SingleColumn,
}

/// User Preferences for adaptive UI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserPreferences {
    pub font_size_multiplier: f64,
    pub line_height_multiplier: f64,
    pub letter_spacing_multiplier: f64,
    pub word_spacing_multiplier: f64,
    pub color_scheme: ColorScheme,
    pub motion_preference: MotionPreference,
    pub layout_preference: LayoutPreference,
    pub button_size_multiplier: f64,
    pub spacing_multiplier: f64,
    pub focus_indicator_size: f64,
    pub reduce_transparency: bool,
    pub high_contrast_mode: bool,
    pub custom_css: Option<String>,
}

/// Adaptive UI Rule
#[derive(Debug, Clone)]
pub struct AdaptationRule {
    pub id: String,
    pub adaptation_type: AdaptationType,
    pub condition: AdaptationCondition,
    pub css_properties: HashMap<String, String>,
    pub priority: u32,
}

/// Condition for applying adaptations
#[derive(Debug, Clone)]
pub enum AdaptationCondition {
    FontSizeAbove(f64),
    FontSizeBelow(f64),
    ContrastRatioBelow(f64),
    MotionReduced,
    HighContrastEnabled,
    ScreenSizeBelow(u32),
    ScreenSizeAbove(u32),
    Always,
    Custom(String),
}

/// Adaptive UI Manager
#[derive(Debug)]
pub struct AdaptiveUIManager {
    enabled: bool,
    user_preferences: UserPreferences,
    adaptation_rules: Vec<AdaptationRule>,
    applied_adaptations: HashMap<String, String>, // element_id -> css
    css_cache: HashMap<String, String>, // cache for generated CSS
}

impl Default for UserPreferences {
    fn default() -> Self {
        Self {
            font_size_multiplier: 1.0,
            line_height_multiplier: 1.0,
            letter_spacing_multiplier: 1.0,
            word_spacing_multiplier: 1.0,
            color_scheme: ColorScheme::Default,
            motion_preference: MotionPreference::NoPreference,
            layout_preference: LayoutPreference::Default,
            button_size_multiplier: 1.0,
            spacing_multiplier: 1.0,
            focus_indicator_size: 1.0,
            reduce_transparency: false,
            high_contrast_mode: false,
            custom_css: None,
        }
    }
}

impl AdaptiveUIManager {
    /// Create a new adaptive UI manager
    pub fn new(enabled: bool) -> Result<Self> {
        info!("🎨 Initializing Adaptive UI Manager (enabled: {})", enabled);
        
        let mut manager = Self {
            enabled,
            user_preferences: UserPreferences::default(),
            adaptation_rules: Vec::new(),
            applied_adaptations: HashMap::new(),
            css_cache: HashMap::new(),
        };

        manager.initialize_default_rules();
        Ok(manager)
    }

    /// Apply user preferences to the UI
    pub async fn apply_preferences(&mut self, preferences: &HashMap<String, serde_json::Value>) -> Result<()> {
        if !self.enabled {
            return Ok(());
        }

        info!("🎯 Applying user preferences for adaptive UI");
        
        // Update preferences from the provided map
        self.update_preferences_from_map(preferences)?;
        
        // Generate and apply CSS adaptations
        self.generate_adaptive_css().await?;
        
        info!("✅ Adaptive UI preferences applied successfully");
        Ok(())
    }

    /// Update preferences from a key-value map
    fn update_preferences_from_map(&mut self, preferences: &HashMap<String, serde_json::Value>) -> Result<()> {
        for (key, value) in preferences {
            match key.as_str() {
                "font_size_multiplier" => {
                    if let Some(multiplier) = value.as_f64() {
                        self.user_preferences.font_size_multiplier = multiplier.clamp(0.5, 3.0);
                    }
                }
                "line_height_multiplier" => {
                    if let Some(multiplier) = value.as_f64() {
                        self.user_preferences.line_height_multiplier = multiplier.clamp(0.8, 2.0);
                    }
                }
                "color_scheme" => {
                    if let Some(scheme_str) = value.as_str() {
                        self.user_preferences.color_scheme = match scheme_str {
                            "high_contrast" => ColorScheme::HighContrast,
                            "dark" => ColorScheme::DarkMode,
                            "light" => ColorScheme::LightMode,
                            "monochrome" => ColorScheme::Monochrome,
                            _ => ColorScheme::Default,
                        };
                    }
                }
                "motion_preference" => {
                    if let Some(motion_str) = value.as_str() {
                        self.user_preferences.motion_preference = match motion_str {
                            "reduce" => MotionPreference::Reduce,
                            "remove" => MotionPreference::Remove,
                            _ => MotionPreference::NoPreference,
                        };
                    }
                }
                "layout_preference" => {
                    if let Some(layout_str) = value.as_str() {
                        self.user_preferences.layout_preference = match layout_str {
                            "simplified" => LayoutPreference::Simplified,
                            "compact" => LayoutPreference::Compact,
                            "spacious" => LayoutPreference::Spacious,
                            "single_column" => LayoutPreference::SingleColumn,
                            _ => LayoutPreference::Default,
                        };
                    }
                }
                "button_size_multiplier" => {
                    if let Some(multiplier) = value.as_f64() {
                        self.user_preferences.button_size_multiplier = multiplier.clamp(0.8, 2.0);
                    }
                }
                "high_contrast_mode" => {
                    if let Some(enabled) = value.as_bool() {
                        self.user_preferences.high_contrast_mode = enabled;
                    }
                }
                "reduce_transparency" => {
                    if let Some(enabled) = value.as_bool() {
                        self.user_preferences.reduce_transparency = enabled;
                    }
                }
                _ => {
                    debug!("Unknown preference key: {}", key);
                }
            }
        }
        
        Ok(())
    }

    /// Generate adaptive CSS based on current preferences
    async fn generate_adaptive_css(&mut self) -> Result<()> {
        let mut css_rules = Vec::new();
        
        // Font size adaptations
        if self.user_preferences.font_size_multiplier != 1.0 {
            let font_size_rule = format!(
                "* {{ font-size: calc(1rem * {}) !important; }}",
                self.user_preferences.font_size_multiplier
            );
            css_rules.push(font_size_rule);
        }
        
        // Line height adaptations
        if self.user_preferences.line_height_multiplier != 1.0 {
            let line_height_rule = format!(
                "* {{ line-height: calc(1.5 * {}) !important; }}",
                self.user_preferences.line_height_multiplier
            );
            css_rules.push(line_height_rule);
        }
        
        // Color scheme adaptations
        match self.user_preferences.color_scheme {
            ColorScheme::HighContrast => {
                css_rules.push(self.generate_high_contrast_css());
            }
            ColorScheme::DarkMode => {
                css_rules.push(self.generate_dark_mode_css());
            }
            ColorScheme::Monochrome => {
                css_rules.push(self.generate_monochrome_css());
            }
            _ => {}
        }
        
        // Motion reduction
        if self.user_preferences.motion_preference != MotionPreference::NoPreference {
            css_rules.push(self.generate_motion_reduction_css());
        }
        
        // Button size adaptations
        if self.user_preferences.button_size_multiplier != 1.0 {
            let button_rule = format!(
                "button, input[type='button'], input[type='submit'] {{ 
                    padding: calc(0.5rem * {}) calc(1rem * {}) !important;
                    min-height: calc(44px * {}) !important;
                }}",
                self.user_preferences.button_size_multiplier,
                self.user_preferences.button_size_multiplier,
                self.user_preferences.button_size_multiplier
            );
            css_rules.push(button_rule);
        }
        
        // Focus indicator enhancements
        if self.user_preferences.focus_indicator_size != 1.0 {
            let focus_rule = format!(
                "*:focus {{ 
                    outline: calc(2px * {}) solid #0066cc !important;
                    outline-offset: calc(2px * {}) !important;
                }}",
                self.user_preferences.focus_indicator_size,
                self.user_preferences.focus_indicator_size
            );
            css_rules.push(focus_rule);
        }
        
        // Transparency reduction
        if self.user_preferences.reduce_transparency {
            css_rules.push("* { opacity: 1 !important; }".to_string());
        }
        
        // Layout adaptations
        match self.user_preferences.layout_preference {
            LayoutPreference::SingleColumn => {
                css_rules.push(self.generate_single_column_css());
            }
            LayoutPreference::Spacious => {
                css_rules.push(self.generate_spacious_layout_css());
            }
            _ => {}
        }
        
        // Combine all CSS rules
        let combined_css = css_rules.join("\n");
        self.css_cache.insert("adaptive_styles".to_string(), combined_css);
        
        Ok(())
    }

    /// Generate high contrast CSS
    fn generate_high_contrast_css(&self) -> String {
        r#"
        * {
            background-color: white !important;
            color: black !important;
            border-color: black !important;
        }
        a, button {
            background-color: yellow !important;
            color: black !important;
            border: 2px solid black !important;
        }
        a:hover, button:hover {
            background-color: black !important;
            color: white !important;
        }
        "#.to_string()
    }

    /// Generate dark mode CSS
    fn generate_dark_mode_css(&self) -> String {
        r#"
        * {
            background-color: #1a1a1a !important;
            color: #ffffff !important;
            border-color: #444444 !important;
        }
        input, textarea, select {
            background-color: #2d2d2d !important;
            color: #ffffff !important;
            border: 1px solid #555555 !important;
        }
        "#.to_string()
    }

    /// Generate monochrome CSS
    fn generate_monochrome_css(&self) -> String {
        r#"
        * {
            filter: grayscale(100%) !important;
        }
        "#.to_string()
    }

    /// Generate motion reduction CSS
    fn generate_motion_reduction_css(&self) -> String {
        match self.user_preferences.motion_preference {
            MotionPreference::Reduce => {
                r#"
                * {
                    animation-duration: 0.1s !important;
                    transition-duration: 0.1s !important;
                }
                "#.to_string()
            }
            MotionPreference::Remove => {
                r#"
                * {
                    animation: none !important;
                    transition: none !important;
                }
                "#.to_string()
            }
            _ => String::new(),
        }
    }

    /// Generate single column layout CSS
    fn generate_single_column_css(&self) -> String {
        r#"
        .container, .row, .grid {
            display: block !important;
            width: 100% !important;
        }
        .col, [class*="col-"] {
            width: 100% !important;
            float: none !important;
            display: block !important;
        }
        "#.to_string()
    }

    /// Generate spacious layout CSS
    fn generate_spacious_layout_css(&self) -> String {
        format!(
            r#"
            * {{
                margin: calc(0.5rem * {}) !important;
                padding: calc(0.5rem * {}) !important;
            }}
            "#,
            self.user_preferences.spacing_multiplier,
            self.user_preferences.spacing_multiplier
        )
    }

    /// Set font size multiplier
    pub async fn set_font_size_multiplier(&mut self, multiplier: f64) -> Result<()> {
        self.user_preferences.font_size_multiplier = multiplier.clamp(0.5, 3.0);
        self.generate_adaptive_css().await?;
        info!("📝 Font size multiplier set to {:.2}", multiplier);
        Ok(())
    }

    /// Initialize default adaptation rules
    fn initialize_default_rules(&mut self) {
        // Add default rules for common adaptations
        let rules = vec![
            AdaptationRule {
                id: "large_font_buttons".to_string(),
                adaptation_type: AdaptationType::ButtonSize,
                condition: AdaptationCondition::FontSizeAbove(1.2),
                css_properties: {
                    let mut props = HashMap::new();
                    props.insert("min-height".to_string(), "48px".to_string());
                    props.insert("padding".to_string(), "12px 16px".to_string());
                    props
                },
                priority: 1,
            },
            AdaptationRule {
                id: "high_contrast_focus".to_string(),
                adaptation_type: AdaptationType::FocusIndicator,
                condition: AdaptationCondition::HighContrastEnabled,
                css_properties: {
                    let mut props = HashMap::new();
                    props.insert("outline".to_string(), "3px solid #000000".to_string());
                    props.insert("outline-offset".to_string(), "2px".to_string());
                    props
                },
                priority: 2,
            },
        ];

        self.adaptation_rules = rules;
    }

    /// Get generated CSS for adaptive styles
    pub fn get_adaptive_css(&self) -> Option<&String> {
        self.css_cache.get("adaptive_styles")
    }

    /// Check if adaptive UI is enabled
    pub fn is_enabled(&self) -> bool {
        self.enabled
    }

    /// Get current user preferences
    pub fn get_preferences(&self) -> &UserPreferences {
        &self.user_preferences
    }
}
