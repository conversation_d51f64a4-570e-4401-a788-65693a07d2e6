//! Contrast Analysis and Dynamic Adjustment
//! 
//! Implements WCAG 3.0 contrast requirements with real-time analysis
//! and automatic adjustments for optimal accessibility.

use serde::{Deserialize, Serialize};
use tracing::{info, debug, warn};

/// Color representation for contrast calculations
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Serialize, Deserialize)]
pub struct Color {
    pub r: u8,
    pub g: u8,
    pub b: u8,
    pub a: f32, // Alpha channel (0.0-1.0)
}

/// Contrast analysis result
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ContrastResult {
    pub ratio: f64,
    pub passes_aa: bool,
    pub passes_aaa: bool,
    pub wcag_level: WcagLevel,
    pub recommendation: Option<String>,
}

/// WCAG Conformance Levels for contrast
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum WcagLevel {
    Fail,
    AA,
    AAA,
}

/// Contrast Analyzer for color accessibility
#[derive(Debug)]
pub struct ContrastAnalyzer {
    minimum_ratio: f64,
    current_ratio: f64,
    auto_adjust: bool,
}

impl Color {
    /// Create a new color from RGB values
    pub fn new(r: u8, g: u8, b: u8) -> Self {
        Self { r, g, b, a: 1.0 }
    }

    /// Create a color from hex string (e.g., "#FF0000")
    pub fn from_hex(hex: &str) -> Result<Self, String> {
        let hex = hex.trim_start_matches('#');
        if hex.len() != 6 {
            return Err("Invalid hex color format".to_string());
        }

        let r = u8::from_str_radix(&hex[0..2], 16).map_err(|_| "Invalid red component")?;
        let g = u8::from_str_radix(&hex[2..4], 16).map_err(|_| "Invalid green component")?;
        let b = u8::from_str_radix(&hex[4..6], 16).map_err(|_| "Invalid blue component")?;

        Ok(Self::new(r, g, b))
    }

    /// Convert to hex string
    pub fn to_hex(&self) -> String {
        format!("#{:02X}{:02X}{:02X}", self.r, self.g, self.b)
    }

    /// Calculate relative luminance according to WCAG formula
    pub fn luminance(&self) -> f64 {
        let r = self.srgb_to_linear(self.r as f64 / 255.0);
        let g = self.srgb_to_linear(self.g as f64 / 255.0);
        let b = self.srgb_to_linear(self.b as f64 / 255.0);

        0.2126 * r + 0.7152 * g + 0.0722 * b
    }

    /// Convert sRGB to linear RGB
    fn srgb_to_linear(&self, value: f64) -> f64 {
        if value <= 0.03928 {
            value / 12.92
        } else {
            ((value + 0.055) / 1.055).powf(2.4)
        }
    }

    /// Adjust brightness by a factor (-1.0 to 1.0)
    pub fn adjust_brightness(&self, factor: f64) -> Self {
        let factor = factor.clamp(-1.0, 1.0);
        
        let adjust = |component: u8| -> u8 {
            let value = component as f64;
            if factor >= 0.0 {
                // Brighten
                (value + (255.0 - value) * factor).round() as u8
            } else {
                // Darken
                (value * (1.0 + factor)).round() as u8
            }
        };

        Self {
            r: adjust(self.r),
            g: adjust(self.g),
            b: adjust(self.b),
            a: self.a,
        }
    }
}

impl ContrastAnalyzer {
    /// Create a new contrast analyzer
    pub fn new(minimum_ratio: f64) -> Self {
        info!("🎨 Initializing Contrast Analyzer (min ratio: {})", minimum_ratio);
        
        Self {
            minimum_ratio,
            current_ratio: 0.0,
            auto_adjust: true,
        }
    }

    /// Calculate contrast ratio between two colors
    pub fn calculate_ratio(&self, foreground: Color, background: Color) -> f64 {
        let fg_luminance = foreground.luminance();
        let bg_luminance = background.luminance();

        let lighter = fg_luminance.max(bg_luminance);
        let darker = fg_luminance.min(bg_luminance);

        (lighter + 0.05) / (darker + 0.05)
    }

    /// Analyze contrast and provide recommendations
    pub fn analyze(&mut self, foreground: Color, background: Color) -> ContrastResult {
        let ratio = self.calculate_ratio(foreground, background);
        self.current_ratio = ratio;

        debug!("🔍 Contrast analysis: {:.2}:1 (fg: {}, bg: {})", 
               ratio, foreground.to_hex(), background.to_hex());

        let passes_aa = ratio >= 4.5;
        let passes_aaa = ratio >= 7.0;

        let wcag_level = if passes_aaa {
            WcagLevel::AAA
        } else if passes_aa {
            WcagLevel::AA
        } else {
            WcagLevel::Fail
        };

        let recommendation = if ratio < self.minimum_ratio {
            Some(self.generate_recommendation(foreground, background, ratio))
        } else {
            None
        };

        ContrastResult {
            ratio,
            passes_aa,
            passes_aaa,
            wcag_level,
            recommendation,
        }
    }

    /// Generate color adjustment recommendations
    fn generate_recommendation(&self, foreground: Color, background: Color, current_ratio: f64) -> String {
        let target_ratio = self.minimum_ratio;
        let improvement_needed = target_ratio / current_ratio;

        if improvement_needed <= 1.5 {
            "Consider slightly darkening the text or lightening the background.".to_string()
        } else if improvement_needed <= 2.0 {
            "Significant contrast improvement needed. Try using a darker text color or lighter background.".to_string()
        } else {
            "Major contrast adjustment required. Consider using high-contrast colors like black text on white background.".to_string()
        }
    }

    /// Automatically adjust colors to meet minimum contrast ratio
    pub fn auto_adjust_colors(&self, foreground: Color, background: Color) -> (Color, Color) {
        let current_ratio = self.calculate_ratio(foreground, background);
        
        if current_ratio >= self.minimum_ratio {
            return (foreground, background);
        }

        info!("🔧 Auto-adjusting colors for better contrast");

        // Try darkening foreground first
        let mut adjusted_fg = foreground;
        let mut adjusted_bg = background;

        // Iteratively adjust until we meet the minimum ratio
        for i in 1..=10 {
            let darken_factor = -0.1 * i as f64;
            adjusted_fg = foreground.adjust_brightness(darken_factor);
            
            let new_ratio = self.calculate_ratio(adjusted_fg, adjusted_bg);
            if new_ratio >= self.minimum_ratio {
                debug!("✅ Achieved contrast ratio: {:.2}:1", new_ratio);
                return (adjusted_fg, adjusted_bg);
            }
        }

        // If darkening foreground didn't work, try lightening background
        adjusted_fg = foreground;
        for i in 1..=10 {
            let lighten_factor = 0.1 * i as f64;
            adjusted_bg = background.adjust_brightness(lighten_factor);
            
            let new_ratio = self.calculate_ratio(adjusted_fg, adjusted_bg);
            if new_ratio >= self.minimum_ratio {
                debug!("✅ Achieved contrast ratio: {:.2}:1", new_ratio);
                return (adjusted_fg, adjusted_bg);
            }
        }

        // Last resort: use high contrast colors
        warn!("⚠️ Could not achieve target contrast, using high contrast fallback");
        (Color::new(0, 0, 0), Color::new(255, 255, 255)) // Black on white
    }

    /// Set minimum contrast ratio
    pub fn set_minimum_ratio(&mut self, ratio: f64) {
        info!("⚙️ Setting minimum contrast ratio to {:.2}:1", ratio);
        self.minimum_ratio = ratio;
    }

    /// Get current contrast ratio
    pub fn get_current_ratio(&self) -> f64 {
        self.current_ratio
    }

    /// Enable or disable auto-adjustment
    pub fn set_auto_adjust(&mut self, enabled: bool) {
        self.auto_adjust = enabled;
        info!("🔧 Auto-adjustment {}", if enabled { "enabled" } else { "disabled" });
    }

    /// Check if a color combination meets WCAG standards
    pub fn meets_wcag_standard(&self, foreground: Color, background: Color, level: WcagLevel) -> bool {
        let ratio = self.calculate_ratio(foreground, background);
        
        match level {
            WcagLevel::AA => ratio >= 4.5,
            WcagLevel::AAA => ratio >= 7.0,
            WcagLevel::Fail => false,
        }
    }

    /// Generate a palette of accessible colors
    pub fn generate_accessible_palette(&self, base_color: Color) -> Vec<Color> {
        let mut palette = Vec::new();
        
        // Generate variations that maintain good contrast
        for i in 0..5 {
            let brightness_factor = -0.2 * i as f64;
            let variant = base_color.adjust_brightness(brightness_factor);
            palette.push(variant);
        }

        for i in 1..5 {
            let brightness_factor = 0.2 * i as f64;
            let variant = base_color.adjust_brightness(brightness_factor);
            palette.push(variant);
        }

        palette
    }
}

impl Default for ContrastAnalyzer {
    fn default() -> Self {
        Self::new(4.5) // WCAG AA standard
    }
}
