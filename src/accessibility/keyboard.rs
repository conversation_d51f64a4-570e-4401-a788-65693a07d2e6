//! Keyboard Navigation and Focus Management
//! 
//! Implements comprehensive keyboard accessibility following WCAG 3.0 guidelines
//! with enhanced focus indicators and navigation patterns.

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{info, debug, warn};

use crate::Result;

/// Keyboard Event Types
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum KeyboardEvent {
    KeyDown(KeyCode),
    KeyUp(KeyCode),
    KeyPress(KeyCode),
}

/// Key Codes for keyboard navigation
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum KeyCode {
    // Navigation keys
    Tab,
    ShiftTab,
    Enter,
    Space,
    Escape,
    
    // Arrow keys
    ArrowUp,
    ArrowDown,
    ArrowLeft,
    ArrowRight,
    
    // Function keys
    F1, F2, F3, F4, F5, F6, F7, F8, F9, F10, F11, F12,
    
    // Modifier keys
    Shift,
    <PERSON>trl,
    Alt,
    Meta,
    
    // Text editing
    Backspace,
    Delete,
    Home,
    End,
    PageUp,
    PageDown,
    
    // Letters and numbers
    Letter(char),
    Number(u8),
    
    // Special accessibility keys
    AccessKey(char),
    
    // Custom key combinations
    Combination(Vec<KeyCode>),
}

/// Focus Management State
#[derive(Debug, Clone)]
pub struct FocusState {
    pub current_element: Option<String>,
    pub focus_ring_visible: bool,
    pub focus_trap_active: bool,
    pub focus_trap_container: Option<String>,
    pub tab_order: Vec<String>,
    pub current_tab_index: usize,
}

/// Keyboard Shortcut Definition
#[derive(Debug, Clone)]
pub struct KeyboardShortcut {
    pub keys: Vec<KeyCode>,
    pub action: String,
    pub description: String,
    pub context: Option<String>, // Context where shortcut is active
}

/// Keyboard Handler for accessibility
#[derive(Debug)]
pub struct KeyboardHandler {
    focus_state: FocusState,
    shortcuts: HashMap<Vec<KeyCode>, KeyboardShortcut>,
    enhanced_focus: bool,
    skip_links: Vec<SkipLink>,
    access_keys: HashMap<char, String>, // access key -> element id
    roving_tabindex_groups: HashMap<String, RovingTabindexGroup>,
}

/// Skip Link for keyboard navigation
#[derive(Debug, Clone)]
pub struct SkipLink {
    pub id: String,
    pub text: String,
    pub target: String,
    pub visible_on_focus: bool,
}

/// Roving Tabindex Group for complex widgets
#[derive(Debug, Clone)]
pub struct RovingTabindexGroup {
    pub name: String,
    pub elements: Vec<String>,
    pub current_index: usize,
    pub orientation: Orientation,
    pub wrap: bool,
}

/// Orientation for roving tabindex navigation
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum Orientation {
    Horizontal,
    Vertical,
    Both,
}

impl KeyboardHandler {
    /// Create a new keyboard handler
    pub fn new(enhanced_focus: bool) -> Result<Self> {
        info!("⌨️ Initializing Keyboard Handler (enhanced focus: {})", enhanced_focus);
        
        let mut handler = Self {
            focus_state: FocusState {
                current_element: None,
                focus_ring_visible: false,
                focus_trap_active: false,
                focus_trap_container: None,
                tab_order: Vec::new(),
                current_tab_index: 0,
            },
            shortcuts: HashMap::new(),
            enhanced_focus,
            skip_links: Vec::new(),
            access_keys: HashMap::new(),
            roving_tabindex_groups: HashMap::new(),
        };

        handler.initialize_default_shortcuts();
        handler.create_default_skip_links();
        
        Ok(handler)
    }

    /// Enable keyboard handler
    pub async fn enable(&mut self) -> Result<()> {
        info!("🔓 Enabling keyboard navigation");
        self.focus_state.focus_ring_visible = true;
        Ok(())
    }

    /// Enable switch control mode for assistive devices
    pub async fn enable_switch_mode(&mut self) -> Result<()> {
        info!("🔄 Enabling switch control mode");
        // Configure for single-switch or dual-switch navigation
        self.focus_state.focus_ring_visible = true;
        // Add switch-specific navigation patterns
        Ok(())
    }

    /// Handle keyboard events
    pub async fn handle_event(&mut self, event: KeyboardEvent) -> Result<bool> {
        match event {
            KeyboardEvent::KeyDown(key) => self.handle_key_down(key).await,
            KeyboardEvent::KeyUp(key) => self.handle_key_up(key).await,
            KeyboardEvent::KeyPress(key) => self.handle_key_press(key).await,
        }
    }

    /// Handle key down events
    async fn handle_key_down(&mut self, key: KeyCode) -> Result<bool> {
        debug!("⌨️ Key down: {:?}", key);
        
        match key {
            KeyCode::Tab => {
                self.handle_tab_navigation(false).await?;
                Ok(true)
            }
            KeyCode::ShiftTab => {
                self.handle_tab_navigation(true).await?;
                Ok(true)
            }
            KeyCode::Enter | KeyCode::Space => {
                self.handle_activation().await?;
                Ok(true)
            }
            KeyCode::Escape => {
                self.handle_escape().await?;
                Ok(true)
            }
            KeyCode::ArrowUp | KeyCode::ArrowDown | KeyCode::ArrowLeft | KeyCode::ArrowRight => {
                self.handle_arrow_navigation(key).await?;
                Ok(true)
            }
            KeyCode::AccessKey(ch) => {
                self.handle_access_key(ch).await?;
                Ok(true)
            }
            _ => Ok(false), // Event not handled
        }
    }

    /// Handle key up events
    async fn handle_key_up(&mut self, _key: KeyCode) -> Result<bool> {
        // Most accessibility features respond to key down, not key up
        Ok(false)
    }

    /// Handle key press events
    async fn handle_key_press(&mut self, key: KeyCode) -> Result<bool> {
        // Handle character input for search/filter functionality
        if let KeyCode::Letter(_) | KeyCode::Number(_) = key {
            // Could implement type-ahead search here
        }
        Ok(false)
    }

    /// Handle tab navigation
    async fn handle_tab_navigation(&mut self, reverse: bool) -> Result<()> {
        if self.focus_state.tab_order.is_empty() {
            warn!("⚠️ No tab order defined");
            return Ok(());
        }

        let current_index = self.focus_state.current_tab_index;
        let new_index = if reverse {
            if current_index == 0 {
                self.focus_state.tab_order.len() - 1
            } else {
                current_index - 1
            }
        } else {
            (current_index + 1) % self.focus_state.tab_order.len()
        };

        self.focus_state.current_tab_index = new_index;
        
        if let Some(element_id) = self.focus_state.tab_order.get(new_index) {
            self.set_focus(element_id.clone()).await?;
        }

        Ok(())
    }

    /// Handle arrow key navigation for complex widgets
    async fn handle_arrow_navigation(&mut self, key: KeyCode) -> Result<()> {
        // Check if current element is part of a roving tabindex group
        if let Some(current_element) = &self.focus_state.current_element {
            let current_element = current_element.clone();

            // Find the group that contains the current element
            let group_name = self.roving_tabindex_groups
                .iter()
                .find(|(_, group)| group.elements.contains(&current_element))
                .map(|(name, _)| name.clone());

            if let Some(group_name) = group_name {
                self.navigate_roving_tabindex(&group_name, key).await?;
                return Ok(());
            }
        }
        Ok(())
    }

    /// Navigate within a roving tabindex group
    async fn navigate_roving_tabindex(&mut self, group_name: &str, key: KeyCode) -> Result<()> {
        if let Some(group) = self.roving_tabindex_groups.get_mut(group_name) {
            let should_move = match (&group.orientation, &key) {
                (Orientation::Horizontal, KeyCode::ArrowLeft) => Some(true),  // Previous
                (Orientation::Horizontal, KeyCode::ArrowRight) => Some(false), // Next
                (Orientation::Vertical, KeyCode::ArrowUp) => Some(true),      // Previous
                (Orientation::Vertical, KeyCode::ArrowDown) => Some(false),   // Next
                (Orientation::Both, KeyCode::ArrowLeft | KeyCode::ArrowUp) => Some(true),
                (Orientation::Both, KeyCode::ArrowRight | KeyCode::ArrowDown) => Some(false),
                _ => None,
            };

            if let Some(reverse) = should_move {
                let new_index = if reverse {
                    if group.current_index == 0 {
                        if group.wrap {
                            group.elements.len() - 1
                        } else {
                            0 // Stay at first element
                        }
                    } else {
                        group.current_index - 1
                    }
                } else {
                    let next = group.current_index + 1;
                    if next >= group.elements.len() {
                        if group.wrap {
                            0
                        } else {
                            group.elements.len() - 1 // Stay at last element
                        }
                    } else {
                        next
                    }
                };

                group.current_index = new_index;

                if let Some(element_id) = group.elements.get(new_index) {
                    let element_id = element_id.clone();
                    drop(group); // Release the mutable borrow
                    self.set_focus(element_id).await?;
                }
            }
        }

        Ok(())
    }

    /// Handle element activation (Enter/Space)
    async fn handle_activation(&mut self) -> Result<()> {
        if let Some(element_id) = &self.focus_state.current_element {
            info!("🎯 Activating element: {}", element_id);
            // Trigger click/activation event for the focused element
        }
        Ok(())
    }

    /// Handle escape key
    async fn handle_escape(&mut self) -> Result<()> {
        if self.focus_state.focus_trap_active {
            info!("🚪 Escaping focus trap");
            self.release_focus_trap().await?;
        }
        Ok(())
    }

    /// Handle access key activation
    async fn handle_access_key(&mut self, key: char) -> Result<()> {
        if let Some(element_id) = self.access_keys.get(&key) {
            info!("🔑 Access key '{}' activated for element: {}", key, element_id);
            self.set_focus(element_id.clone()).await?;
        }
        Ok(())
    }

    /// Set focus to an element
    pub async fn set_focus(&mut self, element_id: String) -> Result<()> {
        debug!("🎯 Setting focus to: {}", element_id);
        self.focus_state.current_element = Some(element_id);
        self.focus_state.focus_ring_visible = true;
        Ok(())
    }

    /// Create a focus trap for modal dialogs
    pub async fn create_focus_trap(&mut self, container_id: String, focusable_elements: Vec<String>) -> Result<()> {
        info!("🔒 Creating focus trap in container: {}", container_id);
        
        self.focus_state.focus_trap_active = true;
        self.focus_state.focus_trap_container = Some(container_id);
        self.focus_state.tab_order = focusable_elements;
        self.focus_state.current_tab_index = 0;
        
        // Focus first element in trap
        if let Some(first_element) = self.focus_state.tab_order.first() {
            self.set_focus(first_element.clone()).await?;
        }
        
        Ok(())
    }

    /// Release focus trap
    pub async fn release_focus_trap(&mut self) -> Result<()> {
        info!("🔓 Releasing focus trap");
        
        self.focus_state.focus_trap_active = false;
        self.focus_state.focus_trap_container = None;
        self.focus_state.tab_order.clear();
        self.focus_state.current_tab_index = 0;
        
        Ok(())
    }

    /// Add a keyboard shortcut
    pub fn add_shortcut(&mut self, shortcut: KeyboardShortcut) {
        info!("⌨️ Adding keyboard shortcut: {:?} -> {}", shortcut.keys, shortcut.action);
        self.shortcuts.insert(shortcut.keys.clone(), shortcut);
    }

    /// Add an access key
    pub fn add_access_key(&mut self, key: char, element_id: String) {
        info!("🔑 Adding access key '{}' for element: {}", key, element_id);
        self.access_keys.insert(key, element_id);
    }

    /// Create a roving tabindex group
    pub fn create_roving_tabindex_group(&mut self, group: RovingTabindexGroup) {
        info!("🔄 Creating roving tabindex group: {}", group.name);
        self.roving_tabindex_groups.insert(group.name.clone(), group);
    }

    /// Initialize default keyboard shortcuts
    fn initialize_default_shortcuts(&mut self) {
        let shortcuts = vec![
            KeyboardShortcut {
                keys: vec![KeyCode::Alt, KeyCode::Letter('h')],
                action: "show_help".to_string(),
                description: "Show help dialog".to_string(),
                context: None,
            },
            KeyboardShortcut {
                keys: vec![KeyCode::Alt, KeyCode::Letter('s')],
                action: "skip_to_content".to_string(),
                description: "Skip to main content".to_string(),
                context: None,
            },
            KeyboardShortcut {
                keys: vec![KeyCode::Ctrl, KeyCode::Letter('/')],
                action: "show_shortcuts".to_string(),
                description: "Show keyboard shortcuts".to_string(),
                context: None,
            },
        ];

        for shortcut in shortcuts {
            self.add_shortcut(shortcut);
        }
    }

    /// Create default skip links
    fn create_default_skip_links(&mut self) {
        self.skip_links = vec![
            SkipLink {
                id: "skip-to-content".to_string(),
                text: "Skip to main content".to_string(),
                target: "main-content".to_string(),
                visible_on_focus: true,
            },
            SkipLink {
                id: "skip-to-navigation".to_string(),
                text: "Skip to navigation".to_string(),
                target: "main-navigation".to_string(),
                visible_on_focus: true,
            },
        ];
    }

    /// Get current focus state
    pub fn get_focus_state(&self) -> &FocusState {
        &self.focus_state
    }

    /// Get available keyboard shortcuts
    pub fn get_shortcuts(&self) -> Vec<&KeyboardShortcut> {
        self.shortcuts.values().collect()
    }
}
