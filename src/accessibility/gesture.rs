//! Gesture Recognition for Accessibility
//! 
//! Implements gesture-based navigation and interaction for users with motor disabilities
//! following WCAG 3.0 guidelines for alternative input methods.

use serde::{Deserialize, Serialize};
use std::time::{Duration, SystemTime};
use tracing::{info, debug, warn};

use crate::Result;

/// Gesture Types
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum GestureType {
    // Single finger gestures
    Tap,
    DoubleTap,
    LongPress,
    Swipe(SwipeDirection),
    
    // Multi-finger gestures
    Pinch,
    Spread,
    TwoFingerTap,
    TwoFingerSwipe(SwipeDirection),
    ThreeFingerTap,
    ThreeFingerSwipe(SwipeDirection),
    
    // Head gestures (for head tracking)
    HeadNod,
    HeadShake,
    HeadTilt(TiltDirection),
    
    // Custom gestures
    Custom(String),
}

/// Swipe Direction
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SwipeDirection {
    Up,
    Down,
    Left,
    Right,
    UpLeft,
    UpRight,
    <PERSON><PERSON>ef<PERSON>,
    DownRight,
}

/// Tilt Direction for head gestures
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON>ial<PERSON>q, <PERSON>q, <PERSON>h, Serialize, <PERSON>erialize)]
pub enum TiltDirection {
    Left,
    Right,
}

/// Touch Point Data
#[derive(Debug, Clone, Copy)]
pub struct TouchPoint {
    pub id: u32,
    pub x: f64,
    pub y: f64,
    pub pressure: f64,
    pub timestamp: SystemTime,
}

/// Gesture Event
#[derive(Debug, Clone)]
pub struct GestureEvent {
    pub gesture_type: GestureType,
    pub start_point: TouchPoint,
    pub end_point: Option<TouchPoint>,
    pub duration: Duration,
    pub confidence: f64,
    pub element_id: Option<String>,
}

/// Gesture Recognition Configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GestureConfig {
    /// Enable gesture recognition
    pub enabled: bool,
    /// Minimum swipe distance in pixels
    pub min_swipe_distance: f64,
    /// Maximum swipe time in milliseconds
    pub max_swipe_time: u64,
    /// Long press duration in milliseconds
    pub long_press_duration: u64,
    /// Double tap maximum interval in milliseconds
    pub double_tap_interval: u64,
    /// Gesture sensitivity (0.0-1.0)
    pub sensitivity: f64,
    /// Enable multi-finger gestures
    pub multi_finger_enabled: bool,
    /// Enable head gesture recognition
    pub head_gestures_enabled: bool,
    /// Gesture timeout in milliseconds
    pub gesture_timeout: u64,
}

/// Gesture State for tracking ongoing gestures
#[derive(Debug, Clone)]
pub struct GestureState {
    pub active_touches: Vec<TouchPoint>,
    pub gesture_start_time: Option<SystemTime>,
    pub last_tap_time: Option<SystemTime>,
    pub tap_count: u32,
    pub is_long_press: bool,
    pub swipe_start: Option<TouchPoint>,
}

/// Gesture Recognizer
#[derive(Debug)]
pub struct GestureRecognizer {
    config: GestureConfig,
    is_enabled: bool,
    gesture_state: GestureState,
    gesture_handlers: std::collections::HashMap<GestureType, String>, // gesture -> action
    custom_gestures: std::collections::HashMap<String, Vec<TouchPoint>>, // name -> pattern
}

impl Default for GestureConfig {
    fn default() -> Self {
        Self {
            enabled: false,
            min_swipe_distance: 50.0,
            max_swipe_time: 1000,
            long_press_duration: 500,
            double_tap_interval: 300,
            sensitivity: 0.7,
            multi_finger_enabled: true,
            head_gestures_enabled: false,
            gesture_timeout: 2000,
        }
    }
}

impl GestureRecognizer {
    /// Create a new gesture recognizer
    pub fn new() -> Result<Self> {
        info!("👋 Initializing Gesture Recognizer");
        
        let mut recognizer = Self {
            config: GestureConfig::default(),
            is_enabled: false,
            gesture_state: GestureState {
                active_touches: Vec::new(),
                gesture_start_time: None,
                last_tap_time: None,
                tap_count: 0,
                is_long_press: false,
                swipe_start: None,
            },
            gesture_handlers: std::collections::HashMap::new(),
            custom_gestures: std::collections::HashMap::new(),
        };

        recognizer.initialize_default_gestures();
        Ok(recognizer)
    }

    /// Enable gesture recognition
    pub async fn enable(&mut self) -> Result<()> {
        info!("✋ Enabling gesture recognition");
        self.is_enabled = true;
        Ok(())
    }

    /// Disable gesture recognition
    pub async fn disable(&mut self) -> Result<()> {
        info!("🚫 Disabling gesture recognition");
        self.is_enabled = false;
        self.reset_gesture_state();
        Ok(())
    }

    /// Process touch start event
    pub async fn touch_start(&mut self, touch: TouchPoint) -> Result<Option<GestureEvent>> {
        if !self.is_enabled {
            return Ok(None);
        }

        debug!("👆 Touch start: id={}, pos=({:.1}, {:.1})", touch.id, touch.x, touch.y);
        
        self.gesture_state.active_touches.push(touch);
        self.gesture_state.gesture_start_time = Some(touch.timestamp);
        
        // Check for multi-finger gestures
        if self.config.multi_finger_enabled && self.gesture_state.active_touches.len() > 1 {
            return self.check_multi_finger_start().await;
        }
        
        Ok(None)
    }

    /// Process touch move event
    pub async fn touch_move(&mut self, touch: TouchPoint) -> Result<Option<GestureEvent>> {
        if !self.is_enabled {
            return Ok(None);
        }

        // Update touch position
        if let Some(existing_touch) = self.gesture_state.active_touches.iter_mut().find(|t| t.id == touch.id) {
            *existing_touch = touch;
        }
        
        // Check for swipe gestures
        if self.gesture_state.active_touches.len() == 1 {
            return self.check_swipe_gesture(touch).await;
        }
        
        Ok(None)
    }

    /// Process touch end event
    pub async fn touch_end(&mut self, touch: TouchPoint) -> Result<Option<GestureEvent>> {
        if !self.is_enabled {
            return Ok(None);
        }

        debug!("👆 Touch end: id={}", touch.id);
        
        // Remove touch from active touches
        self.gesture_state.active_touches.retain(|t| t.id != touch.id);
        
        let gesture_event = if self.gesture_state.active_touches.is_empty() {
            // All touches ended, check for completed gestures
            self.check_completed_gesture(touch).await?
        } else {
            None
        };
        
        // Reset state if no active touches
        if self.gesture_state.active_touches.is_empty() {
            self.reset_gesture_state();
        }
        
        Ok(gesture_event)
    }

    /// Check for completed gestures when all touches end
    async fn check_completed_gesture(&mut self, last_touch: TouchPoint) -> Result<Option<GestureEvent>> {
        if let Some(start_time) = self.gesture_state.gesture_start_time {
            let duration = last_touch.timestamp.duration_since(start_time).unwrap_or_default();
            
            // Check for long press
            if duration.as_millis() >= self.config.long_press_duration as u128 {
                return Ok(Some(GestureEvent {
                    gesture_type: GestureType::LongPress,
                    start_point: last_touch,
                    end_point: None,
                    duration,
                    confidence: 0.9,
                    element_id: None,
                }));
            }
            
            // Check for tap or double tap
            if duration.as_millis() < self.config.long_press_duration as u128 {
                return self.check_tap_gesture(last_touch, duration).await;
            }
        }
        
        Ok(None)
    }

    /// Check for tap or double tap gestures
    async fn check_tap_gesture(&mut self, touch: TouchPoint, duration: Duration) -> Result<Option<GestureEvent>> {
        let now = touch.timestamp;
        
        // Check for double tap
        if let Some(last_tap_time) = self.gesture_state.last_tap_time {
            let interval = now.duration_since(last_tap_time).unwrap_or_default();
            
            if interval.as_millis() <= self.config.double_tap_interval as u128 {
                self.gesture_state.tap_count += 1;
                
                if self.gesture_state.tap_count >= 2 {
                    self.gesture_state.tap_count = 0;
                    self.gesture_state.last_tap_time = None;
                    
                    return Ok(Some(GestureEvent {
                        gesture_type: GestureType::DoubleTap,
                        start_point: touch,
                        end_point: None,
                        duration,
                        confidence: 0.9,
                        element_id: None,
                    }));
                }
            } else {
                // Reset tap count if interval too long
                self.gesture_state.tap_count = 1;
            }
        } else {
            self.gesture_state.tap_count = 1;
        }
        
        self.gesture_state.last_tap_time = Some(now);
        
        // Return single tap
        Ok(Some(GestureEvent {
            gesture_type: GestureType::Tap,
            start_point: touch,
            end_point: None,
            duration,
            confidence: 0.8,
            element_id: None,
        }))
    }

    /// Check for swipe gestures during touch move
    async fn check_swipe_gesture(&mut self, current_touch: TouchPoint) -> Result<Option<GestureEvent>> {
        if let Some(start_touch) = self.gesture_state.active_touches.first() {
            let distance = ((current_touch.x - start_touch.x).powi(2) + (current_touch.y - start_touch.y).powi(2)).sqrt();
            
            if distance >= self.config.min_swipe_distance {
                let direction = self.calculate_swipe_direction(*start_touch, current_touch);
                let duration = current_touch.timestamp.duration_since(start_touch.timestamp).unwrap_or_default();
                
                if duration.as_millis() <= self.config.max_swipe_time as u128 {
                    return Ok(Some(GestureEvent {
                        gesture_type: GestureType::Swipe(direction),
                        start_point: *start_touch,
                        end_point: Some(current_touch),
                        duration,
                        confidence: 0.8,
                        element_id: None,
                    }));
                }
            }
        }
        
        Ok(None)
    }

    /// Calculate swipe direction
    fn calculate_swipe_direction(&self, start: TouchPoint, end: TouchPoint) -> SwipeDirection {
        let dx = end.x - start.x;
        let dy = end.y - start.y;
        
        let angle = dy.atan2(dx).to_degrees();
        
        match angle {
            a if a >= -22.5 && a < 22.5 => SwipeDirection::Right,
            a if a >= 22.5 && a < 67.5 => SwipeDirection::DownRight,
            a if a >= 67.5 && a < 112.5 => SwipeDirection::Down,
            a if a >= 112.5 && a < 157.5 => SwipeDirection::DownLeft,
            a if a >= 157.5 || a < -157.5 => SwipeDirection::Left,
            a if a >= -157.5 && a < -112.5 => SwipeDirection::UpLeft,
            a if a >= -112.5 && a < -67.5 => SwipeDirection::Up,
            a if a >= -67.5 && a < -22.5 => SwipeDirection::UpRight,
            _ => SwipeDirection::Right, // Default
        }
    }

    /// Check for multi-finger gesture start
    async fn check_multi_finger_start(&mut self) -> Result<Option<GestureEvent>> {
        let touch_count = self.gesture_state.active_touches.len();
        
        match touch_count {
            2 => {
                // Could be pinch/spread or two-finger tap
                debug!("👆👆 Two-finger gesture detected");
            }
            3 => {
                debug!("👆👆👆 Three-finger gesture detected");
            }
            _ => {}
        }
        
        Ok(None)
    }

    /// Register a gesture handler
    pub fn register_gesture_handler(&mut self, gesture: GestureType, action: String) {
        info!("👋 Registering gesture handler: {:?} -> {}", gesture, action);
        self.gesture_handlers.insert(gesture, action);
    }

    /// Add a custom gesture pattern
    pub fn add_custom_gesture(&mut self, name: String, pattern: Vec<TouchPoint>) {
        info!("✨ Adding custom gesture: {}", name);
        self.custom_gestures.insert(name, pattern);
    }

    /// Reset gesture state
    fn reset_gesture_state(&mut self) {
        self.gesture_state = GestureState {
            active_touches: Vec::new(),
            gesture_start_time: None,
            last_tap_time: self.gesture_state.last_tap_time, // Keep for double tap detection
            tap_count: self.gesture_state.tap_count,
            is_long_press: false,
            swipe_start: None,
        };
    }

    /// Initialize default gesture handlers
    fn initialize_default_gestures(&mut self) {
        let default_gestures = vec![
            (GestureType::Tap, "click".to_string()),
            (GestureType::DoubleTap, "double_click".to_string()),
            (GestureType::LongPress, "context_menu".to_string()),
            (GestureType::Swipe(SwipeDirection::Up), "scroll_up".to_string()),
            (GestureType::Swipe(SwipeDirection::Down), "scroll_down".to_string()),
            (GestureType::Swipe(SwipeDirection::Left), "navigate_back".to_string()),
            (GestureType::Swipe(SwipeDirection::Right), "navigate_forward".to_string()),
            (GestureType::TwoFingerTap, "right_click".to_string()),
            (GestureType::ThreeFingerTap, "show_menu".to_string()),
        ];

        for (gesture, action) in default_gestures {
            self.register_gesture_handler(gesture, action);
        }
    }

    /// Update configuration
    pub fn update_config(&mut self, config: GestureConfig) {
        info!("⚙️ Updating gesture recognition configuration");
        self.config = config;
    }

    /// Get gesture handler for a gesture type
    pub fn get_gesture_handler(&self, gesture: &GestureType) -> Option<&String> {
        self.gesture_handlers.get(gesture)
    }

    /// Check if gesture recognition is enabled
    pub fn is_enabled(&self) -> bool {
        self.is_enabled
    }
}
