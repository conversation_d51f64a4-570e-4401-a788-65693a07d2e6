//! Neural models for enterprise TTS/STT system
//!
//! This module provides:
//! - High-performance neural model inference using Candle
//! - Model loading and caching with enterprise features
//! - Memory-efficient model management
//! - GPU acceleration support
//! - Formal verification for critical paths

use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, instrument, warn};
use serde::{Deserialize, Serialize};

use crate::{
    audio::AudioData,
    config::{Config, AudioFormat},
    engine::{SynthesisOptions, RecognitionOptions, WordTimestamp},
    ChatterboxError, Result,
};
use crate::error::{ModelErrorCode, ResourceType};

pub mod tts;
pub mod stt;

pub use tts::TtsModel;
pub use stt::SttModel;

/// Model manager for enterprise model lifecycle management
pub struct ModelManager {
    /// Configuration
    config: Arc<Config>,
    /// Loaded TTS models
    tts_models: Arc<RwLock<HashMap<String, Arc<dyn TtsModel>>>>,
    /// Loaded STT models
    stt_models: Arc<RwLock<HashMap<String, Arc<dyn SttModel>>>>,
    /// Model cache directory
    cache_dir: PathBuf,
    /// Maximum models in memory
    max_models: usize,
}

/// Model metadata for tracking and management
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelMetadata {
    /// Model name/identifier
    pub name: String,
    /// Model version
    pub version: String,
    /// Model type (TTS/STT)
    pub model_type: ModelType,
    /// Model size in bytes
    pub size_bytes: u64,
    /// Supported languages
    pub languages: Vec<String>,
    /// Model capabilities
    pub capabilities: ModelCapabilities,
    /// Load timestamp
    pub loaded_at: Option<chrono::DateTime<chrono::Utc>>,
    /// Last used timestamp
    pub last_used: Option<chrono::DateTime<chrono::Utc>>,
}

/// Model type enumeration
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ModelType {
    /// Text-to-Speech model
    Tts,
    /// Speech-to-Text model
    Stt,
}

/// Model capabilities
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelCapabilities {
    /// Supports real-time processing
    pub realtime: bool,
    /// Supports voice cloning
    pub voice_cloning: bool,
    /// Supports emotion/style control
    pub emotion_control: bool,
    /// Supports multiple speakers
    pub multi_speaker: bool,
    /// Maximum audio length in seconds
    pub max_audio_length: f32,
    /// Supported sample rates
    pub sample_rates: Vec<u32>,
}

/// TTS synthesis result from model
#[derive(Debug, Clone)]
pub struct TtsSynthesisResult {
    /// Generated audio data
    pub audio: AudioData,
    /// Model used
    pub model_name: String,
    /// Synthesis parameters used
    pub parameters: TtsSynthesisParameters,
}

/// STT recognition result from model
#[derive(Debug, Clone)]
pub struct SttRecognitionResult {
    /// Recognized text
    pub text: String,
    /// Recognition confidence
    pub confidence: f32,
    /// Audio duration processed
    pub audio_duration: f32,
    /// Detected language
    pub language: Option<String>,
    /// Word-level timestamps
    pub word_timestamps: Option<Vec<WordTimestamp>>,
}

/// TTS synthesis parameters
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TtsSynthesisParameters {
    /// Speaking rate
    pub speaking_rate: f32,
    /// Pitch adjustment
    pub pitch: f32,
    /// Volume level
    pub volume: f32,
    /// Emotion/style
    pub emotion: Option<String>,
}

impl ModelManager {
    /// Create a new model manager
    #[instrument(skip(config))]
    pub async fn new(config: Arc<Config>) -> Result<Self> {
        let cache_dir = config.models.cache_dir.clone();
        let max_models = config.models.max_models_in_memory;

        // Ensure cache directory exists
        tokio::fs::create_dir_all(&cache_dir).await.map_err(|e| {
            ChatterboxError::configuration(
                format!("Failed to create model cache directory: {}", e),
                "models.cache_dir",
            )
        })?;

        info!(
            cache_dir = %cache_dir.display(),
            max_models = max_models,
            "Model manager initialized"
        );

        Ok(Self {
            config,
            tts_models: Arc::new(RwLock::new(HashMap::new())),
            stt_models: Arc::new(RwLock::new(HashMap::new())),
            cache_dir,
            max_models,
        })
    }

    /// Get or load a TTS model
    #[instrument(skip(self))]
    pub async fn get_tts_model(&self, model_name: &str) -> Result<Arc<dyn TtsModel>> {
        // Check if model is already loaded
        {
            let models = self.tts_models.read().await;
            if let Some(model) = models.get(model_name) {
                debug!(model_name = model_name, "TTS model found in cache");
                return Ok(model.clone());
            }
        }

        // Load model
        info!(model_name = model_name, "Loading TTS model");
        let model = self.load_tts_model(model_name).await?;

        // Store in cache
        {
            let mut models = self.tts_models.write().await;
            
            // Check if we need to evict models
            if models.len() >= self.max_models {
                self.evict_least_used_tts_model(&mut models).await;
            }
            
            models.insert(model_name.to_string(), model.clone());
        }

        info!(model_name = model_name, "TTS model loaded successfully");
        Ok(model)
    }

    /// Get or load an STT model
    #[instrument(skip(self))]
    pub async fn get_stt_model(&self, model_name: &str) -> Result<Arc<dyn SttModel>> {
        // Check if model is already loaded
        {
            let models = self.stt_models.read().await;
            if let Some(model) = models.get(model_name) {
                debug!(model_name = model_name, "STT model found in cache");
                return Ok(model.clone());
            }
        }

        // Load model
        info!(model_name = model_name, "Loading STT model");
        let model = self.load_stt_model(model_name).await?;

        // Store in cache
        {
            let mut models = self.stt_models.write().await;
            
            // Check if we need to evict models
            if models.len() >= self.max_models {
                self.evict_least_used_stt_model(&mut models).await;
            }
            
            models.insert(model_name.to_string(), model.clone());
        }

        info!(model_name = model_name, "STT model loaded successfully");
        Ok(model)
    }

    /// Load a TTS model from disk or download
    async fn load_tts_model(&self, model_name: &str) -> Result<Arc<dyn TtsModel>> {
        let model_path = self.cache_dir.join(format!("{}.tts", model_name));
        
        // Check if model exists locally
        if !model_path.exists() {
            return Err(ChatterboxError::model_inference(
                format!("TTS model '{}' not found", model_name),
                model_name.to_string(),
                ModelErrorCode::ModelNotLoaded,
            ));
        }

        // Load model based on type
        match model_name {
            "default-tts" => {
                let model = tts::DefaultTtsModel::load(&model_path).await?;
                Ok(Arc::new(model))
            }
            _ => Err(ChatterboxError::model_inference(
                format!("Unknown TTS model: {}", model_name),
                model_name.to_string(),
                ModelErrorCode::ModelNotLoaded,
            )),
        }
    }

    /// Load an STT model from disk or download
    async fn load_stt_model(&self, model_name: &str) -> Result<Arc<dyn SttModel>> {
        let model_path = self.cache_dir.join(format!("{}.stt", model_name));
        
        // Check if model exists locally
        if !model_path.exists() {
            return Err(ChatterboxError::model_inference(
                format!("STT model '{}' not found", model_name),
                model_name.to_string(),
                ModelErrorCode::ModelNotLoaded,
            ));
        }

        // Load model based on type
        match model_name {
            "default-stt" => {
                let model = stt::DefaultSttModel::load(&model_path).await?;
                Ok(Arc::new(model))
            }
            _ => Err(ChatterboxError::model_inference(
                format!("Unknown STT model: {}", model_name),
                model_name.to_string(),
                ModelErrorCode::ModelNotLoaded,
            )),
        }
    }

    /// Evict least recently used TTS model
    async fn evict_least_used_tts_model(&self, models: &mut HashMap<String, Arc<dyn TtsModel>>) {
        // In a real implementation, this would track usage timestamps
        // For now, just remove the first model
        if let Some(key) = models.keys().next().cloned() {
            models.remove(&key);
            warn!(model_name = key, "Evicted TTS model from cache");
        }
    }

    /// Evict least recently used STT model
    async fn evict_least_used_stt_model(&self, models: &mut HashMap<String, Arc<dyn SttModel>>) {
        // In a real implementation, this would track usage timestamps
        // For now, just remove the first model
        if let Some(key) = models.keys().next().cloned() {
            models.remove(&key);
            warn!(model_name = key, "Evicted STT model from cache");
        }
    }

    /// Get count of loaded models
    pub async fn loaded_models_count(&self) -> u32 {
        let tts_count = self.tts_models.read().await.len();
        let stt_count = self.stt_models.read().await.len();
        (tts_count + stt_count) as u32
    }

    /// Health check for all loaded models
    pub async fn health_check(&self) -> Result<bool> {
        // Check TTS models
        let tts_models = self.tts_models.read().await;
        for (name, model) in tts_models.iter() {
            if !model.health_check().await {
                warn!(model_name = name, "TTS model health check failed");
                return Ok(false);
            }
        }

        // Check STT models
        let stt_models = self.stt_models.read().await;
        for (name, model) in stt_models.iter() {
            if !model.health_check().await {
                warn!(model_name = name, "STT model health check failed");
                return Ok(false);
            }
        }

        Ok(true)
    }

    /// Get metadata for all loaded models
    pub async fn get_loaded_models_metadata(&self) -> Vec<ModelMetadata> {
        let mut metadata = Vec::new();

        // Get TTS model metadata
        let tts_models = self.tts_models.read().await;
        for (name, model) in tts_models.iter() {
            if let Ok(model_metadata) = model.metadata().await {
                metadata.push(model_metadata);
            }
        }

        // Get STT model metadata
        let stt_models = self.stt_models.read().await;
        for (name, model) in stt_models.iter() {
            if let Ok(model_metadata) = model.metadata().await {
                metadata.push(model_metadata);
            }
        }

        metadata
    }

    /// Unload a specific model
    pub async fn unload_model(&self, model_name: &str, model_type: ModelType) -> Result<()> {
        match model_type {
            ModelType::Tts => {
                let mut models = self.tts_models.write().await;
                if models.remove(model_name).is_some() {
                    info!(model_name = model_name, "TTS model unloaded");
                }
            }
            ModelType::Stt => {
                let mut models = self.stt_models.write().await;
                if models.remove(model_name).is_some() {
                    info!(model_name = model_name, "STT model unloaded");
                }
            }
        }
        Ok(())
    }

    /// Clear all loaded models
    pub async fn clear_all_models(&self) -> Result<()> {
        {
            let mut tts_models = self.tts_models.write().await;
            tts_models.clear();
        }
        {
            let mut stt_models = self.stt_models.write().await;
            stt_models.clear();
        }
        info!("All models cleared from cache");
        Ok(())
    }
}

impl Default for ModelCapabilities {
    fn default() -> Self {
        Self {
            realtime: false,
            voice_cloning: false,
            emotion_control: false,
            multi_speaker: false,
            max_audio_length: 300.0,
            sample_rates: vec![16000, 22050, 44100],
        }
    }
}

impl Default for TtsSynthesisParameters {
    fn default() -> Self {
        Self {
            speaking_rate: 1.0,
            pitch: 0.0,
            volume: 1.0,
            emotion: None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Config;

    #[tokio::test]
    async fn test_model_manager_creation() {
        let config = Arc::new(Config::default());
        let manager = ModelManager::new(config).await;
        assert!(manager.is_ok());
    }

    #[tokio::test]
    async fn test_model_metadata() {
        let metadata = ModelMetadata {
            name: "test-model".to_string(),
            version: "1.0.0".to_string(),
            model_type: ModelType::Tts,
            size_bytes: 1024 * 1024,
            languages: vec!["en-US".to_string()],
            capabilities: ModelCapabilities::default(),
            loaded_at: Some(chrono::Utc::now()),
            last_used: None,
        };

        assert_eq!(metadata.name, "test-model");
        assert_eq!(metadata.model_type, ModelType::Tts);
    }
}
