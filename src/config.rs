//! Enterprise configuration management for Chatterbox TTS/STT system
//!
//! This module provides:
//! - Environment-based configuration with validation
//! - Security-aware configuration handling
//! - Hot-reload capabilities for non-security-critical settings
//! - Configuration schema validation

use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tracing::{info, warn};

use crate::{ChatterboxError, Result};

/// Main configuration structure for the Chatterbox system
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Config {
    /// Server configuration
    pub server: ServerConfig,
    /// Audio processing configuration
    pub audio: AudioConfig,
    /// Model configuration
    pub models: ModelConfig,
    /// Security configuration
    pub security: SecurityConfig,
    /// Database configuration
    pub database: DatabaseConfig,
    /// Telemetry configuration
    pub telemetry: TelemetryConfig,
    /// Feature flags
    pub features: FeatureFlags,
}

/// Server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    /// Server host address
    #[serde(default = "default_host")]
    pub host: String,
    /// Server port
    #[serde(default = "default_port")]
    pub port: u16,
    /// Maximum request size in bytes
    #[serde(default = "default_max_request_size")]
    pub max_request_size: usize,
    /// Request timeout in seconds
    #[serde(default = "default_request_timeout")]
    pub request_timeout: u64,
    /// Enable CORS
    #[serde(default = "default_cors_enabled")]
    pub cors_enabled: bool,
    /// Allowed origins for CORS
    #[serde(default)]
    pub cors_origins: Vec<String>,
}

/// Audio processing configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AudioConfig {
    /// Default sample rate
    #[serde(default = "default_sample_rate")]
    pub sample_rate: u32,
    /// Audio buffer size
    #[serde(default = "default_buffer_size")]
    pub buffer_size: usize,
    /// Maximum audio duration in seconds
    #[serde(default = "default_max_duration")]
    pub max_duration: f32,
    /// Supported audio formats
    #[serde(default = "default_audio_formats")]
    pub supported_formats: Vec<AudioFormat>,
    /// Enable real-time processing
    #[serde(default = "default_realtime_enabled")]
    pub realtime_enabled: bool,
}

/// Model configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelConfig {
    /// TTS model configuration
    pub tts: TtsModelConfig,
    /// STT model configuration
    pub stt: SttModelConfig,
    /// Model cache directory
    #[serde(default = "default_model_cache_dir")]
    pub cache_dir: PathBuf,
    /// Maximum models in memory
    #[serde(default = "default_max_models_in_memory")]
    pub max_models_in_memory: usize,
    /// Model loading timeout in seconds
    #[serde(default = "default_model_timeout")]
    pub model_timeout: u64,
}

/// TTS model configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TtsModelConfig {
    /// Default voice model
    #[serde(default = "default_tts_model")]
    pub default_model: String,
    /// Available voice models
    #[serde(default)]
    pub available_models: Vec<String>,
    /// Maximum synthesis length in characters
    #[serde(default = "default_max_synthesis_length")]
    pub max_synthesis_length: usize,
    /// Enable voice cloning
    #[serde(default)]
    pub voice_cloning_enabled: bool,
}

/// STT model configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SttModelConfig {
    /// Default recognition model
    #[serde(default = "default_stt_model")]
    pub default_model: String,
    /// Available recognition models
    #[serde(default)]
    pub available_models: Vec<String>,
    /// Maximum audio length for recognition in seconds
    #[serde(default = "default_max_recognition_length")]
    pub max_recognition_length: f32,
    /// Enable real-time recognition
    #[serde(default)]
    pub realtime_recognition: bool,
}

/// Security configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    /// Enable Perth watermarking
    #[serde(default = "default_watermarking_enabled")]
    pub watermarking_enabled: bool,
    /// Watermarking strength (0.0 to 1.0)
    #[serde(default = "default_watermarking_strength")]
    pub watermarking_strength: f32,
    /// API key for authentication
    #[serde(skip_serializing)]
    pub api_key: Option<String>,
    /// Rate limiting configuration
    pub rate_limiting: RateLimitConfig,
    /// Enable audit logging
    #[serde(default = "default_audit_logging")]
    pub audit_logging: bool,
}

/// Rate limiting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    /// Requests per minute per IP
    #[serde(default = "default_requests_per_minute")]
    pub requests_per_minute: u32,
    /// Burst size
    #[serde(default = "default_burst_size")]
    pub burst_size: u32,
    /// Enable rate limiting
    #[serde(default = "default_rate_limiting_enabled")]
    pub enabled: bool,
}

/// Database configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    /// Database URL
    #[serde(skip_serializing)]
    pub url: String,
    /// Maximum connections
    #[serde(default = "default_max_connections")]
    pub max_connections: u32,
    /// Connection timeout in seconds
    #[serde(default = "default_connection_timeout")]
    pub connection_timeout: u64,
    /// Enable connection pooling
    #[serde(default = "default_pooling_enabled")]
    pub pooling_enabled: bool,
}

/// Telemetry configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TelemetryConfig {
    /// Enable tracing
    #[serde(default = "default_tracing_enabled")]
    pub tracing_enabled: bool,
    /// Tracing level
    #[serde(default = "default_tracing_level")]
    pub tracing_level: String,
    /// Enable metrics
    #[serde(default = "default_metrics_enabled")]
    pub metrics_enabled: bool,
    /// Metrics endpoint
    #[serde(default = "default_metrics_endpoint")]
    pub metrics_endpoint: String,
    /// Jaeger endpoint for distributed tracing
    pub jaeger_endpoint: Option<String>,
}

/// Feature flags for runtime configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeatureFlags {
    /// Enable experimental features
    #[serde(default)]
    pub experimental_features: bool,
    /// Enable GPU acceleration
    #[serde(default = "default_gpu_enabled")]
    pub gpu_acceleration: bool,
    /// Enable WASM compilation
    #[serde(default)]
    pub wasm_enabled: bool,
    /// Enable formal verification
    #[serde(default)]
    pub verification_enabled: bool,
}

/// Supported audio formats
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum AudioFormat {
    /// WAV format
    Wav,
    /// MP3 format
    Mp3,
    /// FLAC format
    Flac,
    /// OGG format
    Ogg,
    /// Raw PCM
    Pcm,
}

impl Config {
    /// Load configuration from environment variables and config files
    pub fn from_env() -> Result<Self> {
        let mut settings = config::Config::builder();

        // Load from default config file if it exists
        if let Ok(config_path) = std::env::var("CHATTERBOX_CONFIG") {
            settings = settings.add_source(config::File::with_name(&config_path));
        } else {
            // Try default locations
            for path in &["./config.toml", "./config.yaml", "/etc/chatterbox/config.toml"] {
                if std::path::Path::new(path).exists() {
                    settings = settings.add_source(config::File::with_name(path));
                    info!("Loaded configuration from {}", path);
                    break;
                }
            }
        }

        // Override with environment variables
        settings = settings.add_source(
            config::Environment::with_prefix("CHATTERBOX")
                .separator("__")
                .try_parsing(true),
        );

        let config: Config = settings.build()?.try_deserialize()?;
        config.validate()?;

        info!("Configuration loaded successfully");
        Ok(config)
    }

    /// Validate configuration values
    pub fn validate(&self) -> Result<()> {
        // Validate server configuration
        if self.server.port == 0 {
            return Err(ChatterboxError::configuration(
                "Server port cannot be 0",
                "server.port",
            ));
        }

        // Validate audio configuration
        if self.audio.sample_rate < 8000 || self.audio.sample_rate > 48000 {
            return Err(ChatterboxError::configuration(
                "Sample rate must be between 8000 and 48000 Hz",
                "audio.sample_rate",
            ));
        }

        if self.audio.max_duration <= 0.0 || self.audio.max_duration > 3600.0 {
            return Err(ChatterboxError::configuration(
                "Max duration must be between 0 and 3600 seconds",
                "audio.max_duration",
            ));
        }

        // Validate security configuration
        if self.security.watermarking_enabled {
            if self.security.watermarking_strength < 0.0
                || self.security.watermarking_strength > 1.0
            {
                return Err(ChatterboxError::configuration(
                    "Watermarking strength must be between 0.0 and 1.0",
                    "security.watermarking_strength",
                ));
            }
        }

        // Validate model configuration
        if self.models.max_models_in_memory == 0 {
            warn!("max_models_in_memory is 0, models will be loaded on demand");
        }

        Ok(())
    }

    /// Get server bind address
    pub fn bind_address(&self) -> String {
        format!("{}:{}", self.server.host, self.server.port)
    }
}

impl Default for Config {
    fn default() -> Self {
        Self {
            server: ServerConfig::default(),
            audio: AudioConfig::default(),
            models: ModelConfig::default(),
            security: SecurityConfig::default(),
            database: DatabaseConfig::default(),
            telemetry: TelemetryConfig::default(),
            features: FeatureFlags::default(),
        }
    }
}

// Default value functions
fn default_host() -> String {
    "0.0.0.0".to_string()
}

fn default_port() -> u16 {
    8080
}

fn default_max_request_size() -> usize {
    100 * 1024 * 1024 // 100MB
}

fn default_request_timeout() -> u64 {
    30
}

fn default_cors_enabled() -> bool {
    true
}

fn default_sample_rate() -> u32 {
    22050
}

fn default_buffer_size() -> usize {
    1024
}

fn default_max_duration() -> f32 {
    300.0 // 5 minutes
}

fn default_audio_formats() -> Vec<AudioFormat> {
    vec![AudioFormat::Wav, AudioFormat::Mp3, AudioFormat::Flac]
}

fn default_realtime_enabled() -> bool {
    true
}

fn default_tts_model() -> String {
    "default-tts".to_string()
}

fn default_stt_model() -> String {
    "default-stt".to_string()
}

fn default_max_synthesis_length() -> usize {
    10000
}

fn default_max_recognition_length() -> f32 {
    300.0
}

fn default_model_cache_dir() -> PathBuf {
    PathBuf::from("./models")
}

fn default_max_models_in_memory() -> usize {
    3
}

fn default_model_timeout() -> u64 {
    60
}

fn default_watermarking_enabled() -> bool {
    true
}

fn default_watermarking_strength() -> f32 {
    0.5
}

fn default_requests_per_minute() -> u32 {
    60
}

fn default_burst_size() -> u32 {
    10
}

fn default_rate_limiting_enabled() -> bool {
    true
}

fn default_audit_logging() -> bool {
    true
}

fn default_max_connections() -> u32 {
    10
}

fn default_connection_timeout() -> u64 {
    30
}

fn default_pooling_enabled() -> bool {
    true
}

fn default_tracing_enabled() -> bool {
    true
}

fn default_tracing_level() -> String {
    "info".to_string()
}

fn default_metrics_enabled() -> bool {
    true
}

fn default_metrics_endpoint() -> String {
    "/metrics".to_string()
}

fn default_gpu_enabled() -> bool {
    false
}

// Implement Default for all config structs
impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            host: default_host(),
            port: default_port(),
            max_request_size: default_max_request_size(),
            request_timeout: default_request_timeout(),
            cors_enabled: default_cors_enabled(),
            cors_origins: vec![],
        }
    }
}

impl Default for AudioConfig {
    fn default() -> Self {
        Self {
            sample_rate: default_sample_rate(),
            buffer_size: default_buffer_size(),
            max_duration: default_max_duration(),
            supported_formats: default_audio_formats(),
            realtime_enabled: default_realtime_enabled(),
        }
    }
}

impl Default for ModelConfig {
    fn default() -> Self {
        Self {
            tts: TtsModelConfig::default(),
            stt: SttModelConfig::default(),
            cache_dir: default_model_cache_dir(),
            max_models_in_memory: default_max_models_in_memory(),
            model_timeout: default_model_timeout(),
        }
    }
}

impl Default for TtsModelConfig {
    fn default() -> Self {
        Self {
            default_model: default_tts_model(),
            available_models: vec![],
            max_synthesis_length: default_max_synthesis_length(),
            voice_cloning_enabled: false,
        }
    }
}

impl Default for SttModelConfig {
    fn default() -> Self {
        Self {
            default_model: default_stt_model(),
            available_models: vec![],
            max_recognition_length: default_max_recognition_length(),
            realtime_recognition: false,
        }
    }
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            watermarking_enabled: default_watermarking_enabled(),
            watermarking_strength: default_watermarking_strength(),
            api_key: None,
            rate_limiting: RateLimitConfig::default(),
            audit_logging: default_audit_logging(),
        }
    }
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        Self {
            requests_per_minute: default_requests_per_minute(),
            burst_size: default_burst_size(),
            enabled: default_rate_limiting_enabled(),
        }
    }
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            url: "sqlite://./chatterbox.db".to_string(),
            max_connections: default_max_connections(),
            connection_timeout: default_connection_timeout(),
            pooling_enabled: default_pooling_enabled(),
        }
    }
}

impl Default for TelemetryConfig {
    fn default() -> Self {
        Self {
            tracing_enabled: default_tracing_enabled(),
            tracing_level: default_tracing_level(),
            metrics_enabled: default_metrics_enabled(),
            metrics_endpoint: default_metrics_endpoint(),
            jaeger_endpoint: None,
        }
    }
}

impl Default for FeatureFlags {
    fn default() -> Self {
        Self {
            experimental_features: false,
            gpu_acceleration: default_gpu_enabled(),
            wasm_enabled: cfg!(feature = "wasm"),
            verification_enabled: cfg!(feature = "verification"),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_default_config() {
        let config = Config::default();
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_config_validation() {
        let mut config = Config::default();
        
        // Test invalid sample rate
        config.audio.sample_rate = 1000;
        assert!(config.validate().is_err());
        
        // Test invalid watermarking strength
        config.audio.sample_rate = 22050;
        config.security.watermarking_strength = 2.0;
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_bind_address() {
        let config = Config::default();
        assert_eq!(config.bind_address(), "0.0.0.0:8080");
    }
}
