# Cargo.toml - Enterprise Chatterbox TTS/STT Rust Backend
# MCStack v9r0 Compliant Configuration

[package]
name = "chatterbox-enterprise"
version = "1.0.0"
edition = "2021"
authors = ["Enterprise Engineering Team"]
license = "MIT"
description = "Enterprise-grade TTS/STT system with formal verification and WASM support"
homepage = "https://github.com/enterprise/chatterbox-rust"
repository = "https://github.com/enterprise/chatterbox-rust"
documentation = "https://docs.rs/chatterbox-enterprise"
readme = "README.md"
keywords = ["tts", "stt", "speech", "ai", "wasm", "enterprise"]
categories = ["multimedia::audio", "science", "web-programming::wasm"]

[lib]
crate-type = ["cdylib", "rlib"]

# MCStack v9r0 Security & Safety Features
[dependencies]
# Core neural network inference
candle-core = { version = "0.6.0", features = ["cuda", "metal"] }
candle-nn = "0.6.0"
candle-transformers = "0.6.0"

# Audio processing with SIMD optimization
rustfft = "6.2"
apodize = "1.0"
realfft = "3.3"
cpal = "0.15"
hound = "3.5"

# Async runtime for real-time processing
tokio = { version = "1.40", features = ["macros", "rt", "rt-multi-thread", "time", "fs", "net"] }
tokio-stream = "0.1"
futures = "0.3"

# Web server and API
axum = { version = "0.7", features = ["ws", "multipart"] }
tower = { version = "0.4", features = ["full"] }
tower-http = { version = "0.5", features = ["cors", "trace"] }
hyper = "1.0"

# Serialization and data handling
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
bincode = "1.3"

# Cryptography for watermarking (Perth implementation)
aes-gcm = "0.10"
rand = "0.8"
sha3 = "0.10"
ed25519-dalek = "2.1"
zeroize = { version = "1.8", features = ["derive"] }

# Memory safety and bounds checking
bounded-vec = "0.7"
smallvec = "1.13"

# Error handling
thiserror = "1.0"
anyhow = "1.0"

# Configuration management
config = "0.14"
dotenvy = "0.15"

# Logging and telemetry (MCStack compliance)
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
metrics = "0.20"
opentelemetry = { version = "0.19", features = ["rt-tokio"] }
opentelemetry-jaeger = "0.18"

# Database and caching
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono"], optional = true }
redis = { version = "0.24", features = ["tokio-comp"], optional = true }

# UUID and time handling
uuid = { version = "1.6", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# Additional dependencies
base64 = "0.21"
scopeguard = "1.2"
dirs = "5.0"

# Optional development dependencies
criterion = { version = "0.5", features = ["html_reports"], optional = true }

# WASM-specific dependencies
[target.'cfg(target_arch = "wasm32")'.dependencies]
wasm-bindgen = "0.2"
wasm-bindgen-futures = "0.4"
js-sys = "0.3"
web-sys = { version = "0.3", features = [
    "console",
    "AudioContext",
    "AudioBuffer",
    "AudioDestinationNode",
    "Performance",
    "Window",
    "MediaStream",
    "MediaRecorder",
]}
getrandom = { version = "0.2", features = ["js"] }

# Development and testing dependencies
[dev-dependencies]
tokio-test = "0.4"
proptest = "1.4"
quickcheck = "1.0"
quickcheck_macros = "1.0"
wiremock = "0.6"

# Build dependencies
[build-dependencies]
chrono = "0.4"

# Formal verification (optional)
[dependencies.prusti-contracts]
version = "0.2.0"
optional = true

[dependencies.creusot-contracts]
version = "0.2.0"
optional = true

# Feature flags for different build configurations
[features]
default = ["audio-processing", "watermarking", "web-server"]

# Core features
audio-processing = []
neural-models = []
watermarking = []
web-server = []
database = []

# Formal verification
verification = []
verification-prusti = []
verification-creusot = []

# Platform-specific optimizations
simd = []
gpu-cuda = []
gpu-metal = []

# WASM build
wasm = []
wasm-simd = []

# Security and compliance
security-hardened = []
mcstack-compliance = []

# Development and debugging
dev-tools = ["criterion"]
profiling = []

# Build optimization profiles
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true

# WASM-optimized release build
[profile.release-wasm]
inherits = "release"
opt-level = "s"  # Optimize for size
lto = "fat"

# Development profile with verification
[profile.dev-verified]
inherits = "dev"
debug = true
overflow-checks = true
debug-assertions = true

# MCStack v9r0 Compliance Metadata
[package.metadata.mcstack]
version = "v9r0_enhanced"
governance_level = "GAL-3"
safety_critical = true
formal_verification = true
security_level = "high"
compliance_frameworks = ["NIST-AI-RMF", "EU-AI-Act", "ISO-27001"]

[package.metadata.mcstack.security]
supply_chain_verification = true
sbom_generation = true
cryptographic_signing = true
watermarking = "perth"

[package.metadata.mcstack.verification]
formal_methods = ["prusti", "creusot"]
property_testing = true
memory_safety = true
real_time_guarantees = true

[package.metadata.mcstack.governance]
audit_trail = true
compliance_monitoring = true
automated_testing = true
documentation_required = true

# Note: Workspace configuration removed for single-crate setup
# In a full enterprise deployment, this would be a multi-crate workspace

# Linting and code quality
[lints.rust]
unsafe_code = "forbid"
missing_docs = "warn"
rust_2018_idioms = "warn"
trivial_casts = "warn"
trivial_numeric_casts = "warn"
unused_import_braces = "warn"
unused_qualifications = "warn"

[lints.clippy]
all = "warn"
pedantic = "warn"
nursery = "warn"
cargo = "warn"
# Allow some clippy lints that are too strict for ML code
float_cmp = "allow"
cast_precision_loss = "allow"
cast_sign_loss = "allow"
cast_possible_truncation = "allow"

# Note: Benchmarks removed for single-crate setup
# In a full enterprise deployment, benchmarks would be in separate files

# Note: Examples removed for single-crate setup
# In a full enterprise deployment, examples would be in separate crates

[[bin]]
name = "chatterbox-server"
path = "src/bin/server.rs"
required-features = ["web-server"]
