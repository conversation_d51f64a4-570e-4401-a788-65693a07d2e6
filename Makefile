# Chatterbox Enterprise - Comprehensive Makefile
# Professional TTS/STT System Build and Deployment Automation

# Project Configuration
PROJECT_NAME := chatterbox-enterprise
VERSION := 1.0.0
RUST_VERSION := 1.70
DOCKER_REGISTRY := ghcr.io/chatterbox
KUBERNETES_NAMESPACE := chatterbox

# Build Configuration
CARGO := cargo
DOCKER := docker
KUBECTL := kubectl
HELM := helm

# Directories
BUILD_DIR := target
RELEASE_DIR := $(BUILD_DIR)/release
CONFIG_DIR := config
SCRIPTS_DIR := scripts
K8S_DIR := k8s
MONITORING_DIR := monitoring
DOCS_DIR := docs
TEMPLATES_DIR := templates
WEB_DIR := web

# Binary Names
SERVER_BIN := chatterbox-server
CLI_BIN := chatterbox-cli
TUI_BIN := chatterbox-tui
WEBUI_BIN := chatterbox-webui

# Docker Images
SERVER_IMAGE := $(DOCKER_REGISTRY)/$(PROJECT_NAME)-server:$(VERSION)
CLI_IMAGE := $(DOCKER_REGISTRY)/$(PROJECT_NAME)-cli:$(VERSION)
TUI_IMAGE := $(DOCKER_REGISTRY)/$(PROJECT_NAME)-tui:$(VERSION)
WEBUI_IMAGE := $(DOCKER_REGISTRY)/$(PROJECT_NAME)-webui:$(VERSION)

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[0;33m
BLUE := \033[0;34m
PURPLE := \033[0;35m
CYAN := \033[0;36m
WHITE := \033[0;37m
NC := \033[0m # No Color

# Default target
.DEFAULT_GOAL := help

# Help target
.PHONY: help
help: ## Show this help message
	@echo "$(CYAN)🎧 Chatterbox Enterprise - Build System$(NC)"
	@echo "$(CYAN)=====================================$(NC)"
	@echo ""
	@echo "$(GREEN)Available targets:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(BLUE)Examples:$(NC)"
	@echo "  make build-all          # Build all binaries"
	@echo "  make docker-build-all   # Build all Docker images"
	@echo "  make deploy-k8s         # Deploy to Kubernetes"
	@echo "  make test-all           # Run all tests"

# =============================================================================
# BUILD TARGETS
# =============================================================================

.PHONY: check
check: ## Check code formatting and linting
	@echo "$(BLUE)🔍 Checking code formatting and linting...$(NC)"
	$(CARGO) fmt --check
	$(CARGO) clippy --all-targets --all-features -- -D warnings

.PHONY: format
format: ## Format code
	@echo "$(BLUE)🎨 Formatting code...$(NC)"
	$(CARGO) fmt

.PHONY: build
build: ## Build all binaries in debug mode
	@echo "$(BLUE)🔨 Building debug binaries...$(NC)"
	$(CARGO) build

.PHONY: build-release
build-release: ## Build all binaries in release mode
	@echo "$(BLUE)🚀 Building release binaries...$(NC)"
	$(CARGO) build --release

.PHONY: build-server
build-server: ## Build server binary
	@echo "$(BLUE)🖥️  Building server binary...$(NC)"
	$(CARGO) build --release --bin $(SERVER_BIN)

.PHONY: build-cli
build-cli: ## Build CLI binary
	@echo "$(BLUE)⌨️  Building CLI binary...$(NC)"
	$(CARGO) build --release --bin $(CLI_BIN)

.PHONY: build-tui
build-tui: ## Build TUI binary
	@echo "$(BLUE)📺 Building TUI binary...$(NC)"
	$(CARGO) build --release --bin $(TUI_BIN)

.PHONY: build-webui
build-webui: ## Build Web UI binary
	@echo "$(BLUE)🌐 Building Web UI binary...$(NC)"
	$(CARGO) build --release --bin $(WEBUI_BIN)

.PHONY: build-all
build-all: build-server build-cli build-tui build-webui ## Build all binaries
	@echo "$(GREEN)✅ All binaries built successfully!$(NC)"

# =============================================================================
# TEST TARGETS
# =============================================================================

.PHONY: test
test: ## Run unit tests
	@echo "$(BLUE)🧪 Running unit tests...$(NC)"
	$(CARGO) test --lib

.PHONY: test-integration
test-integration: ## Run integration tests
	@echo "$(BLUE)🔗 Running integration tests...$(NC)"
	$(CARGO) test --test '*'

.PHONY: test-all
test-all: test test-integration ## Run all tests
	@echo "$(GREEN)✅ All tests completed!$(NC)"

.PHONY: test-coverage
test-coverage: ## Generate test coverage report
	@echo "$(BLUE)📊 Generating test coverage...$(NC)"
	$(CARGO) tarpaulin --out Html --output-dir coverage

.PHONY: benchmark
benchmark: ## Run performance benchmarks
	@echo "$(BLUE)⚡ Running benchmarks...$(NC)"
	$(CARGO) bench

# =============================================================================
# DOCKER TARGETS
# =============================================================================

.PHONY: docker-build-server
docker-build-server: ## Build server Docker image
	@echo "$(BLUE)🐳 Building server Docker image...$(NC)"
	$(DOCKER) build -t $(SERVER_IMAGE) --target server .

.PHONY: docker-build-cli
docker-build-cli: ## Build CLI Docker image
	@echo "$(BLUE)🐳 Building CLI Docker image...$(NC)"
	$(DOCKER) build -t $(CLI_IMAGE) --target cli .

.PHONY: docker-build-tui
docker-build-tui: ## Build TUI Docker image
	@echo "$(BLUE)🐳 Building TUI Docker image...$(NC)"
	$(DOCKER) build -t $(TUI_IMAGE) --target tui .

.PHONY: docker-build-webui
docker-build-webui: ## Build Web UI Docker image
	@echo "$(BLUE)🐳 Building Web UI Docker image...$(NC)"
	$(DOCKER) build -t $(WEBUI_IMAGE) --target webui .

.PHONY: docker-build-all
docker-build-all: docker-build-server docker-build-cli docker-build-tui docker-build-webui ## Build all Docker images
	@echo "$(GREEN)✅ All Docker images built successfully!$(NC)"

.PHONY: docker-push-all
docker-push-all: docker-build-all ## Push all Docker images to registry
	@echo "$(BLUE)📤 Pushing Docker images to registry...$(NC)"
	$(DOCKER) push $(SERVER_IMAGE)
	$(DOCKER) push $(CLI_IMAGE)
	$(DOCKER) push $(TUI_IMAGE)
	$(DOCKER) push $(WEBUI_IMAGE)
	@echo "$(GREEN)✅ All images pushed successfully!$(NC)"

.PHONY: docker-compose-up
docker-compose-up: ## Start services with Docker Compose
	@echo "$(BLUE)🚀 Starting services with Docker Compose...$(NC)"
	docker-compose up -d
	@echo "$(GREEN)✅ Services started! Check http://localhost:8080$(NC)"

.PHONY: docker-compose-down
docker-compose-down: ## Stop Docker Compose services
	@echo "$(BLUE)🛑 Stopping Docker Compose services...$(NC)"
	docker-compose down

.PHONY: docker-compose-logs
docker-compose-logs: ## Show Docker Compose logs
	@echo "$(BLUE)📋 Showing Docker Compose logs...$(NC)"
	docker-compose logs -f

# =============================================================================
# KUBERNETES TARGETS
# =============================================================================

.PHONY: k8s-namespace
k8s-namespace: ## Create Kubernetes namespace
	@echo "$(BLUE)🏗️  Creating Kubernetes namespace...$(NC)"
	$(KUBECTL) create namespace $(KUBERNETES_NAMESPACE) --dry-run=client -o yaml | $(KUBECTL) apply -f -

.PHONY: k8s-secrets
k8s-secrets: ## Create Kubernetes secrets
	@echo "$(BLUE)🔐 Creating Kubernetes secrets...$(NC)"
	$(KUBECTL) create secret generic chatterbox-config \
		--from-file=$(CONFIG_DIR)/config.toml \
		--namespace=$(KUBERNETES_NAMESPACE) \
		--dry-run=client -o yaml | $(KUBECTL) apply -f -

.PHONY: deploy-k8s
deploy-k8s: k8s-namespace k8s-secrets ## Deploy to Kubernetes
	@echo "$(BLUE)🚀 Deploying to Kubernetes...$(NC)"
	$(KUBECTL) apply -f $(K8S_DIR)/ --namespace=$(KUBERNETES_NAMESPACE)
	@echo "$(GREEN)✅ Deployed to Kubernetes!$(NC)"

.PHONY: k8s-status
k8s-status: ## Check Kubernetes deployment status
	@echo "$(BLUE)📊 Checking Kubernetes status...$(NC)"
	$(KUBECTL) get all --namespace=$(KUBERNETES_NAMESPACE)

.PHONY: k8s-logs
k8s-logs: ## Show Kubernetes logs
	@echo "$(BLUE)📋 Showing Kubernetes logs...$(NC)"
	$(KUBECTL) logs -f deployment/chatterbox-server --namespace=$(KUBERNETES_NAMESPACE)

.PHONY: k8s-delete
k8s-delete: ## Delete Kubernetes deployment
	@echo "$(BLUE)🗑️  Deleting Kubernetes deployment...$(NC)"
	$(KUBECTL) delete -f $(K8S_DIR)/ --namespace=$(KUBERNETES_NAMESPACE)

# =============================================================================
# DEVELOPMENT TARGETS
# =============================================================================

.PHONY: dev-server
dev-server: ## Run development server
	@echo "$(BLUE)🔧 Starting development server...$(NC)"
	$(CARGO) run --bin $(SERVER_BIN)

.PHONY: dev-cli
dev-cli: ## Run CLI in development mode
	@echo "$(BLUE)⌨️  Running CLI...$(NC)"
	$(CARGO) run --bin $(CLI_BIN) -- --help

.PHONY: dev-tui
dev-tui: ## Run TUI in development mode
	@echo "$(BLUE)📺 Running TUI...$(NC)"
	$(CARGO) run --bin $(TUI_BIN)

.PHONY: dev-webui
dev-webui: ## Run Web UI in development mode
	@echo "$(BLUE)🌐 Running Web UI...$(NC)"
	$(CARGO) run --bin $(WEBUI_BIN)

.PHONY: watch
watch: ## Watch for changes and rebuild
	@echo "$(BLUE)👀 Watching for changes...$(NC)"
	$(CARGO) watch -x build

.PHONY: watch-test
watch-test: ## Watch for changes and run tests
	@echo "$(BLUE)👀 Watching for changes and running tests...$(NC)"
	$(CARGO) watch -x test

# =============================================================================
# UTILITY TARGETS
# =============================================================================

.PHONY: clean
clean: ## Clean build artifacts
	@echo "$(BLUE)🧹 Cleaning build artifacts...$(NC)"
	$(CARGO) clean
	$(DOCKER) system prune -f

.PHONY: deps
deps: ## Install dependencies
	@echo "$(BLUE)📦 Installing dependencies...$(NC)"
	rustup update
	$(CARGO) update

.PHONY: security-audit
security-audit: ## Run security audit
	@echo "$(BLUE)🔒 Running security audit...$(NC)"
	$(CARGO) audit

.PHONY: docs
docs: ## Generate documentation
	@echo "$(BLUE)📚 Generating documentation...$(NC)"
	$(CARGO) doc --no-deps --open

.PHONY: install
install: build-all ## Install binaries to system
	@echo "$(BLUE)📥 Installing binaries...$(NC)"
	$(CARGO) install --path . --bin $(SERVER_BIN)
	$(CARGO) install --path . --bin $(CLI_BIN)
	$(CARGO) install --path . --bin $(TUI_BIN)
	$(CARGO) install --path . --bin $(WEBUI_BIN)

.PHONY: uninstall
uninstall: ## Uninstall binaries from system
	@echo "$(BLUE)📤 Uninstalling binaries...$(NC)"
	$(CARGO) uninstall $(SERVER_BIN) $(CLI_BIN) $(TUI_BIN) $(WEBUI_BIN)

# =============================================================================
# RELEASE TARGETS
# =============================================================================

.PHONY: release-prepare
release-prepare: clean test-all build-all ## Prepare for release
	@echo "$(BLUE)🎁 Preparing release...$(NC)"
	@echo "$(GREEN)✅ Release preparation complete!$(NC)"

.PHONY: release-docker
release-docker: docker-build-all docker-push-all ## Build and push release Docker images
	@echo "$(GREEN)✅ Docker release complete!$(NC)"

.PHONY: release-k8s
release-k8s: release-docker deploy-k8s ## Full Kubernetes release
	@echo "$(GREEN)✅ Kubernetes release complete!$(NC)"

.PHONY: release-all
release-all: release-prepare release-docker release-k8s ## Complete release process
	@echo "$(GREEN)🎉 Release $(VERSION) completed successfully!$(NC)"

# =============================================================================
# MONITORING TARGETS
# =============================================================================

.PHONY: monitoring-up
monitoring-up: ## Start monitoring stack
	@echo "$(BLUE)📊 Starting monitoring stack...$(NC)"
	$(KUBECTL) apply -f $(MONITORING_DIR)/ --namespace=$(KUBERNETES_NAMESPACE)

.PHONY: monitoring-down
monitoring-down: ## Stop monitoring stack
	@echo "$(BLUE)📊 Stopping monitoring stack...$(NC)"
	$(KUBECTL) delete -f $(MONITORING_DIR)/ --namespace=$(KUBERNETES_NAMESPACE)

# =============================================================================
# VALIDATION TARGETS
# =============================================================================

.PHONY: validate-config
validate-config: ## Validate configuration files
	@echo "$(BLUE)✅ Validating configuration...$(NC)"
	@if [ -f $(CONFIG_DIR)/config.toml ]; then \
		echo "$(GREEN)✅ Configuration file found$(NC)"; \
	else \
		echo "$(RED)❌ Configuration file missing$(NC)"; \
		exit 1; \
	fi

.PHONY: validate-k8s
validate-k8s: ## Validate Kubernetes manifests
	@echo "$(BLUE)✅ Validating Kubernetes manifests...$(NC)"
	$(KUBECTL) apply --dry-run=client -f $(K8S_DIR)/

.PHONY: validate-all
validate-all: validate-config validate-k8s ## Validate all configurations
	@echo "$(GREEN)✅ All validations passed!$(NC)"

# =============================================================================
# SPECIAL TARGETS
# =============================================================================

.PHONY: demo
demo: build-all ## Run demo of all interfaces
	@echo "$(CYAN)🎬 Starting Chatterbox Enterprise Demo...$(NC)"
	@echo "$(YELLOW)1. Starting server in background...$(NC)"
	@$(RELEASE_DIR)/$(SERVER_BIN) &
	@sleep 3
	@echo "$(YELLOW)2. Running CLI demo...$(NC)"
	@$(RELEASE_DIR)/$(CLI_BIN) status || true
	@echo "$(YELLOW)3. Web UI available at http://localhost:8080$(NC)"
	@echo "$(YELLOW)4. TUI available with: make dev-tui$(NC)"
	@echo "$(GREEN)🎉 Demo setup complete!$(NC)"

.PHONY: quick-start
quick-start: build-all docker-compose-up ## Quick start for development
	@echo "$(GREEN)🚀 Quick start complete!$(NC)"
	@echo "$(CYAN)Available services:$(NC)"
	@echo "  - API Server: http://localhost:8080"
	@echo "  - Web UI: http://localhost:3030"
	@echo "  - Grafana: http://localhost:3000 (admin/admin)"
	@echo "  - Prometheus: http://localhost:9090"

# Version information
.PHONY: version
version: ## Show version information
	@echo "$(CYAN)Chatterbox Enterprise $(VERSION)$(NC)"
	@echo "Rust version: $(shell rustc --version)"
	@echo "Cargo version: $(shell cargo --version)"
	@echo "Docker version: $(shell docker --version 2>/dev/null || echo 'Not installed')"
	@echo "Kubectl version: $(shell kubectl version --client --short 2>/dev/null || echo 'Not installed')"
