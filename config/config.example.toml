# Chatterbox Enterprise Configuration Example
# Copy this file to config.toml and customize for your environment

[server]
# Server binding configuration
host = "0.0.0.0"
port = 8080
max_request_size = 104857600  # 100MB
request_timeout = 30
cors_enabled = true

# TLS configuration (recommended for production)
# tls_cert_path = "/path/to/cert.pem"
# tls_key_path = "/path/to/key.pem"

[database]
# PostgreSQL connection for production
url = "postgresql://chatterbox:password@localhost:5432/chatterbox"
max_connections = 20
connection_timeout = 30
idle_timeout = 600

# SQLite for development/testing
# url = "sqlite:./chatterbox.db"

[redis]
# Redis for caching and rate limiting
url = "redis://localhost:6379"
max_connections = 10
connection_timeout = 5
key_prefix = "chatterbox:"

[audio]
# Audio processing configuration
sample_rate = 22050
buffer_size = 1024
max_duration = 300.0  # seconds
realtime_enabled = true
supported_formats = ["wav", "mp3", "flac", "ogg"]

# Audio quality settings
quality = "high"  # low, medium, high, ultra
compression_enabled = true

[security]
# Security and watermarking
watermarking_enabled = true
watermarking_strength = 0.5
audit_logging = true
api_key_required = true

# JWT configuration
jwt_secret = "your-super-secret-jwt-key-change-this-in-production"
jwt_expiration = 3600  # seconds

[security.rate_limiting]
enabled = true
requests_per_minute = 60
burst_size = 10
cleanup_interval = 300  # seconds

# Per-endpoint rate limits
[security.rate_limiting.endpoints]
"/api/v1/tts/synthesize" = 30
"/api/v1/stt/recognize" = 20
"/api/v1/models/load" = 5

[models]
# Model management
cache_dir = "./models"
max_models_in_memory = 3
model_timeout = 60
auto_download = true
model_server_url = "https://models.chatterbox.ai"

# GPU configuration
gpu_enabled = true
gpu_memory_fraction = 0.8
gpu_device_id = 0

[models.tts]
# Text-to-Speech models
default_model = "default-tts"
max_synthesis_length = 10000
voice_cloning_enabled = true
emotion_control_enabled = true

# Available TTS models
[[models.tts.available]]
name = "en-us-female-1"
path = "./models/tts/en-us-female-1.onnx"
language = "en-US"
gender = "female"
quality = "high"

[[models.tts.available]]
name = "en-us-male-1"
path = "./models/tts/en-us-male-1.onnx"
language = "en-US"
gender = "male"
quality = "high"

[[models.tts.available]]
name = "multilingual-neural"
path = "./models/tts/multilingual-neural.onnx"
languages = ["en-US", "es-ES", "fr-FR", "de-DE", "it-IT"]
quality = "ultra"

[models.stt]
# Speech-to-Text models
default_model = "default-stt"
max_recognition_length = 300.0
realtime_recognition = true
language_detection = true

# Available STT models
[[models.stt.available]]
name = "whisper-large-v3"
path = "./models/stt/whisper-large-v3.onnx"
languages = ["en", "es", "fr", "de", "it", "pt", "ru", "ja", "ko", "zh"]
quality = "ultra"

[[models.stt.available]]
name = "en-us-fast"
path = "./models/stt/en-us-fast.onnx"
language = "en-US"
quality = "medium"
realtime_optimized = true

[telemetry]
# Observability configuration
tracing_enabled = true
tracing_level = "info"
metrics_enabled = true
metrics_endpoint = "/metrics"

# OpenTelemetry configuration
otlp_endpoint = "http://localhost:4317"
service_name = "chatterbox-enterprise"
service_version = "1.0.0"

# Jaeger tracing
jaeger_endpoint = "http://localhost:14268/api/traces"

# Prometheus metrics
prometheus_enabled = true
prometheus_namespace = "chatterbox"

[telemetry.logging]
# Structured logging
format = "json"  # json, text
level = "info"
file_path = "./logs/chatterbox.log"
max_file_size = "100MB"
max_files = 10

[features]
# Feature flags
voice_cloning = true
emotion_synthesis = true
real_time_processing = true
batch_processing = true
model_fine_tuning = false
advanced_analytics = true

[performance]
# Performance tuning
worker_threads = 0  # 0 = auto-detect
max_blocking_threads = 512
thread_stack_size = 2097152  # 2MB

# Memory management
max_memory_usage = "8GB"
gc_threshold = "1GB"
cache_size = "2GB"

[development]
# Development-only settings
debug_mode = false
hot_reload = false
mock_models = false
test_data_path = "./test_data"

# API documentation
swagger_ui_enabled = true
api_docs_path = "/docs"

[enterprise]
# Enterprise features
license_key = "your-enterprise-license-key"
support_contact = "<EMAIL>"
deployment_id = "production-cluster-1"

# High availability
health_check_interval = 30
graceful_shutdown_timeout = 30
backup_enabled = true
backup_interval = 3600  # seconds

[enterprise.clustering]
# Multi-node deployment
enabled = false
node_id = "node-1"
cluster_discovery = "consul"  # consul, etcd, kubernetes
discovery_endpoint = "http://localhost:8500"

[enterprise.monitoring]
# Advanced monitoring
apm_enabled = true
error_tracking = true
performance_monitoring = true
user_analytics = false  # GDPR compliance

# Alerting
alert_webhook = "https://hooks.slack.com/your-webhook"
alert_email = "<EMAIL>"

[enterprise.compliance]
# Compliance and governance
gdpr_compliance = true
hipaa_compliance = false
sox_compliance = false
audit_retention_days = 365

# Data governance
data_encryption_at_rest = true
data_encryption_in_transit = true
pii_detection = true
data_anonymization = true
