# Chatterbox Enterprise TTS/STT Configuration
# MCStack v9r0 Compliant Configuration

[server]
host = "0.0.0.0"
port = 8080
max_request_size = 104857600  # 100MB
request_timeout = 30
cors_enabled = true
cors_origins = ["http://localhost:3000", "https://app.example.com"]

[audio]
sample_rate = 22050
buffer_size = 1024
max_duration = 300.0  # 5 minutes
supported_formats = ["Wav", "Mp3", "Flac"]
realtime_enabled = true

[models]
cache_dir = "./models"
max_models_in_memory = 3
model_timeout = 60

[models.tts]
default_model = "default-tts"
available_models = ["default-tts", "premium-tts"]
max_synthesis_length = 10000
voice_cloning_enabled = true

[models.stt]
default_model = "default-stt"
available_models = ["default-stt", "premium-stt"]
max_recognition_length = 300.0
realtime_recognition = true

[security]
watermarking_enabled = true
watermarking_strength = 0.5
# api_key = "your-secure-api-key-here"  # Uncomment and set for production
audit_logging = true

[security.rate_limiting]
enabled = true
requests_per_minute = 60
burst_size = 10

[database]
url = "sqlite://./chatterbox.db"
max_connections = 10
connection_timeout = 30
pooling_enabled = true

[telemetry]
tracing_enabled = true
tracing_level = "info"
metrics_enabled = true
metrics_endpoint = "/metrics"
# jaeger_endpoint = "http://localhost:14268/api/traces"  # Uncomment for distributed tracing

[features]
experimental_features = false
gpu_acceleration = false  # Set to true if CUDA/Metal available
wasm_enabled = true
verification_enabled = true
